{"version": 3, "sources": ["../src/HoverCard.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCard\n * -----------------------------------------------------------------------------------------------*/\n\nlet originalBodyUserSelect: string;\n\nconst HOVERCARD_NAME = 'HoverCard';\n\ntype ScopedProps<P> = P & { __scopeHoverCard?: Scope };\nconst [createHoverCardContext, createHoverCardScope] = createContextScope(HOVERCARD_NAME, [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype HoverCardContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpen(): void;\n  onClose(): void;\n  onDismiss(): void;\n  hasSelectionRef: React.MutableRefObject<boolean>;\n  isPointerDownOnContentRef: React.MutableRefObject<boolean>;\n};\n\nconst [HoverCardProvider, useHoverCardContext] =\n  createHoverCardContext<HoverCardContextValue>(HOVERCARD_NAME);\n\ninterface HoverCardProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  openDelay?: number;\n  closeDelay?: number;\n}\n\nconst HoverCard: React.FC<HoverCardProps> = (props: ScopedProps<HoverCardProps>) => {\n  const {\n    __scopeHoverCard,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    openDelay = 700,\n    closeDelay = 300,\n  } = props;\n  const popperScope = usePopperScope(__scopeHoverCard);\n  const openTimerRef = React.useRef(0);\n  const closeTimerRef = React.useRef(0);\n  const hasSelectionRef = React.useRef(false);\n  const isPointerDownOnContentRef = React.useRef(false);\n\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  const handleOpen = React.useCallback(() => {\n    clearTimeout(closeTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => setOpen(true), openDelay);\n  }, [openDelay, setOpen]);\n\n  const handleClose = React.useCallback(() => {\n    clearTimeout(openTimerRef.current);\n    if (!hasSelectionRef.current && !isPointerDownOnContentRef.current) {\n      closeTimerRef.current = window.setTimeout(() => setOpen(false), closeDelay);\n    }\n  }, [closeDelay, setOpen]);\n\n  const handleDismiss = React.useCallback(() => setOpen(false), [setOpen]);\n\n  // cleanup any queued state updates on unmount\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(openTimerRef.current);\n      clearTimeout(closeTimerRef.current);\n    };\n  }, []);\n\n  return (\n    <HoverCardProvider\n      scope={__scopeHoverCard}\n      open={open}\n      onOpenChange={setOpen}\n      onOpen={handleOpen}\n      onClose={handleClose}\n      onDismiss={handleDismiss}\n      hasSelectionRef={hasSelectionRef}\n      isPointerDownOnContentRef={isPointerDownOnContentRef}\n    >\n      <PopperPrimitive.Root {...popperScope}>{children}</PopperPrimitive.Root>\n    </HoverCardProvider>\n  );\n};\n\nHoverCard.displayName = HOVERCARD_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCardTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'HoverCardTrigger';\n\ntype HoverCardTriggerElement = React.ElementRef<typeof Primitive.a>;\ntype PrimitiveLinkProps = React.ComponentPropsWithoutRef<typeof Primitive.a>;\ninterface HoverCardTriggerProps extends PrimitiveLinkProps {}\n\nconst HoverCardTrigger = React.forwardRef<HoverCardTriggerElement, HoverCardTriggerProps>(\n  (props: ScopedProps<HoverCardTriggerProps>, forwardedRef) => {\n    const { __scopeHoverCard, ...triggerProps } = props;\n    const context = useHoverCardContext(TRIGGER_NAME, __scopeHoverCard);\n    const popperScope = usePopperScope(__scopeHoverCard);\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.a\n          data-state={context.open ? 'open' : 'closed'}\n          {...triggerProps}\n          ref={forwardedRef}\n          onPointerEnter={composeEventHandlers(props.onPointerEnter, excludeTouch(context.onOpen))}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, excludeTouch(context.onClose))}\n          onFocus={composeEventHandlers(props.onFocus, context.onOpen)}\n          onBlur={composeEventHandlers(props.onBlur, context.onClose)}\n          // prevent focus event on touch devices\n          onTouchStart={composeEventHandlers(props.onTouchStart, (event) => event.preventDefault())}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nHoverCardTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCardPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'HoverCardPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createHoverCardContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface HoverCardPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst HoverCardPortal: React.FC<HoverCardPortalProps> = (\n  props: ScopedProps<HoverCardPortalProps>\n) => {\n  const { __scopeHoverCard, forceMount, children, container } = props;\n  const context = useHoverCardContext(PORTAL_NAME, __scopeHoverCard);\n  return (\n    <PortalProvider scope={__scopeHoverCard} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nHoverCardPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCardContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'HoverCardContent';\n\ntype HoverCardContentElement = HoverCardContentImplElement;\ninterface HoverCardContentProps extends HoverCardContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst HoverCardContent = React.forwardRef<HoverCardContentElement, HoverCardContentProps>(\n  (props: ScopedProps<HoverCardContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeHoverCard);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useHoverCardContext(CONTENT_NAME, props.__scopeHoverCard);\n    return (\n      <Presence present={forceMount || context.open}>\n        <HoverCardContentImpl\n          data-state={context.open ? 'open' : 'closed'}\n          {...contentProps}\n          onPointerEnter={composeEventHandlers(props.onPointerEnter, excludeTouch(context.onOpen))}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, excludeTouch(context.onClose))}\n          ref={forwardedRef}\n        />\n      </Presence>\n    );\n  }\n);\n\nHoverCardContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype HoverCardContentImplElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface HoverCardContentImplProps extends Omit<PopperContentProps, 'onPlaced'> {\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `HoverCard`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  /**\n   * Event handler called when the focus moves outside of the `HoverCard`.\n   * Can be prevented.\n   */\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  /**\n   * Event handler called when an interaction happens outside the `HoverCard`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst HoverCardContentImpl = React.forwardRef<\n  HoverCardContentImplElement,\n  HoverCardContentImplProps\n>((props: ScopedProps<HoverCardContentImplProps>, forwardedRef) => {\n  const {\n    __scopeHoverCard,\n    onEscapeKeyDown,\n    onPointerDownOutside,\n    onFocusOutside,\n    onInteractOutside,\n    ...contentProps\n  } = props;\n  const context = useHoverCardContext(CONTENT_NAME, __scopeHoverCard);\n  const popperScope = usePopperScope(__scopeHoverCard);\n  const ref = React.useRef<HoverCardContentImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [containSelection, setContainSelection] = React.useState(false);\n\n  React.useEffect(() => {\n    if (containSelection) {\n      const body = document.body;\n\n      // Safari requires prefix\n      originalBodyUserSelect = body.style.userSelect || body.style.webkitUserSelect;\n\n      body.style.userSelect = 'none';\n      body.style.webkitUserSelect = 'none';\n      return () => {\n        body.style.userSelect = originalBodyUserSelect;\n        body.style.webkitUserSelect = originalBodyUserSelect;\n      };\n    }\n  }, [containSelection]);\n\n  React.useEffect(() => {\n    if (ref.current) {\n      const handlePointerUp = () => {\n        setContainSelection(false);\n        context.isPointerDownOnContentRef.current = false;\n\n        // Delay a frame to ensure we always access the latest selection\n        setTimeout(() => {\n          const hasSelection = document.getSelection()?.toString() !== '';\n          if (hasSelection) context.hasSelectionRef.current = true;\n        });\n      };\n\n      document.addEventListener('pointerup', handlePointerUp);\n      return () => {\n        document.removeEventListener('pointerup', handlePointerUp);\n        context.hasSelectionRef.current = false;\n        context.isPointerDownOnContentRef.current = false;\n      };\n    }\n  }, [context.isPointerDownOnContentRef, context.hasSelectionRef]);\n\n  React.useEffect(() => {\n    if (ref.current) {\n      const tabbables = getTabbableNodes(ref.current);\n      tabbables.forEach((tabbable) => tabbable.setAttribute('tabindex', '-1'));\n    }\n  });\n\n  return (\n    <DismissableLayer\n      asChild\n      disableOutsidePointerEvents={false}\n      onInteractOutside={onInteractOutside}\n      onEscapeKeyDown={onEscapeKeyDown}\n      onPointerDownOutside={onPointerDownOutside}\n      onFocusOutside={composeEventHandlers(onFocusOutside, (event) => {\n        event.preventDefault();\n      })}\n      onDismiss={context.onDismiss}\n    >\n      <PopperPrimitive.Content\n        {...popperScope}\n        {...contentProps}\n        onPointerDown={composeEventHandlers(contentProps.onPointerDown, (event) => {\n          // Contain selection to current layer\n          if (event.currentTarget.contains(event.target as HTMLElement)) {\n            setContainSelection(true);\n          }\n          context.hasSelectionRef.current = false;\n          context.isPointerDownOnContentRef.current = true;\n        })}\n        ref={composedRefs}\n        style={{\n          ...contentProps.style,\n          userSelect: containSelection ? 'text' : undefined,\n          // Safari requires prefix\n          WebkitUserSelect: containSelection ? 'text' : undefined,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-hover-card-content-transform-origin': 'var(--radix-popper-transform-origin)',\n            '--radix-hover-card-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-hover-card-content-available-height': 'var(--radix-popper-available-height)',\n            '--radix-hover-card-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-hover-card-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    </DismissableLayer>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCardArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'HoverCardArrow';\n\ntype HoverCardArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface HoverCardArrowProps extends PopperArrowProps {}\n\nconst HoverCardArrow = React.forwardRef<HoverCardArrowElement, HoverCardArrowProps>(\n  (props: ScopedProps<HoverCardArrowProps>, forwardedRef) => {\n    const { __scopeHoverCard, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeHoverCard);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nHoverCardArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction excludeTouch<E>(eventHandler: () => void) {\n  return (event: React.PointerEvent<E>) =>\n    event.pointerType === 'touch' ? undefined : eventHandler();\n}\n\n/**\n * Returns a list of nodes that can be in the tab sequence.\n * @see: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n */\nfunction getTabbableNodes(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  return nodes;\n}\n\nconst Root = HoverCard;\nconst Trigger = HoverCardTrigger;\nconst Portal = HoverCardPortal;\nconst Content = HoverCardContent;\nconst Arrow = HoverCardArrow;\n\nexport {\n  createHoverCardScope,\n  //\n  HoverCard,\n  HoverCardTrigger,\n  HoverCardPortal,\n  HoverCardContent,\n  HoverCardArrow,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n};\nexport type {\n  HoverCardProps,\n  HoverCardTriggerProps,\n  HoverCardPortalProps,\n  HoverCardContentProps,\n  HoverCardArrowProps,\n};\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,YAAY,qBAAqB;AACjC,SAAS,yBAAyB;AAClC,SAAS,UAAU,uBAAuB;AAC1C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAC1B,SAAS,wBAAwB;AA+F3B;AAvFN,IAAI;AAEJ,IAAM,iBAAiB;AAGvB,IAAM,CAAC,wBAAwB,oBAAoB,IAAI,mBAAmB,gBAAgB;AAAA,EACxF;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAYzC,IAAM,CAAC,mBAAmB,mBAAmB,IAC3C,uBAA8C,cAAc;AAW9D,IAAM,YAAsC,CAAC,UAAuC;AAClF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,EACf,IAAI;AACJ,QAAM,cAAc,eAAe,gBAAgB;AACnD,QAAM,eAAqB,aAAO,CAAC;AACnC,QAAM,gBAAsB,aAAO,CAAC;AACpC,QAAM,kBAAwB,aAAO,KAAK;AAC1C,QAAM,4BAAkC,aAAO,KAAK;AAEpD,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;AAAA,IACnD,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,aAAmB,kBAAY,MAAM;AACzC,iBAAa,cAAc,OAAO;AAClC,iBAAa,UAAU,OAAO,WAAW,MAAM,QAAQ,IAAI,GAAG,SAAS;AAAA,EACzE,GAAG,CAAC,WAAW,OAAO,CAAC;AAEvB,QAAM,cAAoB,kBAAY,MAAM;AAC1C,iBAAa,aAAa,OAAO;AACjC,QAAI,CAAC,gBAAgB,WAAW,CAAC,0BAA0B,SAAS;AAClE,oBAAc,UAAU,OAAO,WAAW,MAAM,QAAQ,KAAK,GAAG,UAAU;AAAA,IAC5E;AAAA,EACF,GAAG,CAAC,YAAY,OAAO,CAAC;AAExB,QAAM,gBAAsB,kBAAY,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,CAAC;AAGvE,EAAM,gBAAU,MAAM;AACpB,WAAO,MAAM;AACX,mBAAa,aAAa,OAAO;AACjC,mBAAa,cAAc,OAAO;AAAA,IACpC;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MAEA,8BAAiB,sBAAhB,EAAsB,GAAG,aAAc,UAAS;AAAA;AAAA,EACnD;AAEJ;AAEA,UAAU,cAAc;AAMxB,IAAM,eAAe;AAMrB,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,kBAAkB,GAAG,aAAa,IAAI;AAC9C,UAAM,UAAU,oBAAoB,cAAc,gBAAgB;AAClE,UAAM,cAAc,eAAe,gBAAgB;AACnD,WACE,oBAAiB,wBAAhB,EAAuB,SAAO,MAAE,GAAG,aAClC;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACC,cAAY,QAAQ,OAAO,SAAS;AAAA,QACnC,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,QAAQ,MAAM,CAAC;AAAA,QACvF,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,QAAQ,OAAO,CAAC;AAAA,QACxF,SAAS,qBAAqB,MAAM,SAAS,QAAQ,MAAM;AAAA,QAC3D,QAAQ,qBAAqB,MAAM,QAAQ,QAAQ,OAAO;AAAA,QAE1D,cAAc,qBAAqB,MAAM,cAAc,CAAC,UAAU,MAAM,eAAe,CAAC;AAAA;AAAA,IAC1F,GACF;AAAA,EAEJ;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,uBAA2C,aAAa;AAAA,EACjG,YAAY;AACd,CAAC;AAgBD,IAAM,kBAAkD,CACtD,UACG;AACH,QAAM,EAAE,kBAAkB,YAAY,UAAU,UAAU,IAAI;AAC9D,QAAM,UAAU,oBAAoB,aAAa,gBAAgB;AACjE,SACE,oBAAC,kBAAe,OAAO,kBAAkB,YACvC,8BAAC,YAAS,SAAS,cAAc,QAAQ,MACvC,8BAAC,mBAAgB,SAAO,MAAC,WACtB,UACH,GACF,GACF;AAEJ;AAEA,gBAAgB,cAAc;AAM9B,IAAM,eAAe;AAWrB,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,gBAAgB;AAC3E,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,oBAAoB,cAAc,MAAM,gBAAgB;AACxE,WACE,oBAAC,YAAS,SAAS,cAAc,QAAQ,MACvC;AAAA,MAAC;AAAA;AAAA,QACC,cAAY,QAAQ,OAAO,SAAS;AAAA,QACnC,GAAG;AAAA,QACJ,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,QAAQ,MAAM,CAAC;AAAA,QACvF,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,QAAQ,OAAO,CAAC;AAAA,QACxF,KAAK;AAAA;AAAA,IACP,GACF;AAAA,EAEJ;AACF;AAEA,iBAAiB,cAAc;AA+B/B,IAAM,uBAA6B,iBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAU,oBAAoB,cAAc,gBAAgB;AAClE,QAAM,cAAc,eAAe,gBAAgB;AACnD,QAAM,MAAY,aAAoC,IAAI;AAC1D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAS,KAAK;AAEpE,EAAM,gBAAU,MAAM;AACpB,QAAI,kBAAkB;AACpB,YAAM,OAAO,SAAS;AAGtB,+BAAyB,KAAK,MAAM,cAAc,KAAK,MAAM;AAE7D,WAAK,MAAM,aAAa;AACxB,WAAK,MAAM,mBAAmB;AAC9B,aAAO,MAAM;AACX,aAAK,MAAM,aAAa;AACxB,aAAK,MAAM,mBAAmB;AAAA,MAChC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,gBAAgB,CAAC;AAErB,EAAM,gBAAU,MAAM;AACpB,QAAI,IAAI,SAAS;AACf,YAAM,kBAAkB,MAAM;AAC5B,4BAAoB,KAAK;AACzB,gBAAQ,0BAA0B,UAAU;AAG5C,mBAAW,MAAM;AACf,gBAAM,eAAe,SAAS,aAAa,GAAG,SAAS,MAAM;AAC7D,cAAI,aAAc,SAAQ,gBAAgB,UAAU;AAAA,QACtD,CAAC;AAAA,MACH;AAEA,eAAS,iBAAiB,aAAa,eAAe;AACtD,aAAO,MAAM;AACX,iBAAS,oBAAoB,aAAa,eAAe;AACzD,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,0BAA0B,UAAU;AAAA,MAC9C;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,2BAA2B,QAAQ,eAAe,CAAC;AAE/D,EAAM,gBAAU,MAAM;AACpB,QAAI,IAAI,SAAS;AACf,YAAM,YAAY,iBAAiB,IAAI,OAAO;AAC9C,gBAAU,QAAQ,CAAC,aAAa,SAAS,aAAa,YAAY,IAAI,CAAC;AAAA,IACzE;AAAA,EACF,CAAC;AAED,SACE;AAAA,IAAC;AAAA;AAAA,MACC,SAAO;AAAA,MACP,6BAA6B;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB,qBAAqB,gBAAgB,CAAC,UAAU;AAC9D,cAAM,eAAe;AAAA,MACvB,CAAC;AAAA,MACD,WAAW,QAAQ;AAAA,MAEnB;AAAA,QAAiB;AAAA,QAAhB;AAAA,UACE,GAAG;AAAA,UACH,GAAG;AAAA,UACJ,eAAe,qBAAqB,aAAa,eAAe,CAAC,UAAU;AAEzE,gBAAI,MAAM,cAAc,SAAS,MAAM,MAAqB,GAAG;AAC7D,kCAAoB,IAAI;AAAA,YAC1B;AACA,oBAAQ,gBAAgB,UAAU;AAClC,oBAAQ,0BAA0B,UAAU;AAAA,UAC9C,CAAC;AAAA,UACD,KAAK;AAAA,UACL,OAAO;AAAA,YACL,GAAG,aAAa;AAAA,YAChB,YAAY,mBAAmB,SAAS;AAAA;AAAA,YAExC,kBAAkB,mBAAmB,SAAS;AAAA;AAAA,YAE9C,GAAG;AAAA,cACD,+CAA+C;AAAA,cAC/C,8CAA8C;AAAA,cAC9C,+CAA+C;AAAA,cAC/C,oCAAoC;AAAA,cACpC,qCAAqC;AAAA,YACvC;AAAA,UACF;AAAA;AAAA,MACF;AAAA;AAAA,EACF;AAEJ,CAAC;AAMD,IAAM,aAAa;AAMnB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,kBAAkB,GAAG,WAAW,IAAI;AAC5C,UAAM,cAAc,eAAe,gBAAgB;AACnD,WAAO,oBAAiB,uBAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,cAAc;AAAA,EACpF;AACF;AAEA,eAAe,cAAc;AAI7B,SAAS,aAAgB,cAA0B;AACjD,SAAO,CAAC,UACN,MAAM,gBAAgB,UAAU,SAAY,aAAa;AAC7D;AAMA,SAAS,iBAAiB,WAAwB;AAChD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;AAAA,IAC3E,YAAY,CAAC,SAAc;AAIzB,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;AAAA,IACpE;AAAA,EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AACtE,SAAO;AACT;AAEA,IAAMA,QAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ;", "names": ["Root", "Content", "Arrow"]}