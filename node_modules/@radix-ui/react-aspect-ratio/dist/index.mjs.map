{"version": 3, "sources": ["../src/AspectRatio.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * AspectRatio\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'AspectRatio';\n\ntype AspectRatioElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AspectRatioProps extends PrimitiveDivProps {\n  ratio?: number;\n}\n\nconst AspectRatio = React.forwardRef<AspectRatioElement, AspectRatioProps>(\n  (props, forwardedRef) => {\n    const { ratio = 1 / 1, style, ...aspectRatioProps } = props;\n    return (\n      <div\n        style={{\n          // ensures inner element is contained\n          position: 'relative',\n          // ensures padding bottom trick maths works\n          width: '100%',\n          paddingBottom: `${100 / ratio}%`,\n        }}\n        data-radix-aspect-ratio-wrapper=\"\"\n      >\n        <Primitive.div\n          {...aspectRatioProps}\n          ref={forwardedRef}\n          style={{\n            ...style,\n            // ensures children expand in ratio\n            position: 'absolute',\n            top: 0,\n            right: 0,\n            bottom: 0,\n            left: 0,\n          }}\n        />\n      </div>\n    );\n  }\n);\n\nAspectRatio.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = AspectRatio;\n\nexport {\n  AspectRatio,\n  //\n  Root,\n};\nexport type { AspectRatioProps };\n"], "mappings": ";AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AA4BlB;AAtBR,IAAM,OAAO;AAQb,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,QAAQ,IAAI,GAAG,OAAO,GAAG,iBAAiB,IAAI;AACtD,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA;AAAA,UAEL,UAAU;AAAA;AAAA,UAEV,OAAO;AAAA,UACP,eAAe,GAAG,MAAM,KAAK;AAAA,QAC/B;AAAA,QACA,mCAAgC;AAAA,QAEhC;AAAA,UAAC,UAAU;AAAA,UAAV;AAAA,YACE,GAAG;AAAA,YACJ,KAAK;AAAA,YACL,OAAO;AAAA,cACL,GAAG;AAAA;AAAA,cAEH,UAAU;AAAA,cACV,KAAK;AAAA,cACL,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,MAAM;AAAA,YACR;AAAA;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,YAAY,cAAc;AAI1B,IAAM,OAAO;", "names": []}