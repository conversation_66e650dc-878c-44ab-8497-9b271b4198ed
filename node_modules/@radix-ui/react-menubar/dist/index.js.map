{"version": 3, "sources": ["../src/index.ts", "../src/Menubar.tsx"], "sourcesContent": ["'use client';\nexport {\n  createMenubarScope,\n  //\n  Menubar,\n  MenubarMenu,\n  MenubarTrigger,\n  MenubarPortal,\n  MenubarContent,\n  MenubarGroup,\n  MenubarLabel,\n  MenubarItem,\n  MenubarCheckboxItem,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarItemIndicator,\n  MenubarSeparator,\n  MenubarArrow,\n  MenubarSub,\n  MenubarSubTrigger,\n  MenubarSubContent,\n  //\n  Root,\n  Menu,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n} from './Menubar';\nexport type {\n  MenubarProps,\n  MenubarMenuProps,\n  MenubarTriggerProps,\n  MenubarPortalProps,\n  MenubarContentProps,\n  MenubarGroupProps,\n  MenubarLabelProps,\n  MenubarItemProps,\n  MenubarCheckboxItemProps,\n  MenubarRadioGroupProps,\n  MenubarRadioItemProps,\n  MenubarItemIndicatorProps,\n  MenubarSeparatorProps,\n  MenubarArrowProps,\n  MenubarSubProps,\n  MenubarSubTriggerProps,\n  MenubarSubContentProps,\n} from './Menubar';\n", "import * as React from 'react';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * Menubar\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENUBAR_NAME = 'Menubar';\n\ntype ItemData = { value: string; disabled: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenubarTriggerElement,\n  ItemData\n>(MENUBAR_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenubar?: Scope };\nconst [createMenubarContext, createMenubarScope] = createContextScope(MENUBAR_NAME, [\n  createCollectionScope,\n  createRovingFocusGroupScope,\n]);\n\nconst useMenuScope = createMenuScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenubarContextValue = {\n  value: string;\n  dir: Direction;\n  loop: boolean;\n  onMenuOpen(value: string): void;\n  onMenuClose(): void;\n  onMenuToggle(value: string): void;\n};\n\nconst [MenubarContextProvider, useMenubarContext] =\n  createMenubarContext<MenubarContextValue>(MENUBAR_NAME);\n\ntype MenubarElement = React.ElementRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenubarProps extends PrimitiveDivProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?: (value: string) => void;\n  loop?: RovingFocusGroupProps['loop'];\n  dir?: RovingFocusGroupProps['dir'];\n}\n\nconst Menubar = React.forwardRef<MenubarElement, MenubarProps>(\n  (props: ScopedProps<MenubarProps>, forwardedRef) => {\n    const {\n      __scopeMenubar,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      loop = true,\n      dir,\n      ...menubarProps\n    } = props;\n    const direction = useDirection(dir);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenubar);\n    const [value = '', setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue,\n    });\n\n    // We need to manage tab stop id manually as `RovingFocusGroup` updates the stop\n    // based on focus, and in some situations our triggers won't ever be given focus\n    // (e.g. click to open and then outside to close)\n    const [currentTabStopId, setCurrentTabStopId] = React.useState<string | null>(null);\n\n    return (\n      <MenubarContextProvider\n        scope={__scopeMenubar}\n        value={value}\n        onMenuOpen={React.useCallback(\n          (value) => {\n            setValue(value);\n            setCurrentTabStopId(value);\n          },\n          [setValue]\n        )}\n        onMenuClose={React.useCallback(() => setValue(''), [setValue])}\n        onMenuToggle={React.useCallback(\n          (value) => {\n            setValue((prevValue) => (Boolean(prevValue) ? '' : value));\n            // `openMenuOpen` and `onMenuToggle` are called exclusively so we\n            // need to update the id in either case.\n            setCurrentTabStopId(value);\n          },\n          [setValue]\n        )}\n        dir={direction}\n        loop={loop}\n      >\n        <Collection.Provider scope={__scopeMenubar}>\n          <Collection.Slot scope={__scopeMenubar}>\n            <RovingFocusGroup.Root\n              asChild\n              {...rovingFocusGroupScope}\n              orientation=\"horizontal\"\n              loop={loop}\n              dir={direction}\n              currentTabStopId={currentTabStopId}\n              onCurrentTabStopIdChange={setCurrentTabStopId}\n            >\n              <Primitive.div role=\"menubar\" {...menubarProps} ref={forwardedRef} />\n            </RovingFocusGroup.Root>\n          </Collection.Slot>\n        </Collection.Provider>\n      </MenubarContextProvider>\n    );\n  }\n);\n\nMenubar.displayName = MENUBAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'MenubarMenu';\n\ntype MenubarMenuContextValue = {\n  value: string;\n  triggerId: string;\n  triggerRef: React.RefObject<MenubarTriggerElement>;\n  contentId: string;\n  wasKeyboardTriggerOpenRef: React.MutableRefObject<boolean>;\n};\n\nconst [MenubarMenuProvider, useMenubarMenuContext] =\n  createMenubarContext<MenubarMenuContextValue>(MENU_NAME);\n\ninterface MenubarMenuProps {\n  value?: string;\n  children?: React.ReactNode;\n}\n\nconst MenubarMenu = (props: ScopedProps<MenubarMenuProps>) => {\n  const { __scopeMenubar, value: valueProp, ...menuProps } = props;\n  const autoValue = useId();\n  // We need to provide an initial deterministic value as `useId` will return\n  // empty string on the first render and we don't want to match our internal \"closed\" value.\n  const value = valueProp || autoValue || 'LEGACY_REACT_AUTO_VALUE';\n  const context = useMenubarContext(MENU_NAME, __scopeMenubar);\n  const menuScope = useMenuScope(__scopeMenubar);\n  const triggerRef = React.useRef<MenubarTriggerElement>(null);\n  const wasKeyboardTriggerOpenRef = React.useRef(false);\n  const open = context.value === value;\n\n  React.useEffect(() => {\n    if (!open) wasKeyboardTriggerOpenRef.current = false;\n  }, [open]);\n\n  return (\n    <MenubarMenuProvider\n      scope={__scopeMenubar}\n      value={value}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      wasKeyboardTriggerOpenRef={wasKeyboardTriggerOpenRef}\n    >\n      <MenuPrimitive.Root\n        {...menuScope}\n        open={open}\n        onOpenChange={(open) => {\n          // Menu only calls `onOpenChange` when dismissing so we\n          // want to close our MenuBar based on the same events.\n          if (!open) context.onMenuClose();\n        }}\n        modal={false}\n        dir={context.dir}\n        {...menuProps}\n      />\n    </MenubarMenuProvider>\n  );\n};\n\nMenubarMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'MenubarTrigger';\n\ntype MenubarTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface MenubarTriggerProps extends PrimitiveButtonProps {}\n\nconst MenubarTrigger = React.forwardRef<MenubarTriggerElement, MenubarTriggerProps>(\n  (props: ScopedProps<MenubarTriggerProps>, forwardedRef) => {\n    const { __scopeMenubar, disabled = false, ...triggerProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenubar);\n    const menuScope = useMenuScope(__scopeMenubar);\n    const context = useMenubarContext(TRIGGER_NAME, __scopeMenubar);\n    const menuContext = useMenubarMenuContext(TRIGGER_NAME, __scopeMenubar);\n    const ref = React.useRef<MenubarTriggerElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, menuContext.triggerRef);\n    const [isFocused, setIsFocused] = React.useState(false);\n    const open = context.value === menuContext.value;\n\n    return (\n      <Collection.ItemSlot scope={__scopeMenubar} value={menuContext.value} disabled={disabled}>\n        <RovingFocusGroup.Item\n          asChild\n          {...rovingFocusGroupScope}\n          focusable={!disabled}\n          tabStopId={menuContext.value}\n        >\n          <MenuPrimitive.Anchor asChild {...menuScope}>\n            <Primitive.button\n              type=\"button\"\n              role=\"menuitem\"\n              id={menuContext.triggerId}\n              aria-haspopup=\"menu\"\n              aria-expanded={open}\n              aria-controls={open ? menuContext.contentId : undefined}\n              data-highlighted={isFocused ? '' : undefined}\n              data-state={open ? 'open' : 'closed'}\n              data-disabled={disabled ? '' : undefined}\n              disabled={disabled}\n              {...triggerProps}\n              ref={composedRefs}\n              onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n                // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n                // but not when the control key is pressed (avoiding MacOS right click)\n                if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                  context.onMenuOpen(menuContext.value);\n                  // prevent trigger focusing when opening\n                  // this allows the content to be given focus without competition\n                  if (!open) event.preventDefault();\n                }\n              })}\n              onPointerEnter={composeEventHandlers(props.onPointerEnter, () => {\n                const menubarOpen = Boolean(context.value);\n                if (menubarOpen && !open) {\n                  context.onMenuOpen(menuContext.value);\n                  ref.current?.focus();\n                }\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                if (disabled) return;\n                if (['Enter', ' '].includes(event.key)) context.onMenuToggle(menuContext.value);\n                if (event.key === 'ArrowDown') context.onMenuOpen(menuContext.value);\n                // prevent keydown from scrolling window / first focused item to execute\n                // that keydown (inadvertently closing the menu)\n                if (['Enter', ' ', 'ArrowDown'].includes(event.key)) {\n                  menuContext.wasKeyboardTriggerOpenRef.current = true;\n                  event.preventDefault();\n                }\n              })}\n              onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n              onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n            />\n          </MenuPrimitive.Anchor>\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nMenubarTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenubarPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface MenubarPortalProps extends MenuPortalProps {}\n\nconst MenubarPortal: React.FC<MenubarPortalProps> = (props: ScopedProps<MenubarPortalProps>) => {\n  const { __scopeMenubar, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nMenubarPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenubarContent';\n\ntype MenubarContentElement = React.ElementRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface MenubarContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst MenubarContent = React.forwardRef<MenubarContentElement, MenubarContentProps>(\n  (props: ScopedProps<MenubarContentProps>, forwardedRef) => {\n    const { __scopeMenubar, align = 'start', ...contentProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    const context = useMenubarContext(CONTENT_NAME, __scopeMenubar);\n    const menuContext = useMenubarMenuContext(CONTENT_NAME, __scopeMenubar);\n    const getItems = useCollection(__scopeMenubar);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={menuContext.contentId}\n        aria-labelledby={menuContext.triggerId}\n        data-radix-menubar-content=\"\"\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        align={align}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          const menubarOpen = Boolean(context.value);\n          if (!menubarOpen && !hasInteractedOutsideRef.current) {\n            menuContext.triggerRef.current?.focus();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n          const target = event.target as HTMLElement;\n          const isMenubarTrigger = getItems().some((item) => item.ref.current?.contains(target));\n          if (isMenubarTrigger) event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, () => {\n          hasInteractedOutsideRef.current = true;\n        })}\n        onEntryFocus={(event) => {\n          if (!menuContext.wasKeyboardTriggerOpenRef.current) event.preventDefault();\n        }}\n        onKeyDown={composeEventHandlers(\n          props.onKeyDown,\n          (event) => {\n            if (['ArrowRight', 'ArrowLeft'].includes(event.key)) {\n              const target = event.target as HTMLElement;\n              const targetIsSubTrigger = target.hasAttribute('data-radix-menubar-subtrigger');\n              const isKeyDownInsideSubMenu =\n                target.closest('[data-radix-menubar-content]') !== event.currentTarget;\n\n              const prevMenuKey = context.dir === 'rtl' ? 'ArrowRight' : 'ArrowLeft';\n              const isPrevKey = prevMenuKey === event.key;\n              const isNextKey = !isPrevKey;\n\n              // Prevent navigation when we're opening a submenu\n              if (isNextKey && targetIsSubTrigger) return;\n              // or we're inside a submenu and are moving backwards to close it\n              if (isKeyDownInsideSubMenu && isPrevKey) return;\n\n              const items = getItems().filter((item) => !item.disabled);\n              let candidateValues = items.map((item) => item.value);\n              if (isPrevKey) candidateValues.reverse();\n\n              const currentIndex = candidateValues.indexOf(menuContext.value);\n\n              candidateValues = context.loop\n                ? wrapArray(candidateValues, currentIndex + 1)\n                : candidateValues.slice(currentIndex + 1);\n\n              const [nextValue] = candidateValues;\n              if (nextValue) context.onMenuOpen(nextValue);\n            }\n          },\n          { checkForDefaultPrevented: false }\n        )}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-menubar-content-transform-origin': 'var(--radix-popper-transform-origin)',\n            '--radix-menubar-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-menubar-content-available-height': 'var(--radix-popper-available-height)',\n            '--radix-menubar-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-menubar-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nMenubarContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenubarGroup';\n\ntype MenubarGroupElement = React.ElementRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface MenubarGroupProps extends MenuGroupProps {}\n\nconst MenubarGroup = React.forwardRef<MenubarGroupElement, MenubarGroupProps>(\n  (props: ScopedProps<MenubarGroupProps>, forwardedRef) => {\n    const { __scopeMenubar, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenubarLabel';\n\ntype MenubarLabelElement = React.ElementRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface MenubarLabelProps extends MenuLabelProps {}\n\nconst MenubarLabel = React.forwardRef<MenubarLabelElement, MenubarLabelProps>(\n  (props: ScopedProps<MenubarLabelProps>, forwardedRef) => {\n    const { __scopeMenubar, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenubarItem';\n\ntype MenubarItemElement = React.ElementRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface MenubarItemProps extends MenuItemProps {}\n\nconst MenubarItem = React.forwardRef<MenubarItemElement, MenubarItemProps>(\n  (props: ScopedProps<MenubarItemProps>, forwardedRef) => {\n    const { __scopeMenubar, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenubarCheckboxItem';\n\ntype MenubarCheckboxItemElement = React.ElementRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface MenubarCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst MenubarCheckboxItem = React.forwardRef<MenubarCheckboxItemElement, MenubarCheckboxItemProps>(\n  (props: ScopedProps<MenubarCheckboxItemProps>, forwardedRef) => {\n    const { __scopeMenubar, ...checkboxItemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenubarRadioGroup';\n\ntype MenubarRadioGroupElement = React.ElementRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface MenubarRadioGroupProps extends MenuRadioGroupProps {}\n\nconst MenubarRadioGroup = React.forwardRef<MenubarRadioGroupElement, MenubarRadioGroupProps>(\n  (props: ScopedProps<MenubarRadioGroupProps>, forwardedRef) => {\n    const { __scopeMenubar, ...radioGroupProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenubarRadioItem';\n\ntype MenubarRadioItemElement = React.ElementRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface MenubarRadioItemProps extends MenuRadioItemProps {}\n\nconst MenubarRadioItem = React.forwardRef<MenubarRadioItemElement, MenubarRadioItemProps>(\n  (props: ScopedProps<MenubarRadioItemProps>, forwardedRef) => {\n    const { __scopeMenubar, ...radioItemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'MenubarItemIndicator';\n\ntype MenubarItemIndicatorElement = React.ElementRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface MenubarItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst MenubarItemIndicator = React.forwardRef<\n  MenubarItemIndicatorElement,\n  MenubarItemIndicatorProps\n>((props: ScopedProps<MenubarItemIndicatorProps>, forwardedRef) => {\n  const { __scopeMenubar, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nMenubarItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenubarSeparator';\n\ntype MenubarSeparatorElement = React.ElementRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface MenubarSeparatorProps extends MenuSeparatorProps {}\n\nconst MenubarSeparator = React.forwardRef<MenubarSeparatorElement, MenubarSeparatorProps>(\n  (props: ScopedProps<MenubarSeparatorProps>, forwardedRef) => {\n    const { __scopeMenubar, ...separatorProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenubarArrow';\n\ntype MenubarArrowElement = React.ElementRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface MenubarArrowProps extends MenuArrowProps {}\n\nconst MenubarArrow = React.forwardRef<MenubarArrowElement, MenubarArrowProps>(\n  (props: ScopedProps<MenubarArrowProps>, forwardedRef) => {\n    const { __scopeMenubar, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenubarSub';\n\ninterface MenubarSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenubarSub: React.FC<MenubarSubProps> = (props: ScopedProps<MenubarSubProps>) => {\n  const { __scopeMenubar, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\nMenubarSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenubarSubTrigger';\n\ntype MenubarSubTriggerElement = React.ElementRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface MenubarSubTriggerProps extends MenuSubTriggerProps {}\n\nconst MenubarSubTrigger = React.forwardRef<MenubarSubTriggerElement, MenubarSubTriggerProps>(\n  (props: ScopedProps<MenubarSubTriggerProps>, forwardedRef) => {\n    const { __scopeMenubar, ...subTriggerProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return (\n      <MenuPrimitive.SubTrigger\n        data-radix-menubar-subtrigger=\"\"\n        {...menuScope}\n        {...subTriggerProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenubarSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenubarSubContent';\n\ntype MenubarSubContentElement = React.ElementRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface MenubarSubContentProps extends MenuSubContentProps {}\n\nconst MenubarSubContent = React.forwardRef<MenubarSubContentElement, MenubarSubContentProps>(\n  (props: ScopedProps<MenubarSubContentProps>, forwardedRef) => {\n    const { __scopeMenubar, ...subContentProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n\n    return (\n      <MenuPrimitive.SubContent\n        {...menuScope}\n        data-radix-menubar-content=\"\"\n        {...subContentProps}\n        ref={forwardedRef}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-menubar-content-transform-origin': 'var(--radix-popper-transform-origin)',\n            '--radix-menubar-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-menubar-content-available-height': 'var(--radix-popper-available-height)',\n            '--radix-menubar-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-menubar-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nMenubarSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\nconst Root = Menubar;\nconst Menu = MenubarMenu;\nconst Trigger = MenubarTrigger;\nconst Portal = MenubarPortal;\nconst Content = MenubarContent;\nconst Group = MenubarGroup;\nconst Label = MenubarLabel;\nconst Item = MenubarItem;\nconst CheckboxItem = MenubarCheckboxItem;\nconst RadioGroup = MenubarRadioGroup;\nconst RadioItem = MenubarRadioItem;\nconst ItemIndicator = MenubarItemIndicator;\nconst Separator = MenubarSeparator;\nconst Arrow = MenubarArrow;\nconst Sub = MenubarSub;\nconst SubTrigger = MenubarSubTrigger;\nconst SubContent = MenubarSubContent;\n\nexport {\n  createMenubarScope,\n  //\n  Menubar,\n  MenubarMenu,\n  MenubarTrigger,\n  MenubarPortal,\n  MenubarContent,\n  MenubarGroup,\n  MenubarLabel,\n  MenubarItem,\n  MenubarCheckboxItem,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarItemIndicator,\n  MenubarSeparator,\n  MenubarArrow,\n  MenubarSub,\n  MenubarSubTrigger,\n  MenubarSubContent,\n  //\n  Root,\n  Menu,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenubarProps,\n  MenubarMenuProps,\n  MenubarTriggerProps,\n  MenubarPortalProps,\n  MenubarContentProps,\n  MenubarGroupProps,\n  MenubarLabelProps,\n  MenubarItemProps,\n  MenubarCheckboxItemProps,\n  MenubarRadioGroupProps,\n  MenubarRadioItemProps,\n  MenubarItemIndicatorProps,\n  MenubarSeparatorProps,\n  MenubarArrowProps,\n  MenubarSubProps,\n  MenubarSubTriggerProps,\n  MenubarSubContentProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,eAAAA;AAAA,EAAA,oBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,8BAAiC;AACjC,6BAA6B;AAC7B,uBAAqC;AACrC,gCAAgC;AAChC,2BAAmC;AACnC,sBAAsB;AACtB,oBAA+B;AAC/B,wBAAgC;AAChC,uBAAkC;AAClC,gCAA4C;AAC5C,6BAA0B;AAC1B,0CAAqC;AA6GvB;AAnGd,IAAM,eAAe;AAGrB,IAAM,CAAC,YAAY,eAAe,qBAAqB,QAAI,0CAGzD,YAAY;AAGd,IAAM,CAAC,sBAAsB,kBAAkB,QAAI,yCAAmB,cAAc;AAAA,EAClF;AAAA,EACA;AACF,CAAC;AAED,IAAM,mBAAe,mCAAgB;AACrC,IAAM,+BAA2B,uDAA4B;AAW7D,IAAM,CAAC,wBAAwB,iBAAiB,IAC9C,qBAA0C,YAAY;AAaxD,IAAM,UAAgB;AAAA,EACpB,CAAC,OAAkC,iBAAiB;AAClD,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,gBAAY,qCAAa,GAAG;AAClC,UAAM,wBAAwB,yBAAyB,cAAc;AACrE,UAAM,CAAC,QAAQ,IAAI,QAAQ,QAAI,0DAAqB;AAAA,MAClD,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf,CAAC;AAKD,UAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAwB,IAAI;AAElF,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA,YAAkB;AAAA,UAChB,CAACC,WAAU;AACT,qBAASA,MAAK;AACd,gCAAoBA,MAAK;AAAA,UAC3B;AAAA,UACA,CAAC,QAAQ;AAAA,QACX;AAAA,QACA,aAAmB,kBAAY,MAAM,SAAS,EAAE,GAAG,CAAC,QAAQ,CAAC;AAAA,QAC7D,cAAoB;AAAA,UAClB,CAACA,WAAU;AACT,qBAAS,CAAC,cAAe,QAAQ,SAAS,IAAI,KAAKA,MAAM;AAGzD,gCAAoBA,MAAK;AAAA,UAC3B;AAAA,UACA,CAAC,QAAQ;AAAA,QACX;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QAEA,sDAAC,WAAW,UAAX,EAAoB,OAAO,gBAC1B,sDAAC,WAAW,MAAX,EAAgB,OAAO,gBACtB;AAAA,UAAkB;AAAA,UAAjB;AAAA,YACC,SAAO;AAAA,YACN,GAAG;AAAA,YACJ,aAAY;AAAA,YACZ;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA,0BAA0B;AAAA,YAE1B,sDAAC,iCAAU,KAAV,EAAc,MAAK,WAAW,GAAG,cAAc,KAAK,cAAc;AAAA;AAAA,QACrE,GACF,GACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,QAAQ,cAAc;AAMtB,IAAM,YAAY;AAUlB,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,qBAA8C,SAAS;AAOzD,IAAM,cAAc,CAAC,UAAyC;AAC5D,QAAM,EAAE,gBAAgB,OAAO,WAAW,GAAG,UAAU,IAAI;AAC3D,QAAM,gBAAY,uBAAM;AAGxB,QAAM,QAAQ,aAAa,aAAa;AACxC,QAAM,UAAU,kBAAkB,WAAW,cAAc;AAC3D,QAAM,YAAY,aAAa,cAAc;AAC7C,QAAM,aAAmB,aAA8B,IAAI;AAC3D,QAAM,4BAAkC,aAAO,KAAK;AACpD,QAAM,OAAO,QAAQ,UAAU;AAE/B,EAAM,gBAAU,MAAM;AACpB,QAAI,CAAC,KAAM,2BAA0B,UAAU;AAAA,EACjD,GAAG,CAAC,IAAI,CAAC;AAET,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA,eAAW,uBAAM;AAAA,MACjB;AAAA,MACA,eAAW,uBAAM;AAAA,MACjB;AAAA,MAEA;AAAA,QAAe;AAAA,QAAd;AAAA,UACE,GAAG;AAAA,UACJ;AAAA,UACA,cAAc,CAACC,UAAS;AAGtB,gBAAI,CAACA,MAAM,SAAQ,YAAY;AAAA,UACjC;AAAA,UACA,OAAO;AAAA,UACP,KAAK,QAAQ;AAAA,UACZ,GAAG;AAAA;AAAA,MACN;AAAA;AAAA,EACF;AAEJ;AAEA,YAAY,cAAc;AAM1B,IAAM,eAAe;AAMrB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,gBAAgB,WAAW,OAAO,GAAG,aAAa,IAAI;AAC9D,UAAM,wBAAwB,yBAAyB,cAAc;AACrE,UAAM,YAAY,aAAa,cAAc;AAC7C,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,cAAc,sBAAsB,cAAc,cAAc;AACtE,UAAM,MAAY,aAA8B,IAAI;AACpD,UAAM,mBAAe,2CAAgB,cAAc,KAAK,YAAY,UAAU;AAC9E,UAAM,CAAC,WAAW,YAAY,IAAU,eAAS,KAAK;AACtD,UAAM,OAAO,QAAQ,UAAU,YAAY;AAE3C,WACE,4CAAC,WAAW,UAAX,EAAoB,OAAO,gBAAgB,OAAO,YAAY,OAAO,UACpE;AAAA,MAAkB;AAAA,MAAjB;AAAA,QACC,SAAO;AAAA,QACN,GAAG;AAAA,QACJ,WAAW,CAAC;AAAA,QACZ,WAAW,YAAY;AAAA,QAEvB,sDAAe,sBAAd,EAAqB,SAAO,MAAE,GAAG,WAChC;AAAA,UAAC,iCAAU;AAAA,UAAV;AAAA,YACC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,IAAI,YAAY;AAAA,YAChB,iBAAc;AAAA,YACd,iBAAe;AAAA,YACf,iBAAe,OAAO,YAAY,YAAY;AAAA,YAC9C,oBAAkB,YAAY,KAAK;AAAA,YACnC,cAAY,OAAO,SAAS;AAAA,YAC5B,iBAAe,WAAW,KAAK;AAAA,YAC/B;AAAA,YACC,GAAG;AAAA,YACJ,KAAK;AAAA,YACL,mBAAe,uCAAqB,MAAM,eAAe,CAAC,UAAU;AAGlE,kBAAI,CAAC,YAAY,MAAM,WAAW,KAAK,MAAM,YAAY,OAAO;AAC9D,wBAAQ,WAAW,YAAY,KAAK;AAGpC,oBAAI,CAAC,KAAM,OAAM,eAAe;AAAA,cAClC;AAAA,YACF,CAAC;AAAA,YACD,oBAAgB,uCAAqB,MAAM,gBAAgB,MAAM;AAC/D,oBAAM,cAAc,QAAQ,QAAQ,KAAK;AACzC,kBAAI,eAAe,CAAC,MAAM;AACxB,wBAAQ,WAAW,YAAY,KAAK;AACpC,oBAAI,SAAS,MAAM;AAAA,cACrB;AAAA,YACF,CAAC;AAAA,YACD,eAAW,uCAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,kBAAI,SAAU;AACd,kBAAI,CAAC,SAAS,GAAG,EAAE,SAAS,MAAM,GAAG,EAAG,SAAQ,aAAa,YAAY,KAAK;AAC9E,kBAAI,MAAM,QAAQ,YAAa,SAAQ,WAAW,YAAY,KAAK;AAGnE,kBAAI,CAAC,SAAS,KAAK,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AACnD,4BAAY,0BAA0B,UAAU;AAChD,sBAAM,eAAe;AAAA,cACvB;AAAA,YACF,CAAC;AAAA,YACD,aAAS,uCAAqB,MAAM,SAAS,MAAM,aAAa,IAAI,CAAC;AAAA,YACrE,YAAQ,uCAAqB,MAAM,QAAQ,MAAM,aAAa,KAAK,CAAC;AAAA;AAAA,QACtE,GACF;AAAA;AAAA,IACF,GACF;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,cAAc;AAKpB,IAAM,gBAA8C,CAAC,UAA2C;AAC9F,QAAM,EAAE,gBAAgB,GAAG,YAAY,IAAI;AAC3C,QAAM,YAAY,aAAa,cAAc;AAC7C,SAAO,4CAAe,sBAAd,EAAsB,GAAG,WAAY,GAAG,aAAa;AAC/D;AAEA,cAAc,cAAc;AAM5B,IAAM,eAAe;AAMrB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,gBAAgB,QAAQ,SAAS,GAAG,aAAa,IAAI;AAC7D,UAAM,YAAY,aAAa,cAAc;AAC7C,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,cAAc,sBAAsB,cAAc,cAAc;AACtE,UAAM,WAAW,cAAc,cAAc;AAC7C,UAAM,0BAAgC,aAAO,KAAK;AAElD,WACE;AAAA,MAAe;AAAA,MAAd;AAAA,QACC,IAAI,YAAY;AAAA,QAChB,mBAAiB,YAAY;AAAA,QAC7B,8BAA2B;AAAA,QAC1B,GAAG;AAAA,QACH,GAAG;AAAA,QACJ,KAAK;AAAA,QACL;AAAA,QACA,sBAAkB,uCAAqB,MAAM,kBAAkB,CAAC,UAAU;AACxE,gBAAM,cAAc,QAAQ,QAAQ,KAAK;AACzC,cAAI,CAAC,eAAe,CAAC,wBAAwB,SAAS;AACpD,wBAAY,WAAW,SAAS,MAAM;AAAA,UACxC;AAEA,kCAAwB,UAAU;AAElC,gBAAM,eAAe;AAAA,QACvB,CAAC;AAAA,QACD,oBAAgB,uCAAqB,MAAM,gBAAgB,CAAC,UAAU;AACpE,gBAAM,SAAS,MAAM;AACrB,gBAAM,mBAAmB,SAAS,EAAE,KAAK,CAAC,SAAS,KAAK,IAAI,SAAS,SAAS,MAAM,CAAC;AACrF,cAAI,iBAAkB,OAAM,eAAe;AAAA,QAC7C,CAAC;AAAA,QACD,uBAAmB,uCAAqB,MAAM,mBAAmB,MAAM;AACrE,kCAAwB,UAAU;AAAA,QACpC,CAAC;AAAA,QACD,cAAc,CAAC,UAAU;AACvB,cAAI,CAAC,YAAY,0BAA0B,QAAS,OAAM,eAAe;AAAA,QAC3E;AAAA,QACA,eAAW;AAAA,UACT,MAAM;AAAA,UACN,CAAC,UAAU;AACT,gBAAI,CAAC,cAAc,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AACnD,oBAAM,SAAS,MAAM;AACrB,oBAAM,qBAAqB,OAAO,aAAa,+BAA+B;AAC9E,oBAAM,yBACJ,OAAO,QAAQ,8BAA8B,MAAM,MAAM;AAE3D,oBAAM,cAAc,QAAQ,QAAQ,QAAQ,eAAe;AAC3D,oBAAM,YAAY,gBAAgB,MAAM;AACxC,oBAAM,YAAY,CAAC;AAGnB,kBAAI,aAAa,mBAAoB;AAErC,kBAAI,0BAA0B,UAAW;AAEzC,oBAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,kBAAI,kBAAkB,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK;AACpD,kBAAI,UAAW,iBAAgB,QAAQ;AAEvC,oBAAM,eAAe,gBAAgB,QAAQ,YAAY,KAAK;AAE9D,gCAAkB,QAAQ,OACtB,UAAU,iBAAiB,eAAe,CAAC,IAC3C,gBAAgB,MAAM,eAAe,CAAC;AAE1C,oBAAM,CAAC,SAAS,IAAI;AACpB,kBAAI,UAAW,SAAQ,WAAW,SAAS;AAAA,YAC7C;AAAA,UACF;AAAA,UACA,EAAE,0BAA0B,MAAM;AAAA,QACpC;AAAA,QACA,OAAO;AAAA,UACL,GAAG,MAAM;AAAA;AAAA,UAET,GAAG;AAAA,YACD,4CAA4C;AAAA,YAC5C,2CAA2C;AAAA,YAC3C,4CAA4C;AAAA,YAC5C,iCAAiC;AAAA,YACjC,kCAAkC;AAAA,UACpC;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,aAAa;AAMnB,IAAM,eAAqB;AAAA,EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,YAAY,aAAa,cAAc;AAC7C,WAAO,4CAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,aAAa,cAAc;AAM3B,IAAM,aAAa;AAMnB,IAAM,eAAqB;AAAA,EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,YAAY,aAAa,cAAc;AAC7C,WAAO,4CAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,aAAa,cAAc;AAM3B,IAAM,YAAY;AAMlB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,gBAAgB,GAAG,UAAU,IAAI;AACzC,UAAM,YAAY,aAAa,cAAc;AAC7C,WAAO,4CAAe,oBAAd,EAAoB,GAAG,WAAY,GAAG,WAAW,KAAK,cAAc;AAAA,EAC9E;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,qBAAqB;AAM3B,IAAM,sBAA4B;AAAA,EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,gBAAgB,GAAG,kBAAkB,IAAI;AACjD,UAAM,YAAY,aAAa,cAAc;AAC7C,WAAO,4CAAe,4BAAd,EAA4B,GAAG,WAAY,GAAG,mBAAmB,KAAK,cAAc;AAAA,EAC9F;AACF;AAEA,oBAAoB,cAAc;AAMlC,IAAM,mBAAmB;AAMzB,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,gBAAgB,GAAG,gBAAgB,IAAI;AAC/C,UAAM,YAAY,aAAa,cAAc;AAC7C,WAAO,4CAAe,0BAAd,EAA0B,GAAG,WAAY,GAAG,iBAAiB,KAAK,cAAc;AAAA,EAC1F;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,kBAAkB;AAMxB,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,gBAAgB,GAAG,eAAe,IAAI;AAC9C,UAAM,YAAY,aAAa,cAAc;AAC7C,WAAO,4CAAe,yBAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,cAAc;AAAA,EACxF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,iBAAiB;AAMvB,IAAM,uBAA6B,iBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,gBAAgB,GAAG,mBAAmB,IAAI;AAClD,QAAM,YAAY,aAAa,cAAc;AAC7C,SAAO,4CAAe,6BAAd,EAA6B,GAAG,WAAY,GAAG,oBAAoB,KAAK,cAAc;AAChG,CAAC;AAED,qBAAqB,cAAc;AAMnC,IAAM,iBAAiB;AAMvB,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,gBAAgB,GAAG,eAAe,IAAI;AAC9C,UAAM,YAAY,aAAa,cAAc;AAC7C,WAAO,4CAAe,yBAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,cAAc;AAAA,EACxF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,aAAa;AAMnB,IAAM,eAAqB;AAAA,EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,YAAY,aAAa,cAAc;AAC7C,WAAO,4CAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,aAAa,cAAc;AAM3B,IAAM,WAAW;AASjB,IAAM,aAAwC,CAAC,UAAwC;AACrF,QAAM,EAAE,gBAAgB,UAAU,MAAM,UAAU,cAAc,YAAY,IAAI;AAChF,QAAM,YAAY,aAAa,cAAc;AAC7C,QAAM,CAAC,OAAO,OAAO,OAAO,QAAI,0DAAqB;AAAA,IACnD,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,EACZ,CAAC;AAED,SACE,4CAAe,mBAAd,EAAmB,GAAG,WAAW,MAAY,cAAc,SACzD,UACH;AAEJ;AAEA,WAAW,cAAc;AAMzB,IAAM,mBAAmB;AAMzB,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,gBAAgB,GAAG,gBAAgB,IAAI;AAC/C,UAAM,YAAY,aAAa,cAAc;AAC7C,WACE;AAAA,MAAe;AAAA,MAAd;AAAA,QACC,iCAA8B;AAAA,QAC7B,GAAG;AAAA,QACH,GAAG;AAAA,QACJ,KAAK;AAAA;AAAA,IACP;AAAA,EAEJ;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,mBAAmB;AAMzB,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,gBAAgB,GAAG,gBAAgB,IAAI;AAC/C,UAAM,YAAY,aAAa,cAAc;AAE7C,WACE;AAAA,MAAe;AAAA,MAAd;AAAA,QACE,GAAG;AAAA,QACJ,8BAA2B;AAAA,QAC1B,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,OAAO;AAAA,UACL,GAAG,MAAM;AAAA;AAAA,UAET,GAAG;AAAA,YACD,4CAA4C;AAAA,YAC5C,2CAA2C;AAAA,YAC3C,4CAA4C;AAAA,YAC5C,iCAAiC;AAAA,YACjC,kCAAkC;AAAA,UACpC;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,kBAAkB,cAAc;AAQhC,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAI,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAC;AAC3E;AAEA,IAAMC,QAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ;AACd,IAAMC,SAAQ;AACd,IAAMC,QAAO;AACb,IAAMC,gBAAe;AACrB,IAAMC,cAAa;AACnB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AACtB,IAAMC,aAAY;AAClB,IAAMC,SAAQ;AACd,IAAMC,OAAM;AACZ,IAAMC,cAAa;AACnB,IAAMC,cAAa;", "names": ["Arrow", "CheckboxItem", "Content", "Group", "<PERSON><PERSON>", "ItemIndicator", "Label", "Portal", "RadioGroup", "RadioItem", "Root", "Separator", "Sub", "SubContent", "SubTrigger", "value", "open", "Root", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"]}