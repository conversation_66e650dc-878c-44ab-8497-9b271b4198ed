{"name": "@swc/core", "version": "1.7.39", "description": "Super-fast alternative for babel", "homepage": "https://swc.rs", "main": "./index.js", "author": "강동윤 <<EMAIL>>", "license": "Apache-2.0", "keywords": ["swc", "swcpack", "babel", "typescript", "rust", "webpack", "tsc"], "engines": {"node": ">=10"}, "repository": {"type": "git", "url": "git+https://github.com/swc-project/swc.git"}, "bugs": {"url": "https://github.com/swc-project/swc/issues"}, "napi": {"binaryName": "swc", "targets": ["x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl", "i686-pc-windows-msvc", "armv7-unknown-linux-gnueabihf", "aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "aarch64-pc-windows-msvc"]}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "types": "./index.d.ts", "scripts": {"postinstall": "node postinstall.js", "artifacts": "napi artifacts --npm-dir scripts/npm", "prepack": "tsc -d && napi prepublish -p scripts/npm --tag-style npm", "pack": "wasm-pack", "build:ts": "tsc -d", "build:wasm": "npm-run-all \"pack -- build ../../bindings/binding_core_wasm --scope swc {1} -t {2} --features plugin\" --", "build": "tsc -d && napi build --manifest-path ../../bindings/Cargo.toml --platform -p binding_core_node --js ./binding.js --dts ./binding.d.ts --release -o .", "build:dev": "tsc -d && napi build --manifest-path ../../bindings/Cargo.toml --platform -p binding_core_node --js ./binding.js --dts ./binding.d.ts -o .", "test": "cross-env NODE_OPTIONS='--experimental-vm-modules' jest --config ./jest.config.js", "version": "napi version --npm-dir scripts/npm"}, "peerDependencies": {"@swc/helpers": "*"}, "peerDependenciesMeta": {"@swc/helpers": {"optional": true}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/swc"}, "files": ["CHANGELOG.md", "Visitor.d.ts", "index.d.ts", "spack.js", "util.d.ts", "LICENSE", "Visitor.js", "binding.d.ts", "index.js", "types.d.ts", "util.js", "README.md", "binding.js", "package.json", "spack.d.ts", "types.js", "postinstall.js", "bindings/binding_core_wasm/pkg/binding_core_wasm.d.ts"], "dependencies": {"@swc/counter": "^0.1.3", "@swc/types": "^0.1.13"}, "devDependencies": {"@napi-rs/cli": "^3.0.0-alpha.43", "cross-env": "^7.0.3", "jest": "^29.7.0", "typescript": "^5.3.3"}, "optionalDependencies": {"@swc/core-darwin-x64": "1.7.39", "@swc/core-win32-x64-msvc": "1.7.39", "@swc/core-linux-x64-gnu": "1.7.39", "@swc/core-linux-x64-musl": "1.7.39", "@swc/core-win32-ia32-msvc": "1.7.39", "@swc/core-linux-arm-gnueabihf": "1.7.39", "@swc/core-darwin-arm64": "1.7.39", "@swc/core-linux-arm64-gnu": "1.7.39", "@swc/core-linux-arm64-musl": "1.7.39", "@swc/core-win32-arm64-msvc": "1.7.39"}}