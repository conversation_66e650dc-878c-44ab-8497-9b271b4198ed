{"hash": "f8490b93", "configHash": "0f6cb843", "lockfileHash": "485a45ec", "browserHash": "1015cd24", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "e72af9e3", "needsInterop": true}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "2858db73", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "0b0b2ae2", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "50d834a6", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "59fe37c9", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "bb995640", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d6751cd1", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "340ff6a2", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "0e5571d8", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}