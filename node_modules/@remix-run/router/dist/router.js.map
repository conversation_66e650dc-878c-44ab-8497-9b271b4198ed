{"version": 3, "file": "router.js", "sources": ["../history.ts", "../utils.ts", "../router.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n// TODO: (v7) Change the Location generic default from `any` to `unknown` and\n// remove Remix `useLocation` wrapper.\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location<State = any> extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: State;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. This may be either a URL or the pieces\n * of a URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { invariant, parsePath, warning } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  // We keep the raw Response for redirects so we can return it verbatim\n  response: Response;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on `<Form>`,\n * useSubmit(), `<fetcher.Form>`, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\"\n  | \"application/json\"\n  | \"text/plain\";\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport type Submission =\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: FormData;\n      json: undefined;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: JsonValue;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: undefined;\n      text: string;\n    };\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs<Context> {\n  request: Request;\n  params: Params;\n  context?: Context;\n}\n\n// TODO: (v7) Change the defaults from any to unknown in and remove Remix wrappers:\n//   ActionFunction, ActionFunctionArgs, LoaderFunction, LoaderFunctionArgs\n//   Also, make them a type alias instead of an interface\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\ntype DataFunctionReturnValue = Promise<DataFunctionValue> | DataFunctionValue;\n\n/**\n * Route loader function signature\n */\nexport type LoaderFunction<Context = any> = {\n  (\n    args: LoaderFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n} & { hydrate?: boolean };\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction<Context = any> {\n  (\n    args: ActionFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n}\n\n/**\n * Arguments passed to shouldRevalidate function\n */\nexport interface ShouldRevalidateFunctionArgs {\n  currentUrl: URL;\n  currentParams: AgnosticDataRouteMatch[\"params\"];\n  nextUrl: URL;\n  nextParams: AgnosticDataRouteMatch[\"params\"];\n  formMethod?: Submission[\"formMethod\"];\n  formAction?: Submission[\"formAction\"];\n  formEncType?: Submission[\"formEncType\"];\n  text?: Submission[\"text\"];\n  formData?: Submission[\"formData\"];\n  json?: Submission[\"json\"];\n  actionStatus?: number;\n  actionResult?: any;\n  defaultShouldRevalidate: boolean;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: ShouldRevalidateFunctionArgs): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\nexport interface DataStrategyMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {\n  shouldLoad: boolean;\n  resolve: (\n    handlerOverride?: (\n      handler: (ctx?: unknown) => DataFunctionReturnValue\n    ) => DataFunctionReturnValue\n  ) => Promise<DataStrategyResult>;\n}\n\nexport interface DataStrategyFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {\n  matches: DataStrategyMatch[];\n  fetcherKey: string | null;\n}\n\n/**\n * Result from a loader or action called via dataStrategy\n */\nexport interface DataStrategyResult {\n  type: \"data\" | \"error\";\n  result: unknown; // data, Error, Response, DeferredData, DataWithResponseInit\n}\n\nexport interface DataStrategyFunction {\n  (args: DataStrategyFunctionArgs): Promise<Record<string, DataStrategyResult>>;\n}\n\nexport type AgnosticPatchRoutesOnNavigationFunctionArgs<\n  O extends AgnosticRouteObject = AgnosticRouteObject,\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> = {\n  path: string;\n  matches: M[];\n  patch: (routeId: string | null, children: O[]) => void;\n};\n\nexport type AgnosticPatchRoutesOnNavigationFunction<\n  O extends AgnosticRouteObject = AgnosticRouteObject,\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> = (\n  opts: AgnosticPatchRoutesOnNavigationFunctionArgs<O, M>\n) => void | Promise<void>;\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\ntype RequireOne<T, Key = keyof T> = Exclude<\n  {\n    [K in keyof T]: K extends Key ? Omit<T, K> & Required<Pick<T, K>> : never;\n  }[keyof T],\n  undefined\n>;\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<RequireOne<Omit<R, ImmutableRouteKey>>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction | boolean;\n  action?: ActionFunction | boolean;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\nexport type PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise, return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if you could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: string[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\n\nexport function matchRoutesImpl<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename: string,\n  allowPartial: boolean\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      decoded,\n      allowPartial\n    );\n  }\n\n  return matches;\n}\n\nexport interface UIMatch<Data = unknown, Handle = unknown> {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: Data;\n  handle: Handle;\n}\n\nexport function convertRouteMatchToUiMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UIMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle,\n  };\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string,\n  allowPartial = false\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    let route = meta.route;\n\n    if (\n      !match &&\n      end &&\n      allowPartial &&\n      !routesMeta[routesMeta.length - 1].route.index\n    ) {\n      match = matchPath(\n        {\n          path: meta.relativePath,\n          caseSensitive: meta.caseSensitive,\n          end: false,\n        },\n        remainingPathname\n      );\n    }\n\n    if (!match) {\n      return null;\n    }\n\n    Object.assign(matchedParams, match.params);\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const stringify = (p: any) =>\n    p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        // Apply the splat\n        return stringify(params[star]);\n      }\n\n      const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n        invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n        return stringify(param);\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, compiledParams] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = compiledParams.reduce<Mutable<Params>>(\n    (memo, { paramName, isOptional }, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      const value = captureGroups[index];\n      if (isOptional && !value) {\n        memo[paramName] = undefined;\n      } else {\n        memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n      }\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\ntype CompiledPathParam = { paramName: string; isOptional?: boolean };\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, CompiledPathParam[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let params: CompiledPathParam[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(\n        /\\/:([\\w-]+)(\\?)?/g,\n        (_: string, paramName: string, isOptional) => {\n          params.push({ paramName, isOptional: isOptional != null });\n          return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n        }\n      );\n\n  if (path.endsWith(\"*\")) {\n    params.push({ paramName: \"*\" });\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, params];\n}\n\nexport function decodePath(value: string) {\n  try {\n    return value\n      .split(\"/\")\n      .map((v) => decodeURIComponent(v).replace(/\\//g, \"%2F\"))\n      .join(\"/\");\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nexport function getResolveToMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[], v7_relativeSplatPath: boolean) {\n  let pathMatches = getPathContributingMatches(matches);\n\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) =>\n      idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase\n    );\n  }\n\n  return pathMatches.map((match) => match.pathnameBase);\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport class DataWithResponseInit<D> {\n  type: string = \"DataWithResponseInit\";\n  data: D;\n  init: ResponseInit | null;\n\n  constructor(data: D, init?: ResponseInit) {\n    this.data = data;\n    this.init = init || null;\n  }\n}\n\n/**\n * Create \"responses\" that contain `status`/`headers` without forcing\n * serialization into an actual `Response` - used by Remix single fetch\n */\nexport function data<D>(data: D, init?: number | ResponseInit) {\n  return new DataWithResponseInit(\n    data,\n    typeof init === \"number\" ? { status: init } : init\n  );\n}\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, undefined, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\n        `Deferred data for key \"${key}\" resolved/rejected with \\`undefined\\`, ` +\n          `you must resolve/reject with a value or \\`null\\`.`\n      );\n      Object.defineProperty(promise, \"_error\", { get: () => undefinedError });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirectDocument: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n\n/**\n * A redirect response that will perform a `history.replaceState` instead of a\n * `history.pushState` for client-side navigation redirects.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const replace: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\n\nexport type ErrorResponse = {\n  status: number;\n  statusText: string;\n  data: any;\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nexport class ErrorResponseImpl implements ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  private error?: Error;\n  private internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DataStrategyMatch,\n  AgnosticRouteObject,\n  DataResult,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DeferredData,\n  DeferredResult,\n  DetectErrorBoundaryFunction,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  HTMLFormMethod,\n  DataStrategyResult,\n  ImmutableRouteKey,\n  MapRoutePropertiesFunction,\n  MutationFormMethod,\n  RedirectResult,\n  RouteData,\n  RouteManifest,\n  ShouldRevalidateFunctionArgs,\n  Submission,\n  SuccessResult,\n  UIMatch,\n  V7_FormMethod,\n  V7_MutationFormMethod,\n  AgnosticPatchRoutesOnNavigationFunction,\n  DataWithResponseInit,\n} from \"./utils\";\nimport {\n  ErrorResponseImpl,\n  ResultType,\n  convertRouteMatchToUiMatch,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  getResolveToMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  matchRoutesImpl,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the future config for the router\n   */\n  get future(): FutureConfig;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the window associated with the router\n   */\n  get window(): RouterInit[\"window\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE DO NOT USE\n   *\n   * Patch additional children routes into an existing parent route\n   * @param routeId The parent route id or a callback function accepting `patch`\n   *                to perform batch patching\n   * @param children The additional children routes\n   */\n  patchRoutes(routeId: string | null, children: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_fetcherPersist: boolean;\n  v7_normalizeFormMethod: boolean;\n  v7_partialHydration: boolean;\n  v7_prependBasename: boolean;\n  v7_relativeSplatPath: boolean;\n  v7_skipActionErrorRevalidation: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n  window?: Window;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: AgnosticPatchRoutesOnNavigationFunction;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: {\n      routeId?: string;\n      requestContext?: unknown;\n      dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<any>;\n}\n\ntype ViewTransitionOpts = {\n  currentLocation: Location;\n  nextLocation: Location;\n};\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (\n    state: RouterState,\n    opts: {\n      deletedFetchers: string[];\n      viewTransitionOpts?: ViewTransitionOpts;\n      flushSync: boolean;\n    }\n  ): void;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UIMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\n// Allowed for any navigation or fetch\ntype BaseNavigateOrFetchOptions = {\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n};\n\n// Only allowed for navigations\ntype BaseNavigateOptions = BaseNavigateOrFetchOptions & {\n  replace?: boolean;\n  state?: any;\n  fromRouteId?: string;\n  viewTransition?: boolean;\n};\n\n// Only allowed for submission navigations\ntype BaseSubmissionOptions = {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n} & (\n  | { formData: FormData; body?: undefined }\n  | { formData?: undefined; body: any }\n);\n\n/**\n * Options for a navigate() call for a normal (non-submission) navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a submission navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to navigate() for a navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options for a fetch() load\n */\ntype LoadFetchOptions = BaseNavigateOrFetchOptions;\n\n/**\n * Options for a fetch() submission\n */\ntype SubmitFetchOptions = BaseNavigateOrFetchOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions = LoadFetchOptions | SubmitFetchOptions;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    json: undefined;\n    text: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    text: Submission[\"text\"];\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    text: undefined;\n    formData: undefined;\n    json: undefined;\n    data: TData | undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    data: TData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    text: Submission[\"text\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    data: TData | undefined;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ntype PendingActionResult = [string, SuccessResult | ErrorResult];\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * Tuple for the returned or thrown value from the current action.  The routeId\n   * is the action route for success and the bubbled boundary route for errors.\n   */\n  pendingActionResult?: PendingActionResult;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  const routerWindow = init.window\n    ? init.window\n    : typeof window !== \"undefined\"\n    ? window\n    : undefined;\n  const isBrowser =\n    typeof routerWindow !== \"undefined\" &&\n    typeof routerWindow.document !== \"undefined\" &&\n    typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.dataStrategy || defaultDataStrategy;\n  let patchRoutesOnNavigationImpl = init.patchRoutesOnNavigation;\n\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    v7_skipActionErrorRevalidation: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null && !patchRoutesOnNavigationImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  // In SPA apps, if the user provided a patchRoutesOnNavigation implementation and\n  // our initial match is a splat route, clear them out so we run through lazy\n  // discovery on hydration in case there's a more accurate lazy route match.\n  // In SSR apps (with `hydrationData`), we expect that the server will send\n  // up the proper matched routes so we don't want to run lazy discovery on\n  // initial hydration and want to hydrate into the splat route.\n  if (initialMatches && !init.hydrationData) {\n    let fogOfWar = checkFogOfWar(\n      initialMatches,\n      dataRoutes,\n      init.history.location.pathname\n    );\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n\n  let initialized: boolean;\n  if (!initialMatches) {\n    initialized = false;\n    initialMatches = [];\n\n    // If partial hydration and fog of war is enabled, we will be running\n    // `patchRoutesOnNavigation` during hydration so include any partial matches as\n    // the initial matches so we can properly render `HydrateFallback`'s\n    if (future.v7_partialHydration) {\n      let fogOfWar = checkFogOfWar(\n        null,\n        dataRoutes,\n        init.history.location.pathname\n      );\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatches = fogOfWar.matches;\n      }\n    }\n  } else if (initialMatches.some((m) => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some((m) => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(\n        (m) => errors![m.route.id] !== undefined\n      );\n      initialized = initialMatches\n        .slice(0, idx + 1)\n        .every((m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    } else {\n      initialized = initialMatches.every(\n        (m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors)\n      );\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions: Map<string, Set<string>> = new Map<\n    string,\n    Set<string>\n  >();\n\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener: (() => void) | null = null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: Set<string> = new Set();\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map<string, number>();\n\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set<string>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Map of pending patchRoutesOnNavigation() promises (keyed by path/matches) so\n  // that we only kick them off once for a given combo\n  let pendingPatchRoutes = new Map<\n    string,\n    ReturnType<AgnosticPatchRoutesOnNavigationFunction>\n  >();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let unblockBlockerHistoryUpdate: (() => void) | undefined = undefined;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (unblockBlockerHistoryUpdate) {\n          unblockBlockerHistoryUpdate();\n          unblockBlockerHistoryUpdate = undefined;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          let nextHistoryUpdatePromise = new Promise<void>((resolve) => {\n            unblockBlockerHistoryUpdate = resolve;\n          });\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked, after the url\n              // restoration is also complete.  See:\n              // https://github.com/remix-run/react-router/issues/11613\n              nextHistoryUpdatePromise.then(() => init.history.go(delta));\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey!, IDLE_BLOCKER);\n              updateState({ blockers });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () =>\n        persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () =>\n        routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location, {\n        initialHydration: true,\n      });\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(\n    newState: Partial<RouterState>,\n    opts: {\n      flushSync?: boolean;\n      viewTransitionOpts?: ViewTransitionOpts;\n    } = {}\n  ): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers: string[] = [];\n    let deletedFetchersKeys: string[] = [];\n\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach((subscriber) =>\n      subscriber(state, {\n        deletedFetchers: deletedFetchersKeys,\n        viewTransitionOpts: opts.viewTransitionOpts,\n        flushSync: opts.flushSync === true,\n      })\n    );\n\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach((key) => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach((key) => deleteFetcher(key));\n    }\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>,\n    { flushSync }: { flushSync?: boolean } = {}\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    let viewTransitionOpts: ViewTransitionOpts | undefined;\n\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === HistoryAction.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location,\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location,\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set<string>([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location,\n      };\n    }\n\n    updateState(\n      {\n        ...newState, // matches, errors, fetchers go through as-is\n        actionData,\n        loaderData,\n        historyAction: pendingAction,\n        location,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        revalidation: \"idle\",\n        restoreScrollPosition: getSavedScrollPosition(\n          location,\n          newState.matches || state.matches\n        ),\n        preventScrollReset,\n        blockers,\n      },\n      {\n        viewTransitionOpts,\n        flushSync: flushSync === true,\n      }\n    );\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      future.v7_relativeSplatPath,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey!, IDLE_BLOCKER);\n          updateState({ blockers });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.viewTransition,\n      flushSync,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      {\n        overrideNavigation: state.navigation,\n        // Proxy through any rending view transition\n        enableViewTransition: pendingViewTransitionEnabled === true,\n      }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      initialHydration?: boolean;\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponseImpl;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n      enableViewTransition?: boolean;\n      flushSync?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let { error, notFoundMatches, route } = handleNavigational404(\n        location.pathname\n      );\n      completeNavigation(\n        location,\n        {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        },\n        { flushSync }\n      );\n      return;\n    }\n\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial hydration will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (\n      state.initialized &&\n      !isRevalidationRequired &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches }, { flushSync });\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionResult: PendingActionResult | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [\n        findNearestBoundary(matches).route.id,\n        { type: ResultType.error, error: opts.pendingError },\n      ];\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        fogOfWar.active,\n        { replace: opts.replace, flushSync }\n      );\n\n      if (actionResult.shortCircuited) {\n        return;\n      }\n\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (\n          isErrorResult(result) &&\n          isRouteErrorResponse(result.error) &&\n          result.error.status === 404\n        ) {\n          pendingNavigationController = null;\n\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error,\n            },\n          });\n          return;\n        }\n      }\n\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n\n      // Create a GET request for the loaders\n      request = createClientSideRequest(\n        init.history,\n        request.url,\n        request.signal\n      );\n    }\n\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors,\n    } = await handleLoaders(\n      request,\n      location,\n      matches,\n      fogOfWar.active,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      opts && opts.initialHydration === true,\n      flushSync,\n      pendingActionResult\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches: updatedMatches || matches,\n      ...getActionDataForCommit(pendingActionResult),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    opts: { replace?: boolean; flushSync?: boolean } = {}\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation }, { flushSync: opts.flushSync === true });\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches)\n          .route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [\n            boundaryId,\n            {\n              type: ResultType.error,\n              error: discoverResult.error,\n            },\n          ],\n        };\n      } else if (!discoverResult.matches) {\n        let { notFoundMatches, error, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [\n            route.id,\n            {\n              type: ResultType.error,\n              error,\n            },\n          ],\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        state,\n        request,\n        [actionMatch],\n        matches,\n        null\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(\n          result.response.headers.get(\"Location\")!,\n          new URL(request.url),\n          basename\n        );\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result],\n      };\n    }\n\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result],\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    initialHydration?: boolean,\n    flushSync?: boolean,\n    pendingActionResult?: PendingActionResult\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation =\n      overrideNavigation || getLoadingNavigation(location, submission);\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission ||\n      fetcherSubmission ||\n      getSubmissionFromNavigation(loadingNavigation);\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState =\n      !isUninterruptedRevalidation &&\n      (!future.v7_partialHydration || !initialHydration);\n\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(\n          {\n            navigation: loadingNavigation,\n            ...(actionData !== undefined ? { actionData } : {}),\n          },\n          {\n            flushSync,\n          }\n        );\n      }\n\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches)\n          .route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: discoverResult.error,\n          },\n        };\n      } else if (!discoverResult.matches) {\n        let { error, notFoundMatches, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      future.v7_partialHydration && initialHydration === true,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      pendingActionResult\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(\n        location,\n        {\n          matches,\n          loaderData: {},\n          // Commit pending error if we're short circuiting\n          errors:\n            pendingActionResult && isErrorResult(pendingActionResult[1])\n              ? { [pendingActionResult[0]]: pendingActionResult[1].error }\n              : null,\n          ...getActionDataForCommit(pendingActionResult),\n          ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n        },\n        { flushSync }\n      );\n      return { shortCircuited: true };\n    }\n\n    if (shouldUpdateNavigationState) {\n      let updates: Partial<RouterState> = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, { flushSync });\n    }\n\n    revalidatingFetchers.forEach((rf) => {\n      abortFetcher(rf.key);\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      pendingActionResult,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    // Preserve SSR errors during partial hydration\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      errors = { ...state.errors, ...errors };\n    }\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      matches,\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  function getUpdatedActionData(\n    pendingActionResult: PendingActionResult | undefined\n  ): Record<string, RouteData> | null | undefined {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data as any,\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n\n  function getUpdatedRevalidatingFetchers(\n    revalidatingFetchers: RevalidatingFetcher[]\n  ) {\n    revalidatingFetchers.forEach((rf) => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(\n        undefined,\n        fetcher ? fetcher.data : undefined\n      );\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    abortFetcher(key);\n\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      future.v7_relativeSplatPath,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath }),\n        { flushSync }\n      );\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n\n    if (error) {\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n\n    let match = getTargetMatch(matches, path);\n\n    let preventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(\n        key,\n        routeId,\n        path,\n        match,\n        matches,\n        fogOfWar.active,\n        flushSync,\n        preventScrollReset,\n        submission\n      );\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(\n      key,\n      routeId,\n      path,\n      match,\n      matches,\n      fogOfWar.active,\n      flushSync,\n      preventScrollReset,\n      submission\n    );\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    preventScrollReset: boolean,\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    function detectAndHandle405Error(m: AgnosticDataRouteMatch) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId,\n        });\n        setFetcherError(key, routeId, error, { flushSync });\n        return true;\n      }\n      return false;\n    }\n\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync,\n    });\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        requestMatches,\n        path,\n        fetchRequest.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\n      \"action\",\n      state,\n      fetchRequest,\n      [match],\n      requestMatches,\n      key\n    );\n    let actionResult = actionResults[match.route.id];\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n            preventScrollReset,\n          });\n        }\n      }\n\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      false,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      [match.route.id, actionResult]\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          existingFetcher ? existingFetcher.data : undefined\n        );\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        abortFetcher(staleKey);\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n\n    abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        fetchers: new Map(state.fetchers),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    preventScrollReset: boolean,\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(\n      key,\n      getLoadingFetcher(\n        submission,\n        existingFetcher ? existingFetcher.data : undefined\n      ),\n      { flushSync }\n    );\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        path,\n        fetchRequest.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\n      \"loader\",\n      state,\n      fetchRequest,\n      [match],\n      matches,\n      key\n    );\n    let result = results[match.route.id];\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false, {\n          preventScrollReset,\n        });\n        return;\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    request: Request,\n    redirect: RedirectResult,\n    isNavigation: boolean,\n    {\n      submission,\n      fetcherSubmission,\n      preventScrollReset,\n      replace,\n    }: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n    } = {}\n  ) {\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(\n      location,\n      new URL(request.url),\n      basename\n    );\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true,\n    });\n\n    if (isBrowser) {\n      let isDocumentReload = false;\n\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n          // Hard reload if it's an absolute URL to a new origin\n          url.origin !== routerWindow.location.origin ||\n          // Hard reload if it's an absolute URL that does not match our basename\n          stripBasename(url.pathname, basename) == null;\n      }\n\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true || redirect.response.headers.has(\"X-Remix-Replace\")\n        ? HistoryAction.Replace\n        : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType } = state.navigation;\n    if (\n      !submission &&\n      !fetcherSubmission &&\n      formMethod &&\n      formAction &&\n      formEncType\n    ) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.response.status) &&\n      activeSubmission &&\n      isMutationMethod(activeSubmission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: location,\n        },\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        submission\n      );\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    }\n  }\n\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    state: RouterState,\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    fetcherKey: string | null\n  ): Promise<Record<string, DataResult>> {\n    let results: Record<string, DataStrategyResult>;\n    let dataResults: Record<string, DataResult> = {};\n    try {\n      results = await callDataStrategyImpl(\n        dataStrategyImpl,\n        type,\n        state,\n        request,\n        matchesToLoad,\n        matches,\n        fetcherKey,\n        manifest,\n        mapRouteProperties\n      );\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      matchesToLoad.forEach((m) => {\n        dataResults[m.route.id] = {\n          type: ResultType.error,\n          error: e,\n        };\n      });\n      return dataResults;\n    }\n\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result as Response;\n        dataResults[routeId] = {\n          type: ResultType.redirect,\n          response: normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            routeId,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          ),\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(\n          result\n        );\n      }\n    }\n\n    return dataResults;\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    state: RouterState,\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    let currentMatches = state.matches;\n\n    // Kick off loaders and fetchers in parallel\n    let loaderResultsPromise = callDataStrategy(\n      \"loader\",\n      state,\n      request,\n      matchesToLoad,\n      matches,\n      null\n    );\n\n    let fetcherResultsPromise = Promise.all(\n      fetchersToLoad.map(async (f) => {\n        if (f.matches && f.match && f.controller) {\n          let results = await callDataStrategy(\n            \"loader\",\n            state,\n            createClientSideRequest(init.history, f.path, f.controller.signal),\n            [f.match],\n            f.matches,\n            f.key\n          );\n          let result = results[f.match.route.id];\n          // Fetcher results are keyed by fetcher key from here on out, not routeId\n          return { [f.key]: result };\n        } else {\n          return Promise.resolve({\n            [f.key]: {\n              type: ResultType.error,\n              error: getInternalRouterError(404, {\n                pathname: f.path,\n              }),\n            } as ErrorResult,\n          });\n        }\n      })\n    );\n\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce(\n      (acc, r) => Object.assign(acc, r),\n      {}\n    );\n\n    await Promise.all([\n      resolveNavigationDeferredResults(\n        matches,\n        loaderResults,\n        request.signal,\n        currentMatches,\n        state.loaderData\n      ),\n      resolveFetcherDeferredResults(matches, fetcherResults, fetchersToLoad),\n    ]);\n\n    return {\n      loaderResults,\n      fetcherResults,\n    };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n      }\n      abortFetcher(key);\n    });\n  }\n\n  function updateFetcherState(\n    key: string,\n    fetcher: Fetcher,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    state.fetchers.set(key, fetcher);\n    updateState(\n      { fetchers: new Map(state.fetchers) },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function setFetcherError(\n    key: string,\n    routeId: string,\n    error: any,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState(\n      {\n        errors: {\n          [boundaryMatch.route.id]: error,\n        },\n        fetchers: new Map(state.fetchers),\n      },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    if (future.v7_fetcherPersist) {\n      activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n      // If this fetcher was previously marked for deletion, unmark it since we\n      // have a new instance\n      if (deletedFetchers.has(key)) {\n        deletedFetchers.delete(key);\n      }\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  function deleteFetcher(key: string): void {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (\n      fetchControllers.has(key) &&\n      !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))\n    ) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    deletedFetchers.delete(key);\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function deleteFetcherAndUpdateState(key: string): void {\n    if (future.v7_fetcherPersist) {\n      let count = (activeFetchers.get(key) || 0) - 1;\n      if (count <= 0) {\n        activeFetchers.delete(key);\n        deletedFetchers.add(key);\n      } else {\n        activeFetchers.set(key, count);\n      }\n    } else {\n      deleteFetcher(key);\n    }\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    if (controller) {\n      controller.abort();\n      fetchControllers.delete(key);\n    }\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function handleNavigational404(pathname: string) {\n    let error = getInternalRouterError(404, { pathname });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let { matches, route } = getShortCircuitMatches(routesToUse);\n\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n\n    return { notFoundMatches: matches, route, error };\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function getScrollKey(location: Location, matches: AgnosticDataRouteMatch[]) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(\n        location,\n        matches.map((m) => convertRouteMatchToUiMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function checkFogOfWar(\n    matches: AgnosticDataRouteMatch[] | null,\n    routesToUse: AgnosticDataRouteObject[],\n    pathname: string\n  ): { active: boolean; matches: AgnosticDataRouteMatch[] | null } {\n    if (patchRoutesOnNavigationImpl) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n          routesToUse,\n          pathname,\n          basename,\n          true\n        );\n\n        return { active: true, matches: fogMatches || [] };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          // If we matched a dynamic param or a splat, it might only be because\n          // we haven't yet discovered other routes that would match with a\n          // higher score.  Call patchRoutesOnNavigation just to be sure\n          let partialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n            routesToUse,\n            pathname,\n            basename,\n            true\n          );\n          return { active: true, matches: partialMatches };\n        }\n      }\n    }\n\n    return { active: false, matches: null };\n  }\n\n  type DiscoverRoutesSuccessResult = {\n    type: \"success\";\n    matches: AgnosticDataRouteMatch[] | null;\n  };\n  type DiscoverRoutesErrorResult = {\n    type: \"error\";\n    error: any;\n    partialMatches: AgnosticDataRouteMatch[];\n  };\n  type DiscoverRoutesAbortedResult = { type: \"aborted\" };\n  type DiscoverRoutesResult =\n    | DiscoverRoutesSuccessResult\n    | DiscoverRoutesErrorResult\n    | DiscoverRoutesAbortedResult;\n\n  async function discoverRoutes(\n    matches: AgnosticDataRouteMatch[],\n    pathname: string,\n    signal: AbortSignal\n  ): Promise<DiscoverRoutesResult> {\n    if (!patchRoutesOnNavigationImpl) {\n      return { type: \"success\", matches };\n    }\n\n    let partialMatches: AgnosticDataRouteMatch[] | null = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      let localManifest = manifest;\n      try {\n        await patchRoutesOnNavigationImpl({\n          path: pathname,\n          matches: partialMatches,\n          patch: (routeId, children) => {\n            if (signal.aborted) return;\n            patchRoutesImpl(\n              routeId,\n              children,\n              routesToUse,\n              localManifest,\n              mapRouteProperties\n            );\n          },\n        });\n      } catch (e) {\n        return { type: \"error\", error: e, partialMatches };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR && !signal.aborted) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n\n      if (signal.aborted) {\n        return { type: \"aborted\" };\n      }\n\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        return { type: \"success\", matches: newMatches };\n      }\n\n      let newPartialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n        routesToUse,\n        pathname,\n        basename,\n        true\n      );\n\n      // Avoid loops if the second pass results in the same partial matches\n      if (\n        !newPartialMatches ||\n        (partialMatches.length === newPartialMatches.length &&\n          partialMatches.every(\n            (m, i) => m.route.id === newPartialMatches![i].route.id\n          ))\n      ) {\n        return { type: \"success\", matches: null };\n      }\n\n      partialMatches = newPartialMatches;\n    }\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  function patchRoutes(\n    routeId: string | null,\n    children: AgnosticRouteObject[]\n  ): void {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(\n      routeId,\n      children,\n      routesToUse,\n      manifest,\n      mapRouteProperties\n    );\n\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface StaticHandlerFutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_throwAbortReason: boolean;\n}\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<StaticHandlerFutureConfig>;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future: StaticHandlerFutureConfig = {\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false,\n    ...(opts ? opts.future : null),\n  };\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(\n    request: Request,\n    {\n      requestContext,\n      skipLoaderErrorBubbling,\n      dataStrategy,\n    }: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      skipLoaderErrorBubbling === true,\n      null\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n      dataStrategy,\n    }: {\n      requestContext?: unknown;\n      routeId?: string;\n      dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      false,\n      match\n    );\n\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          dataStrategy,\n          skipLoaderErrorBubbling,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `DataStrategyResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        request,\n        [actionMatch],\n        matches,\n        isRouteRequest,\n        requestContext,\n        dataStrategy\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")!,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling\n        ? actionMatch\n        : findNearestBoundary(matches, actionMatch.route.id);\n\n      let context = await loadRouteData(\n        loaderRequest,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        null,\n        [boundaryMatch.route.id, result]\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : result.statusCode != null\n          ? result.statusCode\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    let context = await loadRouteData(\n      loaderRequest,\n      matches,\n      requestContext,\n      dataStrategy,\n      skipLoaderErrorBubbling,\n      null\n    );\n\n    return {\n      ...context,\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionHeaders: result.headers\n        ? { [actionMatch.route.id]: result.headers }\n        : {},\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null,\n    pendingActionResult?: PendingActionResult\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0])\n      : matches;\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors:\n          pendingActionResult && isErrorResult(pendingActionResult[1])\n            ? {\n                [pendingActionResult[0]]: pendingActionResult[1].error,\n              }\n            : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await callDataStrategy(\n      \"loader\",\n      request,\n      matchesToLoad,\n      matches,\n      isRouteRequest,\n      requestContext,\n      dataStrategy\n    );\n\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      results,\n      pendingActionResult,\n      activeDeferreds,\n      skipLoaderErrorBubbling\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    isRouteRequest: boolean,\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null\n  ): Promise<Record<string, DataResult>> {\n    let results = await callDataStrategyImpl(\n      dataStrategy || defaultDataStrategy,\n      type,\n      null,\n      request,\n      matchesToLoad,\n      matches,\n      null,\n      manifest,\n      mapRouteProperties,\n      requestContext\n    );\n\n    let dataResults: Record<string, DataResult> = {};\n    await Promise.all(\n      matches.map(async (match) => {\n        if (!(match.route.id in results)) {\n          return;\n        }\n        let result = results[match.route.id];\n        if (isRedirectDataStrategyResultResult(result)) {\n          let response = result.result as Response;\n          // Throw redirects and let the server handle them with an HTTP redirect\n          throw normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            match.route.id,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          );\n        }\n        if (isResponse(result.result) && isRouteRequest) {\n          // For SSR single-route requests, we want to hand Responses back\n          // directly without unwrapping\n          throw result;\n        }\n\n        dataResults[match.route.id] =\n          await convertDataStrategyResultToDataResult(result);\n      })\n    );\n    return dataResults;\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction throwStaticHandlerAbortedError(\n  request: Request,\n  isRouteRequest: boolean,\n  future: StaticHandlerFutureConfig\n) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(`${method}() call aborted: ${request.method} ${request.url}`);\n}\n\nfunction isSubmissionNavigation(\n  opts: BaseNavigateOrFetchOptions\n): opts is SubmissionNavigateOptions {\n  return (\n    opts != null &&\n    ((\"formData\" in opts && opts.formData != null) ||\n      (\"body\" in opts && opts.body !== undefined))\n  );\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  v7_relativeSplatPath: boolean,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getResolveToMatches(contextualMatches, v7_relativeSplatPath),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Account for `?index` params when routing to the current location\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch) {\n    let nakedIndex = hasNakedIndexQuery(path.search);\n    if (activeRouteMatch.route.index && !nakedIndex) {\n      // Add one when we're targeting an index route\n      path.search = path.search\n        ? path.search.replace(/^\\?/, \"?index&\")\n        : \"?index\";\n    } else if (!activeRouteMatch.route.index && nakedIndex) {\n      // Remove existing ones when we're not\n      let params = new URLSearchParams(path.search);\n      let indexValues = params.getAll(\"index\");\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: BaseNavigateOrFetchOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponseImpl;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" }),\n  });\n\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod\n    ? (rawFormMethod.toUpperCase() as V7_FormMethod)\n    : (rawFormMethod.toLowerCase() as FormMethod);\n  let formAction = stripHashFromPath(path);\n\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      let text =\n        typeof opts.body === \"string\"\n          ? opts.body\n          : opts.body instanceof FormData ||\n            opts.body instanceof URLSearchParams\n          ? // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n            Array.from(opts.body.entries()).reduce(\n              (acc, [name, value]) => `${acc}${name}=${value}\\n`,\n              \"\"\n            )\n          : String(opts.body);\n\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text,\n        },\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      try {\n        let json =\n          typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined,\n          },\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n\n  let searchParams: URLSearchParams;\n  let formData: FormData;\n\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n\n  let submission: Submission = {\n    formMethod,\n    formAction,\n    formEncType:\n      (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined,\n  };\n\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes at/below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId: string,\n  includeBoundary = false\n) {\n  let index = matches.findIndex((m) => m.route.id === boundaryId);\n  if (index >= 0) {\n    return matches.slice(0, includeBoundary ? index + 1 : index);\n  }\n  return matches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  initialHydration: boolean,\n  skipActionErrorRevalidation: boolean,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: Set<string>,\n  deletedFetchers: Set<string>,\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  fetchRedirectIds: Set<string>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionResult?: PendingActionResult\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingActionResult\n    ? isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : pendingActionResult[1].data\n    : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryMatches = matches;\n  if (initialHydration && state.errors) {\n    // On initial hydration, only consider matches up to _and including_ the boundary.\n    // This is inclusive to handle cases where a server loader ran successfully,\n    // a child server loader bubbled up to this route, but this route has\n    // `clientLoader.hydrate` so we want to still run the `clientLoader` so that\n    // we have a complete version of `loaderData`\n    boundaryMatches = getLoaderMatchesUntilBoundary(\n      matches,\n      Object.keys(state.errors)[0],\n      true\n    );\n  } else if (pendingActionResult && isErrorResult(pendingActionResult[1])) {\n    // If an action threw an error, we call loaders up to, but not including the\n    // boundary\n    boundaryMatches = getLoaderMatchesUntilBoundary(\n      matches,\n      pendingActionResult[0]\n    );\n  }\n\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult\n    ? pendingActionResult[1].statusCode\n    : undefined;\n  let shouldSkipRevalidation =\n    skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let { route } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n\n    if (route.loader == null) {\n      return false;\n    }\n\n    if (initialHydration) {\n      return shouldLoadRouteOnHydration(route, state.loaderData, state.errors);\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation\n        ? false\n        : // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n          isRevalidationRequired ||\n          currentUrl.pathname + currentUrl.search ===\n            nextUrl.pathname + nextUrl.search ||\n          // Search params affect all loaders\n          currentUrl.search !== nextUrl.search ||\n          isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial hydration (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (\n      initialHydration ||\n      !matches.some((m) => m.route.id === f.routeId) ||\n      deletedFetchers.has(key)\n    ) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.has(key)) {\n      // Always mark for revalidation if the fetcher was cancelled\n      cancelledFetcherLoads.delete(key);\n      shouldRevalidate = true;\n    } else if (\n      fetcher &&\n      fetcher.state !== \"idle\" &&\n      fetcher.data === undefined\n    ) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params,\n        ...submission,\n        actionResult,\n        actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation\n          ? false\n          : isRevalidationRequired,\n      });\n    }\n\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction shouldLoadRouteOnHydration(\n  route: AgnosticDataRouteObject,\n  loaderData: RouteData | null | undefined,\n  errors: RouteData | null | undefined\n) {\n  // We dunno if we have a loader - gotta find out!\n  if (route.lazy) {\n    return true;\n  }\n\n  // No loader, nothing to initialize\n  if (!route.loader) {\n    return false;\n  }\n\n  let hasData = loaderData != null && loaderData[route.id] !== undefined;\n  let hasError = errors != null && errors[route.id] !== undefined;\n\n  // Don't run if we error'd during SSR\n  if (!hasData && hasError) {\n    return false;\n  }\n\n  // Explicitly opting-in to running on hydration\n  if (typeof route.loader === \"function\" && route.loader.hydrate === true) {\n    return true;\n  }\n\n  // Otherwise, run if we're not yet initialized with anything\n  return !hasData && !hasError;\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: ShouldRevalidateFunctionArgs\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\nfunction patchRoutesImpl(\n  routeId: string | null,\n  children: AgnosticRouteObject[],\n  routesToUse: AgnosticDataRouteObject[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction\n) {\n  let childrenToPatch: AgnosticDataRouteObject[];\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(\n      route,\n      `No route found to patch children into: routeId = ${routeId}`\n    );\n    if (!route.children) {\n      route.children = [];\n    }\n    childrenToPatch = route.children;\n  } else {\n    childrenToPatch = routesToUse;\n  }\n\n  // Don't patch in routes we already know about so that `patch` is idempotent\n  // to simplify user-land code. This is useful because we re-call the\n  // `patchRoutesOnNavigation` function for matched routes with params.\n  let uniqueChildren = children.filter(\n    (newRoute) =>\n      !childrenToPatch.some((existingRoute) =>\n        isSameRoute(newRoute, existingRoute)\n      )\n  );\n\n  let newRoutes = convertRoutesToDataRoutes(\n    uniqueChildren,\n    mapRouteProperties,\n    [routeId || \"_\", \"patch\", String(childrenToPatch?.length || \"0\")],\n    manifest\n  );\n\n  childrenToPatch.push(...newRoutes);\n}\n\nfunction isSameRoute(\n  newRoute: AgnosticRouteObject,\n  existingRoute: AgnosticRouteObject\n): boolean {\n  // Most optimal check is by id\n  if (\n    \"id\" in newRoute &&\n    \"id\" in existingRoute &&\n    newRoute.id === existingRoute.id\n  ) {\n    return true;\n  }\n\n  // Second is by pathing differences\n  if (\n    !(\n      newRoute.index === existingRoute.index &&\n      newRoute.path === existingRoute.path &&\n      newRoute.caseSensitive === existingRoute.caseSensitive\n    )\n  ) {\n    return false;\n  }\n\n  // Pathless layout routes are trickier since we need to check children.\n  // If they have no children then they're the same as far as we can tell\n  if (\n    (!newRoute.children || newRoute.children.length === 0) &&\n    (!existingRoute.children || existingRoute.children.length === 0)\n  ) {\n    return true;\n  }\n\n  // Otherwise, we look to see if every child in the new route is already\n  // represented in the existing route's children\n  return newRoute.children!.every((aChild, i) =>\n    existingRoute.children?.some((bChild) => isSameRoute(aChild, bChild))\n  );\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nasync function defaultDataStrategy({\n  matches,\n}: DataStrategyFunctionArgs): ReturnType<DataStrategyFunction> {\n  let matchesToLoad = matches.filter((m) => m.shouldLoad);\n  let results = await Promise.all(matchesToLoad.map((m) => m.resolve()));\n  return results.reduce(\n    (acc, result, i) =>\n      Object.assign(acc, { [matchesToLoad[i].route.id]: result }),\n    {}\n  );\n}\n\nasync function callDataStrategyImpl(\n  dataStrategyImpl: DataStrategyFunction,\n  type: \"loader\" | \"action\",\n  state: RouterState | null,\n  request: Request,\n  matchesToLoad: AgnosticDataRouteMatch[],\n  matches: AgnosticDataRouteMatch[],\n  fetcherKey: string | null,\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  requestContext?: unknown\n): Promise<Record<string, DataStrategyResult>> {\n  let loadRouteDefinitionsPromises = matches.map((m) =>\n    m.route.lazy\n      ? loadLazyRouteModule(m.route, mapRouteProperties, manifest)\n      : undefined\n  );\n\n  let dsMatches = matches.map((match, i) => {\n    let loadRoutePromise = loadRouteDefinitionsPromises[i];\n    let shouldLoad = matchesToLoad.some((m) => m.route.id === match.route.id);\n    // `resolve` encapsulates route.lazy(), executing the loader/action,\n    // and mapping return values/thrown errors to a `DataStrategyResult`.  Users\n    // can pass a callback to take fine-grained control over the execution\n    // of the loader/action\n    let resolve: DataStrategyMatch[\"resolve\"] = async (handlerOverride) => {\n      if (\n        handlerOverride &&\n        request.method === \"GET\" &&\n        (match.route.lazy || match.route.loader)\n      ) {\n        shouldLoad = true;\n      }\n      return shouldLoad\n        ? callLoaderOrAction(\n            type,\n            request,\n            match,\n            loadRoutePromise,\n            handlerOverride,\n            requestContext\n          )\n        : Promise.resolve({ type: ResultType.data, result: undefined });\n    };\n\n    return {\n      ...match,\n      shouldLoad,\n      resolve,\n    };\n  });\n\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: dsMatches,\n    request,\n    params: matches[0].params,\n    fetcherKey,\n    context: requestContext,\n  });\n\n  // Wait for all routes to load here but 'swallow the error since we want\n  // it to bubble up from the `await loadRoutePromise` in `callLoaderOrAction` -\n  // called from `match.resolve()`\n  try {\n    await Promise.all(loadRouteDefinitionsPromises);\n  } catch (e) {\n    // No-op\n  }\n\n  return results;\n}\n\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  loadRoutePromise: Promise<void> | undefined,\n  handlerOverride: Parameters<DataStrategyMatch[\"resolve\"]>[0],\n  staticContext?: unknown\n): Promise<DataStrategyResult> {\n  let result: DataStrategyResult;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (\n    handler: AgnosticRouteObject[\"loader\"] | AgnosticRouteObject[\"action\"]\n  ): Promise<DataStrategyResult> => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    // This will never resolve so safe to type it as Promise<DataStrategyResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise<DataStrategyResult>((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n\n    let actualHandler = (ctx?: unknown) => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(\n          new Error(\n            `You cannot call the handler for a route which defines a boolean ` +\n              `\"${type}\" [routeId: ${match.route.id}]`\n          )\n        );\n      }\n      return handler(\n        {\n          request,\n          params: match.params,\n          context: staticContext,\n        },\n        ...(ctx !== undefined ? [ctx] : [])\n      );\n    };\n\n    let handlerPromise: Promise<DataStrategyResult> = (async () => {\n      try {\n        let val = await (handlerOverride\n          ? handlerOverride((ctx: unknown) => actualHandler(ctx))\n          : actualHandler());\n        return { type: \"data\", result: val };\n      } catch (e) {\n        return { type: \"error\", result: e };\n      }\n    })();\n\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    // If we have a route.lazy promise, await that first\n    if (loadRoutePromise) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n          // If the handler throws, don't let it immediately bubble out,\n          // since we need to let the lazy() execution finish so we know if this\n          // route has a boundary that can handle the error\n          runHandler(handler).catch((e) => {\n            handlerError = e;\n          }),\n          loadRoutePromise,\n        ]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value!;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadRoutePromise;\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, result: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result.result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // DataStrategyResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return { type: ResultType.error, result: e };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  return result;\n}\n\nasync function convertDataStrategyResultToDataResult(\n  dataStrategyResult: DataStrategyResult\n): Promise<DataResult> {\n  let { result, type } = dataStrategyResult;\n\n  if (isResponse(result)) {\n    let data: any;\n\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return { type: ResultType.error, error: e };\n    }\n\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (type === ResultType.error) {\n    if (isDataWithResponseInit(result)) {\n      if (result.data instanceof Error) {\n        return {\n          type: ResultType.error,\n          error: result.data,\n          statusCode: result.init?.status,\n        };\n      }\n\n      // Convert thrown data() to ErrorResponse instances\n      result = new ErrorResponseImpl(\n        result.init?.status || 500,\n        undefined,\n        result.data\n      );\n    }\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n    };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  if (isDataWithResponseInit(result)) {\n    return {\n      type: ResultType.data,\n      data: result.data,\n      statusCode: result.init?.status,\n      headers: result.init?.headers\n        ? new Headers(result.init.headers)\n        : undefined,\n    };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(\n  response: Response,\n  request: Request,\n  routeId: string,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  v7_relativeSplatPath: boolean\n) {\n  let location = response.headers.get(\"Location\");\n  invariant(\n    location,\n    \"Redirects returned/thrown from loaders/actions must have a Location header\"\n  );\n\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(\n      0,\n      matches.findIndex((m) => m.route.id === routeId) + 1\n    );\n    location = normalizeTo(\n      new URL(request.url),\n      trimmedMatches,\n      basename,\n      true,\n      location,\n      v7_relativeSplatPath\n    );\n    response.headers.set(\"Location\", location);\n  }\n\n  return response;\n}\n\nfunction normalizeRedirectLocation(\n  location: string,\n  currentUrl: URL,\n  basename: string\n): string {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\")\n      ? new URL(currentUrl.protocol + normalizedLocation)\n      : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (\n      formEncType === \"application/x-www-form-urlencoded\" &&\n      submission.formData\n    ) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n\n  return searchParams;\n}\n\nfunction convertSearchParamsToFormData(\n  searchParams: URLSearchParams\n): FormData {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  activeDeferreds: Map<string, DeferredData>,\n  skipLoaderErrorBubbling: boolean\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n  let pendingError =\n    pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : undefined;\n\n  // Process loader results into state.loaderData/state.errors\n  matches.forEach((match) => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (\n          result.statusCode != null &&\n          result.statusCode !== 200 &&\n          !foundError\n        ) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = { [pendingActionResult[0]]: pendingError };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: Record<string, DataResult>,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    results,\n    pendingActionResult,\n    activeDeferreds,\n    false // This method is only called client side so we always want to bubble\n  );\n\n  // Process results from our revalidating fetchers\n  revalidatingFetchers.forEach((rf) => {\n    let { key, match, controller } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\nfunction getActionDataForCommit(\n  pendingActionResult: PendingActionResult | undefined\n) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1])\n    ? {\n        // Clear out prior actionData on errors\n        actionData: {},\n      }\n    : {\n        actionData: {\n          [pendingActionResult[0]]: pendingActionResult[1].data,\n        },\n      };\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route =\n    routes.length === 1\n      ? routes[0]\n      : routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n          id: `__shim-error-route__`,\n        };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n    message,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\" | \"invalid-body\";\n    message?: string;\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponseImpl(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(\n  results: Record<string, DataResult>\n): { key: string; result: RedirectResult } | undefined {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return { key, result };\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isPromise<T = unknown>(val: unknown): val is Promise<T> {\n  return typeof val === \"object\" && val != null && \"then\" in val;\n}\n\nfunction isDataStrategyResult(result: unknown): result is DataStrategyResult {\n  return (\n    result != null &&\n    typeof result === \"object\" &&\n    \"type\" in result &&\n    \"result\" in result &&\n    (result.type === ResultType.data || result.type === ResultType.error)\n  );\n}\n\nfunction isRedirectDataStrategyResultResult(result: DataStrategyResult) {\n  return (\n    isResponse(result.result) && redirectStatusCodes.has(result.result.status)\n  );\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDataWithResponseInit(\n  value: any\n): value is DataWithResponseInit<unknown> {\n  return (\n    typeof value === \"object\" &&\n    value != null &&\n    \"type\" in value &&\n    \"data\" in value &&\n    \"init\" in value &&\n    value.type === \"DataWithResponseInit\"\n  );\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveNavigationDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  signal: AbortSignal,\n  currentMatches: AgnosticDataRouteMatch[],\n  currentLoaderData: RouteData\n) {\n  let entries = Object.entries(results);\n  for (let index = 0; index < entries.length; index++) {\n    let [routeId, result] = entries[index];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && isRevalidatingLoader) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, false).then((result) => {\n        if (result) {\n          results[routeId] = result;\n        }\n      });\n    }\n  }\n}\n\nasync function resolveFetcherDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  revalidatingFetchers: RevalidatingFetcher[]\n) {\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, routeId, controller } = revalidatingFetchers[index];\n    let result = results[key];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    if (isDeferredResult(result)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      invariant(\n        controller,\n        \"Expected an AbortController for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, controller.signal, true).then(\n        (result) => {\n          if (result) {\n            results[key] = result;\n          }\n        }\n      );\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n\nfunction getSubmissionFromNavigation(\n  navigation: Navigation\n): Submission | undefined {\n  let { formMethod, formAction, formEncType, text, formData, json } =\n    navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text,\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined,\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined,\n    };\n  }\n}\n\nfunction getLoadingNavigation(\n  location: Location,\n  submission?: Submission\n): NavigationStates[\"Loading\"] {\n  if (submission) {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n    };\n    return navigation;\n  } else {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n    };\n    return navigation;\n  }\n}\n\nfunction getSubmittingNavigation(\n  location: Location,\n  submission: Submission\n): NavigationStates[\"Submitting\"] {\n  let navigation: NavigationStates[\"Submitting\"] = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n  };\n  return navigation;\n}\n\nfunction getLoadingFetcher(\n  submission?: Submission,\n  data?: Fetcher[\"data\"]\n): FetcherStates[\"Loading\"] {\n  if (submission) {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n    };\n    return fetcher;\n  } else {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n    };\n    return fetcher;\n  }\n}\n\nfunction getSubmittingFetcher(\n  submission: Submission,\n  existingFetcher?: Fetcher\n): FetcherStates[\"Submitting\"] {\n  let fetcher: FetcherStates[\"Submitting\"] = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n  };\n  return fetcher;\n}\n\nfunction getDoneFetcher(data: Fetcher[\"data\"]): FetcherStates[\"Idle\"] {\n  let fetcher: FetcherStates[\"Idle\"] = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n  };\n  return fetcher;\n}\n\nfunction restoreAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(\n      TRANSITIONS_STORAGE_KEY\n    );\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\n\nfunction persistAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  if (transitions.size > 0) {\n    let json: Record<string, string[]> = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(\n        TRANSITIONS_STORAGE_KEY,\n        JSON.stringify(json)\n      );\n    } catch (error) {\n      warning(\n        false,\n        `Failed to save applied view transitions in sessionStorage (${error}).`\n      );\n    }\n  }\n}\n//#endregion\n"], "names": ["Action", "PopStateEventType", "createMemoryHistory", "options", "initialEntries", "initialIndex", "v5Compat", "entries", "map", "entry", "index", "createMemoryLocation", "state", "undefined", "clampIndex", "length", "action", "Pop", "listener", "n", "Math", "min", "max", "getCurrentLocation", "to", "key", "location", "createLocation", "pathname", "warning", "char<PERSON>t", "JSON", "stringify", "createHref", "createPath", "history", "createURL", "URL", "encodeLocation", "path", "parsePath", "search", "hash", "push", "<PERSON><PERSON>", "nextLocation", "splice", "delta", "replace", "Replace", "go", "nextIndex", "listen", "fn", "createBrowserHistory", "createBrowserLocation", "window", "globalHistory", "usr", "createBrowserHref", "getUrlBasedHistory", "createHashHistory", "createHashLocation", "substr", "startsWith", "createHashHref", "base", "document", "querySelector", "href", "getAttribute", "url", "hashIndex", "indexOf", "slice", "validateHashLocation", "invariant", "value", "message", "Error", "cond", "console", "warn", "e", "create<PERSON><PERSON>", "random", "toString", "getHistoryState", "idx", "current", "_extends", "_ref", "parsed<PERSON><PERSON>", "searchIndex", "getLocation", "validateLocation", "defaultView", "getIndex", "replaceState", "handlePop", "historyState", "pushState", "error", "DOMException", "name", "assign", "origin", "addEventListener", "removeEventListener", "ResultType", "immutableRouteKeys", "Set", "isIndexRoute", "route", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "treePath", "String", "id", "join", "children", "indexRoute", "pathOrLayoutRoute", "matchRoutes", "locationArg", "basename", "matchRoutesImpl", "allowPartial", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "decoded", "decodePath", "matchRouteBranch", "convertRouteMatchToUiMatch", "match", "loaderData", "params", "data", "handle", "parents<PERSON>eta", "flattenRoute", "relativePath", "meta", "caseSensitive", "childrenIndex", "joinPaths", "routesMeta", "concat", "score", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "sort", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "matchPath", "Object", "pathnameBase", "normalizePathname", "generatePath", "originalPath", "prefix", "p", "array", "isLastSegment", "star", "keyMatch", "optional", "param", "pattern", "matcher", "compiledParams", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "regexpSource", "_", "RegExp", "v", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "getResolveToMatches", "v7_relativeSplatPath", "pathMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "getToPathname", "paths", "json", "init", "responseInit", "status", "headers", "Headers", "has", "set", "Response", "DataWithResponseInit", "constructor", "type", "Aborted<PERSON>eferredError", "DeferredData", "pendingKeysSet", "subscribers", "deferred<PERSON><PERSON><PERSON>", "Array", "isArray", "reject", "abortPromise", "Promise", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "acc", "_ref2", "trackPromise", "done", "add", "promise", "race", "then", "onSettle", "catch", "defineProperty", "get", "aborted", "delete", "undefinedError", "emit", "<PERSON><PERSON><PERSON>", "subscriber", "subscribe", "cancel", "abort", "k", "resolveData", "resolve", "size", "unwrappedData", "_ref3", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "isTrackedPromise", "_tracked", "_error", "_data", "defer", "redirect", "redirectDocument", "response", "ErrorResponseImpl", "statusText", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "formMethod", "formAction", "formEncType", "formData", "text", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "ABSOLUTE_URL_REGEX", "defaultMapRouteProperties", "hasErrorBou<PERSON>ry", "Boolean", "TRANSITIONS_STORAGE_KEY", "createRouter", "routerWindow", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "isServer", "detectErrorBoundary", "dataRoutes", "inFlightDataRoutes", "dataStrategyImpl", "dataStrategy", "defaultDataStrategy", "patchRoutesOnNavigationImpl", "patchRoutesOnNavigation", "future", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_partialHydration", "v7_prependBasename", "v7_skipActionErrorRevalidation", "unlistenHistory", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "initialErrors", "getInternalRouterError", "getShortCircuitMatches", "fogOfWar", "checkFogOfWar", "active", "initialized", "m", "lazy", "loader", "errors", "findIndex", "shouldLoadRouteOnHydration", "router", "historyAction", "navigation", "restoreScrollPosition", "preventScrollReset", "revalidation", "actionData", "fetchers", "Map", "blockers", "pendingAction", "HistoryAction", "pendingPreventScrollReset", "pendingNavigationController", "pendingViewTransitionEnabled", "appliedViewTransitions", "removePageHideEventListener", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeFetchers", "deletedFetchers", "activeDeferreds", "blockerFunctions", "unblockBlockerHistoryUpdate", "initialize", "blockerKey", "shouldBlockNavigation", "currentLocation", "nextHistoryUpdatePromise", "updateBlocker", "updateState", "startNavigation", "restoreAppliedTransitions", "_saveAppliedTransitions", "persistAppliedTransitions", "initialHydration", "dispose", "clear", "deleteFetcher", "deleteBlocker", "newState", "opts", "completedFetchers", "deletedFetchersKeys", "fetcher", "viewTransitionOpts", "flushSync", "completeNavigation", "_temp", "_location$state", "_location$state2", "isActionReload", "isMutationMethod", "_isRedirect", "keys", "mergeLoaderData", "priorPaths", "toPaths", "getSavedScrollPosition", "navigate", "normalizedPath", "normalizeTo", "fromRouteId", "relative", "submission", "normalizeNavigateOptions", "userReplace", "pendingError", "enableViewTransition", "viewTransition", "revalidate", "interruptActiveLoads", "startUninterruptedRevalidation", "overrideNavigation", "saveScrollPosition", "routesToUse", "loadingNavigation", "notFoundMatches", "handleNavigational404", "isHashChangeOnly", "request", "createClientSideRequest", "pendingActionResult", "findNearestBoundary", "actionResult", "handleAction", "shortCircuited", "routeId", "isErrorResult", "getLoadingNavigation", "updatedMatches", "handleLoaders", "fetcherSubmission", "getActionDataForCommit", "isFogOfWar", "getSubmittingNavigation", "discoverResult", "discoverRoutes", "boundaryId", "partialMatch<PERSON>", "actionMatch", "getTargetMatch", "method", "results", "callDataStrategy", "isRedirectResult", "normalizeRedirectLocation", "startRedirectNavigation", "isDeferredResult", "boundaryMatch", "activeSubmission", "getSubmissionFromNavigation", "shouldUpdateNavigationState", "getUpdatedActionData", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "cancelActiveDeferreds", "updatedFetchers", "markFetchRedirectsDone", "updates", "getUpdatedRevalidatingFetchers", "rf", "abort<PERSON><PERSON><PERSON>", "abortPendingFetchRevalidations", "f", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "findRedirect", "processLoaderData", "deferredData", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "revalidatingFetcher", "getLoadingFetcher", "fetch", "setFetcherError", "handleFetcherAction", "handleFetcherLoader", "requestMatches", "detectAndHandle405Error", "existingFetcher", "updateFetcherState", "getSubmittingFetcher", "abortController", "fetchRequest", "originatingLoadId", "actionResults", "getDoneFetcher", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "done<PERSON>etcher", "resolveDeferredData", "isNavigation", "_temp2", "redirectLocation", "isDocumentReload", "redirectHistoryAction", "fetcher<PERSON>ey", "dataResults", "callDataStrategyImpl", "isRedirectDataStrategyResultResult", "normalizeRelativeRoutingRedirectResponse", "convertDataStrategyResultToDataResult", "fetchersToLoad", "currentMatches", "loaderResultsPromise", "fetcherResultsPromise", "all", "resolveNavigationDeferredResults", "resolveFetcherDeferredResults", "getFetcher", "deleteFetcherAndUpdateState", "count", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "get<PERSON><PERSON>er", "blocker", "newBlocker", "blockerFunction", "predicate", "cancelledRouteIds", "dfd", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "y", "getScrollKey", "fogMatches", "isNonHMR", "localManifest", "patch", "patchRoutesImpl", "newMatches", "newPartialMatches", "_internalSetRoutes", "newRoutes", "patchRoutes", "_internalFetchControllers", "_internalActiveDeferreds", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "createStaticHandler", "v7_throwAbortReason", "query", "_temp3", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "isValidMethod", "methodNotAllowedMatches", "statusCode", "loaderHeaders", "actionHeaders", "queryImpl", "isResponse", "queryRoute", "_temp4", "find", "values", "_result$activeDeferre", "routeMatch", "submit", "loadRouteData", "isDataStrategyResult", "isRedirectResponse", "isRouteRequest", "throwStaticHandlerAbortedError", "Location", "loaderRequest", "Request", "context", "getLoaderMatchesUntilBoundary", "processRouteLoaderData", "executedLoaders", "fromEntries", "getStaticContextFromError", "newContext", "_deepestRenderedBoundaryId", "reason", "isSubmissionNavigation", "body", "prependBasename", "contextualMatches", "activeRouteMatch", "nakedIndex", "hasNakedIndexQuery", "URLSearchParams", "indexValues", "getAll", "append", "qs", "normalizeFormMethod", "isFetcher", "getInvalidBodyError", "rawFormMethod", "toUpperCase", "stripHashFromPath", "FormData", "parse", "searchParams", "convertFormDataToSearchParams", "convertSearchParamsToFormData", "includeBoundary", "skipActionErrorRevalidation", "currentUrl", "nextUrl", "boundaryMatches", "actionStatus", "shouldSkipRevalidation", "navigationMatches", "is<PERSON>ew<PERSON><PERSON>der", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "currentParams", "nextParams", "defaultShouldRevalidate", "isNewRouteInstance", "fetcherMatches", "fetcherMatch", "shouldRevalidate", "hasData", "<PERSON><PERSON><PERSON><PERSON>", "hydrate", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "currentPath", "loaderMatch", "arg", "routeChoice", "_childrenToPatch", "childrenToPatch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newRoute", "existingRoute", "isSameRoute", "<PERSON><PERSON><PERSON><PERSON>", "_existingRoute$childr", "b<PERSON><PERSON><PERSON>", "loadLazyRouteModule", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "staticRouteValue", "isPropertyStaticallyDefined", "_ref4", "shouldLoad", "loadRouteDefinitionsPromises", "dsMatches", "loadRoutePromise", "handlerOverride", "callLoaderOrAction", "staticContext", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "handlerPromise", "val", "handlerError", "dataStrategyResult", "contentType", "isDataWithResponseInit", "_result$init2", "_result$init", "isDeferredData", "_result$init3", "_result$init4", "deferred", "_result$init5", "_result$init6", "trimmedMatches", "normalizedLocation", "protocol", "isSameBasename", "found<PERSON><PERSON>r", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "eligibleMatches", "reverse", "_temp5", "errorMessage", "isRevalidatingLoader", "unwrap", "_window", "transitions", "sessionPositions", "sessionStorage", "getItem", "setItem"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;AAEG;IACSA,OAsBX;AAtBD,CAAA,UAAYA,MAAM,EAAA;AAChB;;;;;;AAMG;AACHA,EAAAA,MAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AAEX;;;;AAIG;AACHA,EAAAA,MAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AAEb;;;AAGG;AACHA,EAAAA,MAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACrB,CAAC,EAtBWA,MAAM,KAANA,MAAM,GAsBjB,EAAA,CAAA,CAAA,CAAA;AAqKD,MAAMC,iBAAiB,GAAG,UAAU,CAAA;AA+BpC;;;AAGG;AACa,SAAAC,mBAAmBA,CACjCC,OAAA,EAAkC;AAAA,EAAA,IAAlCA,OAAA,KAAA,KAAA,CAAA,EAAA;IAAAA,OAAA,GAAgC,EAAE,CAAA;AAAA,GAAA;EAElC,IAAI;IAAEC,cAAc,GAAG,CAAC,GAAG,CAAC;IAAEC,YAAY;AAAEC,IAAAA,QAAQ,GAAG,KAAA;AAAO,GAAA,GAAGH,OAAO,CAAA;EACxE,IAAII,OAAmB,CAAC;AACxBA,EAAAA,OAAO,GAAGH,cAAc,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KACxCC,oBAAoB,CAClBF,KAAK,EACL,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAI,GAAGA,KAAK,CAACG,KAAK,EAC9CF,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGG,SAAS,CACpC,CACF,CAAA;AACD,EAAA,IAAIH,KAAK,GAAGI,UAAU,CACpBT,YAAY,IAAI,IAAI,GAAGE,OAAO,CAACQ,MAAM,GAAG,CAAC,GAAGV,YAAY,CACzD,CAAA;AACD,EAAA,IAAIW,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;EACvB,IAAIC,QAAQ,GAAoB,IAAI,CAAA;EAEpC,SAASJ,UAAUA,CAACK,CAAS,EAAA;AAC3B,IAAA,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,CAAC,EAAE,CAAC,CAAC,EAAEZ,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,CAAA;AACrD,GAAA;EACA,SAASQ,kBAAkBA,GAAA;IACzB,OAAOhB,OAAO,CAACG,KAAK,CAAC,CAAA;AACvB,GAAA;AACA,EAAA,SAASC,oBAAoBA,CAC3Ba,EAAM,EACNZ,KAAa,EACba,GAAY,EAAA;AAAA,IAAA,IADZb,KAAa,KAAA,KAAA,CAAA,EAAA;AAAbA,MAAAA,KAAa,GAAA,IAAI,CAAA;AAAA,KAAA;AAGjB,IAAA,IAAIc,QAAQ,GAAGC,cAAc,CAC3BpB,OAAO,GAAGgB,kBAAkB,EAAE,CAACK,QAAQ,GAAG,GAAG,EAC7CJ,EAAE,EACFZ,KAAK,EACLa,GAAG,CACJ,CAAA;AACDI,IAAAA,OAAO,CACLH,QAAQ,CAACE,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,+DACwBC,IAAI,CAACC,SAAS,CACvER,EAAE,CACD,CACJ,CAAA;AACD,IAAA,OAAOE,QAAQ,CAAA;AACjB,GAAA;EAEA,SAASO,UAAUA,CAACT,EAAM,EAAA;IACxB,OAAO,OAAOA,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;AACrD,GAAA;AAEA,EAAA,IAAIW,OAAO,GAAkB;IAC3B,IAAIzB,KAAKA,GAAA;AACP,MAAA,OAAOA,KAAK,CAAA;KACb;IACD,IAAIM,MAAMA,GAAA;AACR,MAAA,OAAOA,MAAM,CAAA;KACd;IACD,IAAIU,QAAQA,GAAA;MACV,OAAOH,kBAAkB,EAAE,CAAA;KAC5B;IACDU,UAAU;IACVG,SAASA,CAACZ,EAAE,EAAA;MACV,OAAO,IAAIa,GAAG,CAACJ,UAAU,CAACT,EAAE,CAAC,EAAE,kBAAkB,CAAC,CAAA;KACnD;IACDc,cAAcA,CAACd,EAAM,EAAA;AACnB,MAAA,IAAIe,IAAI,GAAG,OAAOf,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,CAAA;MACtD,OAAO;AACLI,QAAAA,QAAQ,EAAEW,IAAI,CAACX,QAAQ,IAAI,EAAE;AAC7Ba,QAAAA,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,EAAE;AACzBC,QAAAA,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAA;OACpB,CAAA;KACF;AACDC,IAAAA,IAAIA,CAACnB,EAAE,EAAEZ,KAAK,EAAA;MACZI,MAAM,GAAGhB,MAAM,CAAC4C,IAAI,CAAA;AACpB,MAAA,IAAIC,YAAY,GAAGlC,oBAAoB,CAACa,EAAE,EAAEZ,KAAK,CAAC,CAAA;AAClDF,MAAAA,KAAK,IAAI,CAAC,CAAA;MACVH,OAAO,CAACuC,MAAM,CAACpC,KAAK,EAAEH,OAAO,CAACQ,MAAM,EAAE8B,YAAY,CAAC,CAAA;MACnD,IAAIvC,QAAQ,IAAIY,QAAQ,EAAE;AACxBA,QAAAA,QAAQ,CAAC;UAAEF,MAAM;AAAEU,UAAAA,QAAQ,EAAEmB,YAAY;AAAEE,UAAAA,KAAK,EAAE,CAAA;AAAC,SAAE,CAAC,CAAA;AACvD,OAAA;KACF;AACDC,IAAAA,OAAOA,CAACxB,EAAE,EAAEZ,KAAK,EAAA;MACfI,MAAM,GAAGhB,MAAM,CAACiD,OAAO,CAAA;AACvB,MAAA,IAAIJ,YAAY,GAAGlC,oBAAoB,CAACa,EAAE,EAAEZ,KAAK,CAAC,CAAA;AAClDL,MAAAA,OAAO,CAACG,KAAK,CAAC,GAAGmC,YAAY,CAAA;MAC7B,IAAIvC,QAAQ,IAAIY,QAAQ,EAAE;AACxBA,QAAAA,QAAQ,CAAC;UAAEF,MAAM;AAAEU,UAAAA,QAAQ,EAAEmB,YAAY;AAAEE,UAAAA,KAAK,EAAE,CAAA;AAAC,SAAE,CAAC,CAAA;AACvD,OAAA;KACF;IACDG,EAAEA,CAACH,KAAK,EAAA;MACN/B,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;AACnB,MAAA,IAAIkC,SAAS,GAAGrC,UAAU,CAACJ,KAAK,GAAGqC,KAAK,CAAC,CAAA;AACzC,MAAA,IAAIF,YAAY,GAAGtC,OAAO,CAAC4C,SAAS,CAAC,CAAA;AACrCzC,MAAAA,KAAK,GAAGyC,SAAS,CAAA;AACjB,MAAA,IAAIjC,QAAQ,EAAE;AACZA,QAAAA,QAAQ,CAAC;UAAEF,MAAM;AAAEU,UAAAA,QAAQ,EAAEmB,YAAY;AAAEE,UAAAA,KAAAA;AAAO,SAAA,CAAC,CAAA;AACpD,OAAA;KACF;IACDK,MAAMA,CAACC,EAAY,EAAA;AACjBnC,MAAAA,QAAQ,GAAGmC,EAAE,CAAA;AACb,MAAA,OAAO,MAAK;AACVnC,QAAAA,QAAQ,GAAG,IAAI,CAAA;OAChB,CAAA;AACH,KAAA;GACD,CAAA;AAED,EAAA,OAAOiB,OAAO,CAAA;AAChB,CAAA;AAkBA;;;;;;AAMG;AACa,SAAAmB,oBAAoBA,CAClCnD,OAAA,EAAmC;AAAA,EAAA,IAAnCA,OAAA,KAAA,KAAA,CAAA,EAAA;IAAAA,OAAA,GAAiC,EAAE,CAAA;AAAA,GAAA;AAEnC,EAAA,SAASoD,qBAAqBA,CAC5BC,MAAc,EACdC,aAAgC,EAAA;IAEhC,IAAI;MAAE7B,QAAQ;MAAEa,MAAM;AAAEC,MAAAA,IAAAA;KAAM,GAAGc,MAAM,CAAC9B,QAAQ,CAAA;IAChD,OAAOC,cAAc,CACnB,EAAE,EACF;MAAEC,QAAQ;MAAEa,MAAM;AAAEC,MAAAA,IAAAA;KAAM;AAC1B;IACCe,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAAC8C,GAAG,IAAK,IAAI,EACvDD,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAACa,GAAG,IAAK,SAAS,CAC9D,CAAA;AACH,GAAA;AAEA,EAAA,SAASkC,iBAAiBA,CAACH,MAAc,EAAEhC,EAAM,EAAA;IAC/C,OAAO,OAAOA,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;AACrD,GAAA;EAEA,OAAOoC,kBAAkB,CACvBL,qBAAqB,EACrBI,iBAAiB,EACjB,IAAI,EACJxD,OAAO,CACR,CAAA;AACH,CAAA;AAsBA;;;;;;;AAOG;AACa,SAAA0D,iBAAiBA,CAC/B1D,OAAA,EAAgC;AAAA,EAAA,IAAhCA,OAAA,KAAA,KAAA,CAAA,EAAA;IAAAA,OAAA,GAA8B,EAAE,CAAA;AAAA,GAAA;AAEhC,EAAA,SAAS2D,kBAAkBA,CACzBN,MAAc,EACdC,aAAgC,EAAA;IAEhC,IAAI;AACF7B,MAAAA,QAAQ,GAAG,GAAG;AACda,MAAAA,MAAM,GAAG,EAAE;AACXC,MAAAA,IAAI,GAAG,EAAA;AAAE,KACV,GAAGF,SAAS,CAACgB,MAAM,CAAC9B,QAAQ,CAACgB,IAAI,CAACqB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI,CAACnC,QAAQ,CAACoC,UAAU,CAAC,GAAG,CAAC,IAAI,CAACpC,QAAQ,CAACoC,UAAU,CAAC,GAAG,CAAC,EAAE;MAC1DpC,QAAQ,GAAG,GAAG,GAAGA,QAAQ,CAAA;AAC1B,KAAA;IAED,OAAOD,cAAc,CACnB,EAAE,EACF;MAAEC,QAAQ;MAAEa,MAAM;AAAEC,MAAAA,IAAAA;KAAM;AAC1B;IACCe,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAAC8C,GAAG,IAAK,IAAI,EACvDD,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAACa,GAAG,IAAK,SAAS,CAC9D,CAAA;AACH,GAAA;AAEA,EAAA,SAASwC,cAAcA,CAACT,MAAc,EAAEhC,EAAM,EAAA;IAC5C,IAAI0C,IAAI,GAAGV,MAAM,CAACW,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAAA;IAChD,IAAIC,IAAI,GAAG,EAAE,CAAA;IAEb,IAAIH,IAAI,IAAIA,IAAI,CAACI,YAAY,CAAC,MAAM,CAAC,EAAE;AACrC,MAAA,IAAIC,GAAG,GAAGf,MAAM,CAAC9B,QAAQ,CAAC2C,IAAI,CAAA;AAC9B,MAAA,IAAIG,SAAS,GAAGD,GAAG,CAACE,OAAO,CAAC,GAAG,CAAC,CAAA;AAChCJ,MAAAA,IAAI,GAAGG,SAAS,KAAK,CAAC,CAAC,GAAGD,GAAG,GAAGA,GAAG,CAACG,KAAK,CAAC,CAAC,EAAEF,SAAS,CAAC,CAAA;AACxD,KAAA;AAED,IAAA,OAAOH,IAAI,GAAG,GAAG,IAAI,OAAO7C,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAC,CAAA;AACpE,GAAA;AAEA,EAAA,SAASmD,oBAAoBA,CAACjD,QAAkB,EAAEF,EAAM,EAAA;AACtDK,IAAAA,OAAO,CACLH,QAAQ,CAACE,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAA,4DAAA,GAC0BC,IAAI,CAACC,SAAS,CACzER,EAAE,CACH,MAAG,CACL,CAAA;AACH,GAAA;EAEA,OAAOoC,kBAAkB,CACvBE,kBAAkB,EAClBG,cAAc,EACdU,oBAAoB,EACpBxE,OAAO,CACR,CAAA;AACH,CAAA;AAegB,SAAAyE,SAASA,CAACC,KAAU,EAAEC,OAAgB,EAAA;AACpD,EAAA,IAAID,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;AACrE,IAAA,MAAM,IAAIE,KAAK,CAACD,OAAO,CAAC,CAAA;AACzB,GAAA;AACH,CAAA;AAEgB,SAAAjD,OAAOA,CAACmD,IAAS,EAAEF,OAAe,EAAA;EAChD,IAAI,CAACE,IAAI,EAAE;AACT;IACA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAEA,OAAO,CAACC,IAAI,CAACJ,OAAO,CAAC,CAAA;IAEzD,IAAI;AACF;AACA;AACA;AACA;AACA;AACA,MAAA,MAAM,IAAIC,KAAK,CAACD,OAAO,CAAC,CAAA;AACxB;AACD,KAAA,CAAC,OAAOK,CAAC,EAAE,EAAE;AACf,GAAA;AACH,CAAA;AAEA,SAASC,SAASA,GAAA;AAChB,EAAA,OAAOhE,IAAI,CAACiE,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACvB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAChD,CAAA;AAEA;;AAEG;AACH,SAASwB,eAAeA,CAAC7D,QAAkB,EAAEhB,KAAa,EAAA;EACxD,OAAO;IACLgD,GAAG,EAAEhC,QAAQ,CAACd,KAAK;IACnBa,GAAG,EAAEC,QAAQ,CAACD,GAAG;AACjB+D,IAAAA,GAAG,EAAE9E,KAAAA;GACN,CAAA;AACH,CAAA;AAEA;;AAEG;AACG,SAAUiB,cAAcA,CAC5B8D,OAA0B,EAC1BjE,EAAM,EACNZ,KAAA,EACAa,GAAY,EAAA;AAAA,EAAA,IADZb,KAAA,KAAA,KAAA,CAAA,EAAA;AAAAA,IAAAA,KAAA,GAAa,IAAI,CAAA;AAAA,GAAA;EAGjB,IAAIc,QAAQ,GAAAgE,QAAA,CAAA;IACV9D,QAAQ,EAAE,OAAO6D,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,CAAC7D,QAAQ;AAClEa,IAAAA,MAAM,EAAE,EAAE;AACVC,IAAAA,IAAI,EAAE,EAAA;GACF,EAAA,OAAOlB,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,EAAA;IAC/CZ,KAAK;AACL;AACA;AACA;AACA;IACAa,GAAG,EAAGD,EAAE,IAAKA,EAAe,CAACC,GAAG,IAAKA,GAAG,IAAI2D,SAAS,EAAE;GACxD,CAAA,CAAA;AACD,EAAA,OAAO1D,QAAQ,CAAA;AACjB,CAAA;AAEA;;AAEG;AACa,SAAAQ,UAAUA,CAAAyD,IAAA,EAIV;EAAA,IAJW;AACzB/D,IAAAA,QAAQ,GAAG,GAAG;AACda,IAAAA,MAAM,GAAG,EAAE;AACXC,IAAAA,IAAI,GAAG,EAAA;AACO,GAAA,GAAAiD,IAAA,CAAA;EACd,IAAIlD,MAAM,IAAIA,MAAM,KAAK,GAAG,EAC1Bb,QAAQ,IAAIa,MAAM,CAACX,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGW,MAAM,GAAG,GAAG,GAAGA,MAAM,CAAA;EAC9D,IAAIC,IAAI,IAAIA,IAAI,KAAK,GAAG,EACtBd,QAAQ,IAAIc,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGY,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAA;AACxD,EAAA,OAAOd,QAAQ,CAAA;AACjB,CAAA;AAEA;;AAEG;AACG,SAAUY,SAASA,CAACD,IAAY,EAAA;EACpC,IAAIqD,UAAU,GAAkB,EAAE,CAAA;AAElC,EAAA,IAAIrD,IAAI,EAAE;AACR,IAAA,IAAIiC,SAAS,GAAGjC,IAAI,CAACkC,OAAO,CAAC,GAAG,CAAC,CAAA;IACjC,IAAID,SAAS,IAAI,CAAC,EAAE;MAClBoB,UAAU,CAAClD,IAAI,GAAGH,IAAI,CAACwB,MAAM,CAACS,SAAS,CAAC,CAAA;MACxCjC,IAAI,GAAGA,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAES,SAAS,CAAC,CAAA;AACjC,KAAA;AAED,IAAA,IAAIqB,WAAW,GAAGtD,IAAI,CAACkC,OAAO,CAAC,GAAG,CAAC,CAAA;IACnC,IAAIoB,WAAW,IAAI,CAAC,EAAE;MACpBD,UAAU,CAACnD,MAAM,GAAGF,IAAI,CAACwB,MAAM,CAAC8B,WAAW,CAAC,CAAA;MAC5CtD,IAAI,GAAGA,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAE8B,WAAW,CAAC,CAAA;AACnC,KAAA;AAED,IAAA,IAAItD,IAAI,EAAE;MACRqD,UAAU,CAAChE,QAAQ,GAAGW,IAAI,CAAA;AAC3B,KAAA;AACF,GAAA;AAED,EAAA,OAAOqD,UAAU,CAAA;AACnB,CAAA;AASA,SAAShC,kBAAkBA,CACzBkC,WAA2E,EAC3E7D,UAA8C,EAC9C8D,gBAA+D,EAC/D5F,OAAA,EAA+B;AAAA,EAAA,IAA/BA,OAAA,KAAA,KAAA,CAAA,EAAA;IAAAA,OAAA,GAA6B,EAAE,CAAA;AAAA,GAAA;EAE/B,IAAI;IAAEqD,MAAM,GAAGW,QAAQ,CAAC6B,WAAY;AAAE1F,IAAAA,QAAQ,GAAG,KAAA;AAAO,GAAA,GAAGH,OAAO,CAAA;AAClE,EAAA,IAAIsD,aAAa,GAAGD,MAAM,CAACrB,OAAO,CAAA;AAClC,EAAA,IAAInB,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;EACvB,IAAIC,QAAQ,GAAoB,IAAI,CAAA;AAEpC,EAAA,IAAIR,KAAK,GAAGuF,QAAQ,EAAG,CAAA;AACvB;AACA;AACA;EACA,IAAIvF,KAAK,IAAI,IAAI,EAAE;AACjBA,IAAAA,KAAK,GAAG,CAAC,CAAA;AACT+C,IAAAA,aAAa,CAACyC,YAAY,CAAAR,QAAA,CAAMjC,EAAAA,EAAAA,aAAa,CAAC7C,KAAK,EAAA;AAAE4E,MAAAA,GAAG,EAAE9E,KAAAA;AAAK,KAAA,CAAA,EAAI,EAAE,CAAC,CAAA;AACvE,GAAA;EAED,SAASuF,QAAQA,GAAA;AACf,IAAA,IAAIrF,KAAK,GAAG6C,aAAa,CAAC7C,KAAK,IAAI;AAAE4E,MAAAA,GAAG,EAAE,IAAA;KAAM,CAAA;IAChD,OAAO5E,KAAK,CAAC4E,GAAG,CAAA;AAClB,GAAA;EAEA,SAASW,SAASA,GAAA;IAChBnF,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;AACnB,IAAA,IAAIkC,SAAS,GAAG8C,QAAQ,EAAE,CAAA;IAC1B,IAAIlD,KAAK,GAAGI,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGA,SAAS,GAAGzC,KAAK,CAAA;AACxDA,IAAAA,KAAK,GAAGyC,SAAS,CAAA;AACjB,IAAA,IAAIjC,QAAQ,EAAE;AACZA,MAAAA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;AAAEqB,QAAAA,KAAAA;AAAK,OAAE,CAAC,CAAA;AACxD,KAAA;AACH,GAAA;AAEA,EAAA,SAASJ,IAAIA,CAACnB,EAAM,EAAEZ,KAAW,EAAA;IAC/BI,MAAM,GAAGhB,MAAM,CAAC4C,IAAI,CAAA;IACpB,IAAIlB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAQ,EAAEF,EAAE,EAAEZ,KAAK,CAAC,CAAA;AAC1D,IAAA,IAAImF,gBAAgB,EAAEA,gBAAgB,CAACrE,QAAQ,EAAEF,EAAE,CAAC,CAAA;AAEpDd,IAAAA,KAAK,GAAGuF,QAAQ,EAAE,GAAG,CAAC,CAAA;AACtB,IAAA,IAAIG,YAAY,GAAGb,eAAe,CAAC7D,QAAQ,EAAEhB,KAAK,CAAC,CAAA;AACnD,IAAA,IAAI6D,GAAG,GAAGpC,OAAO,CAACF,UAAU,CAACP,QAAQ,CAAC,CAAA;AAEtC;IACA,IAAI;MACF+B,aAAa,CAAC4C,SAAS,CAACD,YAAY,EAAE,EAAE,EAAE7B,GAAG,CAAC,CAAA;KAC/C,CAAC,OAAO+B,KAAK,EAAE;AACd;AACA;AACA;AACA;MACA,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,gBAAgB,EAAE;AACpE,QAAA,MAAMF,KAAK,CAAA;AACZ,OAAA;AACD;AACA;AACA9C,MAAAA,MAAM,CAAC9B,QAAQ,CAAC+E,MAAM,CAAClC,GAAG,CAAC,CAAA;AAC5B,KAAA;IAED,IAAIjE,QAAQ,IAAIY,QAAQ,EAAE;AACxBA,MAAAA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;AAAEqB,QAAAA,KAAK,EAAE,CAAA;AAAC,OAAE,CAAC,CAAA;AAC3D,KAAA;AACH,GAAA;AAEA,EAAA,SAASC,OAAOA,CAACxB,EAAM,EAAEZ,KAAW,EAAA;IAClCI,MAAM,GAAGhB,MAAM,CAACiD,OAAO,CAAA;IACvB,IAAIvB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAQ,EAAEF,EAAE,EAAEZ,KAAK,CAAC,CAAA;AAC1D,IAAA,IAAImF,gBAAgB,EAAEA,gBAAgB,CAACrE,QAAQ,EAAEF,EAAE,CAAC,CAAA;IAEpDd,KAAK,GAAGuF,QAAQ,EAAE,CAAA;AAClB,IAAA,IAAIG,YAAY,GAAGb,eAAe,CAAC7D,QAAQ,EAAEhB,KAAK,CAAC,CAAA;AACnD,IAAA,IAAI6D,GAAG,GAAGpC,OAAO,CAACF,UAAU,CAACP,QAAQ,CAAC,CAAA;IACtC+B,aAAa,CAACyC,YAAY,CAACE,YAAY,EAAE,EAAE,EAAE7B,GAAG,CAAC,CAAA;IAEjD,IAAIjE,QAAQ,IAAIY,QAAQ,EAAE;AACxBA,MAAAA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;AAAEqB,QAAAA,KAAK,EAAE,CAAA;AAAC,OAAE,CAAC,CAAA;AAC3D,KAAA;AACH,GAAA;EAEA,SAASX,SAASA,CAACZ,EAAM,EAAA;AACvB;AACA;AACA;IACA,IAAI0C,IAAI,GACNV,MAAM,CAAC9B,QAAQ,CAACgF,MAAM,KAAK,MAAM,GAC7BlD,MAAM,CAAC9B,QAAQ,CAACgF,MAAM,GACtBlD,MAAM,CAAC9B,QAAQ,CAAC2C,IAAI,CAAA;AAE1B,IAAA,IAAIA,IAAI,GAAG,OAAO7C,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;AACvD;AACA;AACA;IACA6C,IAAI,GAAGA,IAAI,CAACrB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;AAChC4B,IAAAA,SAAS,CACPV,IAAI,EACkEG,qEAAAA,GAAAA,IAAM,CAC7E,CAAA;AACD,IAAA,OAAO,IAAIhC,GAAG,CAACgC,IAAI,EAAEH,IAAI,CAAC,CAAA;AAC5B,GAAA;AAEA,EAAA,IAAI/B,OAAO,GAAY;IACrB,IAAInB,MAAMA,GAAA;AACR,MAAA,OAAOA,MAAM,CAAA;KACd;IACD,IAAIU,QAAQA,GAAA;AACV,MAAA,OAAOoE,WAAW,CAACtC,MAAM,EAAEC,aAAa,CAAC,CAAA;KAC1C;IACDL,MAAMA,CAACC,EAAY,EAAA;AACjB,MAAA,IAAInC,QAAQ,EAAE;AACZ,QAAA,MAAM,IAAI6D,KAAK,CAAC,4CAA4C,CAAC,CAAA;AAC9D,OAAA;AACDvB,MAAAA,MAAM,CAACmD,gBAAgB,CAAC1G,iBAAiB,EAAEkG,SAAS,CAAC,CAAA;AACrDjF,MAAAA,QAAQ,GAAGmC,EAAE,CAAA;AAEb,MAAA,OAAO,MAAK;AACVG,QAAAA,MAAM,CAACoD,mBAAmB,CAAC3G,iBAAiB,EAAEkG,SAAS,CAAC,CAAA;AACxDjF,QAAAA,QAAQ,GAAG,IAAI,CAAA;OAChB,CAAA;KACF;IACDe,UAAUA,CAACT,EAAE,EAAA;AACX,MAAA,OAAOS,UAAU,CAACuB,MAAM,EAAEhC,EAAE,CAAC,CAAA;KAC9B;IACDY,SAAS;IACTE,cAAcA,CAACd,EAAE,EAAA;AACf;AACA,MAAA,IAAI+C,GAAG,GAAGnC,SAAS,CAACZ,EAAE,CAAC,CAAA;MACvB,OAAO;QACLI,QAAQ,EAAE2C,GAAG,CAAC3C,QAAQ;QACtBa,MAAM,EAAE8B,GAAG,CAAC9B,MAAM;QAClBC,IAAI,EAAE6B,GAAG,CAAC7B,IAAAA;OACX,CAAA;KACF;IACDC,IAAI;IACJK,OAAO;IACPE,EAAEA,CAAC/B,CAAC,EAAA;AACF,MAAA,OAAOsC,aAAa,CAACP,EAAE,CAAC/B,CAAC,CAAC,CAAA;AAC5B,KAAA;GACD,CAAA;AAED,EAAA,OAAOgB,OAAO,CAAA;AAChB,CAAA;AAEA;;AC/tBA,IAAY0E,UAKX,CAAA;AALD,CAAA,UAAYA,UAAU,EAAA;AACpBA,EAAAA,UAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACbA,EAAAA,UAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrBA,EAAAA,UAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrBA,EAAAA,UAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACjB,CAAC,EALWA,UAAU,KAAVA,UAAU,GAKrB,EAAA,CAAA,CAAA,CAAA;AAyRM,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAoB,CAC3D,MAAM,EACN,eAAe,EACf,MAAM,EACN,IAAI,EACJ,OAAO,EACP,UAAU,CACX,CAAC,CAAA;AAoJF,SAASC,YAAYA,CACnBC,KAA0B,EAAA;AAE1B,EAAA,OAAOA,KAAK,CAACvG,KAAK,KAAK,IAAI,CAAA;AAC7B,CAAA;AAEA;AACA;AACM,SAAUwG,yBAAyBA,CACvCC,MAA6B,EAC7BC,kBAA8C,EAC9CC,UAAuB,EACvBC,QAAA,EAA4B;AAAA,EAAA,IAD5BD,UAAuB,KAAA,KAAA,CAAA,EAAA;AAAvBA,IAAAA,UAAuB,GAAA,EAAE,CAAA;AAAA,GAAA;AAAA,EAAA,IACzBC,QAAA,KAAA,KAAA,CAAA,EAAA;IAAAA,QAAA,GAA0B,EAAE,CAAA;AAAA,GAAA;EAE5B,OAAOH,MAAM,CAAC3G,GAAG,CAAC,CAACyG,KAAK,EAAEvG,KAAK,KAAI;IACjC,IAAI6G,QAAQ,GAAG,CAAC,GAAGF,UAAU,EAAEG,MAAM,CAAC9G,KAAK,CAAC,CAAC,CAAA;AAC7C,IAAA,IAAI+G,EAAE,GAAG,OAAOR,KAAK,CAACQ,EAAE,KAAK,QAAQ,GAAGR,KAAK,CAACQ,EAAE,GAAGF,QAAQ,CAACG,IAAI,CAAC,GAAG,CAAC,CAAA;AACrE9C,IAAAA,SAAS,CACPqC,KAAK,CAACvG,KAAK,KAAK,IAAI,IAAI,CAACuG,KAAK,CAACU,QAAQ,EAAA,2CACI,CAC5C,CAAA;IACD/C,SAAS,CACP,CAAC0C,QAAQ,CAACG,EAAE,CAAC,EACb,qCAAqCA,GAAAA,EAAE,GACrC,aAAA,GAAA,wDAAwD,CAC3D,CAAA;AAED,IAAA,IAAIT,YAAY,CAACC,KAAK,CAAC,EAAE;MACvB,IAAIW,UAAU,GAAAlC,QAAA,CAAA,EAAA,EACTuB,KAAK,EACLG,kBAAkB,CAACH,KAAK,CAAC,EAAA;AAC5BQ,QAAAA,EAAAA;OACD,CAAA,CAAA;AACDH,MAAAA,QAAQ,CAACG,EAAE,CAAC,GAAGG,UAAU,CAAA;AACzB,MAAA,OAAOA,UAAU,CAAA;AAClB,KAAA,MAAM;MACL,IAAIC,iBAAiB,GAAAnC,QAAA,CAAA,EAAA,EAChBuB,KAAK,EACLG,kBAAkB,CAACH,KAAK,CAAC,EAAA;QAC5BQ,EAAE;AACFE,QAAAA,QAAQ,EAAE9G,SAAAA;OACX,CAAA,CAAA;AACDyG,MAAAA,QAAQ,CAACG,EAAE,CAAC,GAAGI,iBAAiB,CAAA;MAEhC,IAAIZ,KAAK,CAACU,QAAQ,EAAE;AAClBE,QAAAA,iBAAiB,CAACF,QAAQ,GAAGT,yBAAyB,CACpDD,KAAK,CAACU,QAAQ,EACdP,kBAAkB,EAClBG,QAAQ,EACRD,QAAQ,CACT,CAAA;AACF,OAAA;AAED,MAAA,OAAOO,iBAAiB,CAAA;AACzB,KAAA;AACH,GAAC,CAAC,CAAA;AACJ,CAAA;AAEA;;;;AAIG;AACG,SAAUC,WAAWA,CAGzBX,MAAyB,EACzBY,WAAuC,EACvCC,QAAQ,EAAM;AAAA,EAAA,IAAdA,QAAQ,KAAA,KAAA,CAAA,EAAA;AAARA,IAAAA,QAAQ,GAAG,GAAG,CAAA;AAAA,GAAA;EAEd,OAAOC,eAAe,CAACd,MAAM,EAAEY,WAAW,EAAEC,QAAQ,EAAE,KAAK,CAAC,CAAA;AAC9D,CAAA;AAEM,SAAUC,eAAeA,CAG7Bd,MAAyB,EACzBY,WAAuC,EACvCC,QAAgB,EAChBE,YAAqB,EAAA;AAErB,EAAA,IAAIxG,QAAQ,GACV,OAAOqG,WAAW,KAAK,QAAQ,GAAGvF,SAAS,CAACuF,WAAW,CAAC,GAAGA,WAAW,CAAA;EAExE,IAAInG,QAAQ,GAAGuG,aAAa,CAACzG,QAAQ,CAACE,QAAQ,IAAI,GAAG,EAAEoG,QAAQ,CAAC,CAAA;EAEhE,IAAIpG,QAAQ,IAAI,IAAI,EAAE;AACpB,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED,EAAA,IAAIwG,QAAQ,GAAGC,aAAa,CAAClB,MAAM,CAAC,CAAA;EACpCmB,iBAAiB,CAACF,QAAQ,CAAC,CAAA;EAE3B,IAAIG,OAAO,GAAG,IAAI,CAAA;AAClB,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAED,OAAO,IAAI,IAAI,IAAIC,CAAC,GAAGJ,QAAQ,CAACrH,MAAM,EAAE,EAAEyH,CAAC,EAAE;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAIC,OAAO,GAAGC,UAAU,CAAC9G,QAAQ,CAAC,CAAA;IAClC2G,OAAO,GAAGI,gBAAgB,CACxBP,QAAQ,CAACI,CAAC,CAAC,EACXC,OAAO,EACPP,YAAY,CACb,CAAA;AACF,GAAA;AAED,EAAA,OAAOK,OAAO,CAAA;AAChB,CAAA;AAUgB,SAAAK,0BAA0BA,CACxCC,KAA6B,EAC7BC,UAAqB,EAAA;EAErB,IAAI;IAAE7B,KAAK;IAAErF,QAAQ;AAAEmH,IAAAA,MAAAA;AAAM,GAAE,GAAGF,KAAK,CAAA;EACvC,OAAO;IACLpB,EAAE,EAAER,KAAK,CAACQ,EAAE;IACZ7F,QAAQ;IACRmH,MAAM;AACNC,IAAAA,IAAI,EAAEF,UAAU,CAAC7B,KAAK,CAACQ,EAAE,CAAC;IAC1BwB,MAAM,EAAEhC,KAAK,CAACgC,MAAAA;GACf,CAAA;AACH,CAAA;AAmBA,SAASZ,aAAaA,CAGpBlB,MAAyB,EACzBiB,QAA2C,EAC3Cc,WAAA,EACA7B,UAAU,EAAK;AAAA,EAAA,IAFfe,QAA2C,KAAA,KAAA,CAAA,EAAA;AAA3CA,IAAAA,QAA2C,GAAA,EAAE,CAAA;AAAA,GAAA;AAAA,EAAA,IAC7Cc,WAAA,KAAA,KAAA,CAAA,EAAA;AAAAA,IAAAA,WAAA,GAA4C,EAAE,CAAA;AAAA,GAAA;AAAA,EAAA,IAC9C7B,UAAU,KAAA,KAAA,CAAA,EAAA;AAAVA,IAAAA,UAAU,GAAG,EAAE,CAAA;AAAA,GAAA;EAEf,IAAI8B,YAAY,GAAGA,CACjBlC,KAAsB,EACtBvG,KAAa,EACb0I,YAAqB,KACnB;AACF,IAAA,IAAIC,IAAI,GAA+B;MACrCD,YAAY,EACVA,YAAY,KAAKvI,SAAS,GAAGoG,KAAK,CAAC1E,IAAI,IAAI,EAAE,GAAG6G,YAAY;AAC9DE,MAAAA,aAAa,EAAErC,KAAK,CAACqC,aAAa,KAAK,IAAI;AAC3CC,MAAAA,aAAa,EAAE7I,KAAK;AACpBuG,MAAAA,KAAAA;KACD,CAAA;IAED,IAAIoC,IAAI,CAACD,YAAY,CAACpF,UAAU,CAAC,GAAG,CAAC,EAAE;AACrCY,MAAAA,SAAS,CACPyE,IAAI,CAACD,YAAY,CAACpF,UAAU,CAACqD,UAAU,CAAC,EACxC,wBAAA,GAAwBgC,IAAI,CAACD,YAAY,qCACnC/B,UAAU,GAAA,gDAAA,CAA+C,gEACA,CAChE,CAAA;AAEDgC,MAAAA,IAAI,CAACD,YAAY,GAAGC,IAAI,CAACD,YAAY,CAAC1E,KAAK,CAAC2C,UAAU,CAACtG,MAAM,CAAC,CAAA;AAC/D,KAAA;IAED,IAAIwB,IAAI,GAAGiH,SAAS,CAAC,CAACnC,UAAU,EAAEgC,IAAI,CAACD,YAAY,CAAC,CAAC,CAAA;AACrD,IAAA,IAAIK,UAAU,GAAGP,WAAW,CAACQ,MAAM,CAACL,IAAI,CAAC,CAAA;AAEzC;AACA;AACA;IACA,IAAIpC,KAAK,CAACU,QAAQ,IAAIV,KAAK,CAACU,QAAQ,CAAC5G,MAAM,GAAG,CAAC,EAAE;MAC/C6D,SAAS;AACP;AACA;MACAqC,KAAK,CAACvG,KAAK,KAAK,IAAI,EACpB,yDACuC6B,IAAAA,qCAAAA,GAAAA,IAAI,SAAI,CAChD,CAAA;MACD8F,aAAa,CAACpB,KAAK,CAACU,QAAQ,EAAES,QAAQ,EAAEqB,UAAU,EAAElH,IAAI,CAAC,CAAA;AAC1D,KAAA;AAED;AACA;IACA,IAAI0E,KAAK,CAAC1E,IAAI,IAAI,IAAI,IAAI,CAAC0E,KAAK,CAACvG,KAAK,EAAE;AACtC,MAAA,OAAA;AACD,KAAA;IAED0H,QAAQ,CAACzF,IAAI,CAAC;MACZJ,IAAI;MACJoH,KAAK,EAAEC,YAAY,CAACrH,IAAI,EAAE0E,KAAK,CAACvG,KAAK,CAAC;AACtC+I,MAAAA,UAAAA;AACD,KAAA,CAAC,CAAA;GACH,CAAA;AACDtC,EAAAA,MAAM,CAAC0C,OAAO,CAAC,CAAC5C,KAAK,EAAEvG,KAAK,KAAI;AAAA,IAAA,IAAAoJ,WAAA,CAAA;AAC9B;AACA,IAAA,IAAI7C,KAAK,CAAC1E,IAAI,KAAK,EAAE,IAAI,GAAAuH,WAAA,GAAC7C,KAAK,CAAC1E,IAAI,aAAVuH,WAAA,CAAYC,QAAQ,CAAC,GAAG,CAAC,CAAE,EAAA;AACnDZ,MAAAA,YAAY,CAAClC,KAAK,EAAEvG,KAAK,CAAC,CAAA;AAC3B,KAAA,MAAM;MACL,KAAK,IAAIsJ,QAAQ,IAAIC,uBAAuB,CAAChD,KAAK,CAAC1E,IAAI,CAAC,EAAE;AACxD4G,QAAAA,YAAY,CAAClC,KAAK,EAAEvG,KAAK,EAAEsJ,QAAQ,CAAC,CAAA;AACrC,OAAA;AACF,KAAA;AACH,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO5B,QAAQ,CAAA;AACjB,CAAA;AAEA;;;;;;;;;;;;;AAaG;AACH,SAAS6B,uBAAuBA,CAAC1H,IAAY,EAAA;AAC3C,EAAA,IAAI2H,QAAQ,GAAG3H,IAAI,CAAC4H,KAAK,CAAC,GAAG,CAAC,CAAA;AAC9B,EAAA,IAAID,QAAQ,CAACnJ,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE,CAAA;AAEpC,EAAA,IAAI,CAACqJ,KAAK,EAAE,GAAGC,IAAI,CAAC,GAAGH,QAAQ,CAAA;AAE/B;AACA,EAAA,IAAII,UAAU,GAAGF,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC,CAAA;AACpC;EACA,IAAIC,QAAQ,GAAGJ,KAAK,CAACpH,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;AAEvC,EAAA,IAAIqH,IAAI,CAACtJ,MAAM,KAAK,CAAC,EAAE;AACrB;AACA;IACA,OAAOuJ,UAAU,GAAG,CAACE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAACA,QAAQ,CAAC,CAAA;AAChD,GAAA;EAED,IAAIC,YAAY,GAAGR,uBAAuB,CAACI,IAAI,CAAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;EAE1D,IAAIgD,MAAM,GAAa,EAAE,CAAA;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;EACAA,MAAM,CAAC/H,IAAI,CACT,GAAG8H,YAAY,CAACjK,GAAG,CAAEmK,OAAO,IAC1BA,OAAO,KAAK,EAAE,GAAGH,QAAQ,GAAG,CAACA,QAAQ,EAAEG,OAAO,CAAC,CAACjD,IAAI,CAAC,GAAG,CAAC,CAC1D,CACF,CAAA;AAED;AACA,EAAA,IAAI4C,UAAU,EAAE;AACdI,IAAAA,MAAM,CAAC/H,IAAI,CAAC,GAAG8H,YAAY,CAAC,CAAA;AAC7B,GAAA;AAED;EACA,OAAOC,MAAM,CAAClK,GAAG,CAAEwJ,QAAQ,IACzBzH,IAAI,CAACyB,UAAU,CAAC,GAAG,CAAC,IAAIgG,QAAQ,KAAK,EAAE,GAAG,GAAG,GAAGA,QAAQ,CACzD,CAAA;AACH,CAAA;AAEA,SAAS1B,iBAAiBA,CAACF,QAAuB,EAAA;EAChDA,QAAQ,CAACwC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACjBD,CAAC,CAAClB,KAAK,KAAKmB,CAAC,CAACnB,KAAK,GACfmB,CAAC,CAACnB,KAAK,GAAGkB,CAAC,CAAClB,KAAK;AAAC,IAClBoB,cAAc,CACZF,CAAC,CAACpB,UAAU,CAACjJ,GAAG,CAAE6I,IAAI,IAAKA,IAAI,CAACE,aAAa,CAAC,EAC9CuB,CAAC,CAACrB,UAAU,CAACjJ,GAAG,CAAE6I,IAAI,IAAKA,IAAI,CAACE,aAAa,CAAC,CAC/C,CACN,CAAA;AACH,CAAA;AAEA,MAAMyB,OAAO,GAAG,WAAW,CAAA;AAC3B,MAAMC,mBAAmB,GAAG,CAAC,CAAA;AAC7B,MAAMC,eAAe,GAAG,CAAC,CAAA;AACzB,MAAMC,iBAAiB,GAAG,CAAC,CAAA;AAC3B,MAAMC,kBAAkB,GAAG,EAAE,CAAA;AAC7B,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAA;AACvB,MAAMC,OAAO,GAAIC,CAAS,IAAKA,CAAC,KAAK,GAAG,CAAA;AAExC,SAAS3B,YAAYA,CAACrH,IAAY,EAAE7B,KAA0B,EAAA;AAC5D,EAAA,IAAIwJ,QAAQ,GAAG3H,IAAI,CAAC4H,KAAK,CAAC,GAAG,CAAC,CAAA;AAC9B,EAAA,IAAIqB,YAAY,GAAGtB,QAAQ,CAACnJ,MAAM,CAAA;AAClC,EAAA,IAAImJ,QAAQ,CAACuB,IAAI,CAACH,OAAO,CAAC,EAAE;AAC1BE,IAAAA,YAAY,IAAIH,YAAY,CAAA;AAC7B,GAAA;AAED,EAAA,IAAI3K,KAAK,EAAE;AACT8K,IAAAA,YAAY,IAAIN,eAAe,CAAA;AAChC,GAAA;AAED,EAAA,OAAOhB,QAAQ,CACZwB,MAAM,CAAEH,CAAC,IAAK,CAACD,OAAO,CAACC,CAAC,CAAC,CAAC,CAC1BI,MAAM,CACL,CAAChC,KAAK,EAAEiC,OAAO,KACbjC,KAAK,IACJqB,OAAO,CAACa,IAAI,CAACD,OAAO,CAAC,GAClBX,mBAAmB,GACnBW,OAAO,KAAK,EAAE,GACdT,iBAAiB,GACjBC,kBAAkB,CAAC,EACzBI,YAAY,CACb,CAAA;AACL,CAAA;AAEA,SAAST,cAAcA,CAACF,CAAW,EAAEC,CAAW,EAAA;AAC9C,EAAA,IAAIgB,QAAQ,GACVjB,CAAC,CAAC9J,MAAM,KAAK+J,CAAC,CAAC/J,MAAM,IAAI8J,CAAC,CAACnG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACqH,KAAK,CAAC,CAAC5K,CAAC,EAAEqH,CAAC,KAAKrH,CAAC,KAAK2J,CAAC,CAACtC,CAAC,CAAC,CAAC,CAAA;AAErE,EAAA,OAAOsD,QAAQ;AACX;AACA;AACA;AACA;AACAjB,EAAAA,CAAC,CAACA,CAAC,CAAC9J,MAAM,GAAG,CAAC,CAAC,GAAG+J,CAAC,CAACA,CAAC,CAAC/J,MAAM,GAAG,CAAC,CAAC;AACjC;AACA;EACA,CAAC,CAAA;AACP,CAAA;AAEA,SAAS4H,gBAAgBA,CAIvBqD,MAAoC,EACpCpK,QAAgB,EAChBsG,YAAY,EAAQ;AAAA,EAAA,IAApBA,YAAY,KAAA,KAAA,CAAA,EAAA;AAAZA,IAAAA,YAAY,GAAG,KAAK,CAAA;AAAA,GAAA;EAEpB,IAAI;AAAEuB,IAAAA,UAAAA;AAAY,GAAA,GAAGuC,MAAM,CAAA;EAE3B,IAAIC,aAAa,GAAG,EAAE,CAAA;EACtB,IAAIC,eAAe,GAAG,GAAG,CAAA;EACzB,IAAI3D,OAAO,GAAoD,EAAE,CAAA;AACjE,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,UAAU,CAAC1I,MAAM,EAAE,EAAEyH,CAAC,EAAE;AAC1C,IAAA,IAAIa,IAAI,GAAGI,UAAU,CAACjB,CAAC,CAAC,CAAA;IACxB,IAAI2D,GAAG,GAAG3D,CAAC,KAAKiB,UAAU,CAAC1I,MAAM,GAAG,CAAC,CAAA;AACrC,IAAA,IAAIqL,iBAAiB,GACnBF,eAAe,KAAK,GAAG,GACnBtK,QAAQ,GACRA,QAAQ,CAAC8C,KAAK,CAACwH,eAAe,CAACnL,MAAM,CAAC,IAAI,GAAG,CAAA;IACnD,IAAI8H,KAAK,GAAGwD,SAAS,CACnB;MAAE9J,IAAI,EAAE8G,IAAI,CAACD,YAAY;MAAEE,aAAa,EAAED,IAAI,CAACC,aAAa;AAAE6C,MAAAA,GAAAA;KAAK,EACnEC,iBAAiB,CAClB,CAAA;AAED,IAAA,IAAInF,KAAK,GAAGoC,IAAI,CAACpC,KAAK,CAAA;IAEtB,IACE,CAAC4B,KAAK,IACNsD,GAAG,IACHjE,YAAY,IACZ,CAACuB,UAAU,CAACA,UAAU,CAAC1I,MAAM,GAAG,CAAC,CAAC,CAACkG,KAAK,CAACvG,KAAK,EAC9C;MACAmI,KAAK,GAAGwD,SAAS,CACf;QACE9J,IAAI,EAAE8G,IAAI,CAACD,YAAY;QACvBE,aAAa,EAAED,IAAI,CAACC,aAAa;AACjC6C,QAAAA,GAAG,EAAE,KAAA;OACN,EACDC,iBAAiB,CAClB,CAAA;AACF,KAAA;IAED,IAAI,CAACvD,KAAK,EAAE;AACV,MAAA,OAAO,IAAI,CAAA;AACZ,KAAA;IAEDyD,MAAM,CAAC7F,MAAM,CAACwF,aAAa,EAAEpD,KAAK,CAACE,MAAM,CAAC,CAAA;IAE1CR,OAAO,CAAC5F,IAAI,CAAC;AACX;AACAoG,MAAAA,MAAM,EAAEkD,aAAiC;MACzCrK,QAAQ,EAAE4H,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAACjH,QAAQ,CAAC,CAAC;AACtD2K,MAAAA,YAAY,EAAEC,iBAAiB,CAC7BhD,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAAC0D,YAAY,CAAC,CAAC,CACjD;AACDtF,MAAAA,KAAAA;AACD,KAAA,CAAC,CAAA;AAEF,IAAA,IAAI4B,KAAK,CAAC0D,YAAY,KAAK,GAAG,EAAE;MAC9BL,eAAe,GAAG1C,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAAC0D,YAAY,CAAC,CAAC,CAAA;AACnE,KAAA;AACF,GAAA;AAED,EAAA,OAAOhE,OAAO,CAAA;AAChB,CAAA;AAEA;;;;AAIG;SACakE,YAAYA,CAC1BC,YAAkB,EAClB3D,QAEa;AAAA,EAAA,IAFbA;IAAAA,SAEI,EAAS,CAAA;AAAA,GAAA;EAEb,IAAIxG,IAAI,GAAWmK,YAAY,CAAA;AAC/B,EAAA,IAAInK,IAAI,CAACgI,QAAQ,CAAC,GAAG,CAAC,IAAIhI,IAAI,KAAK,GAAG,IAAI,CAACA,IAAI,CAACgI,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC9D1I,OAAO,CACL,KAAK,EACL,eAAeU,GAAAA,IAAI,GACbA,mCAAAA,IAAAA,IAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAqC,oCAAA,CAAA,GAAA,kEACE,IAChCT,oCAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAA,KAAA,CAAI,CACpE,CAAA;IACDT,IAAI,GAAGA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAS,CAAA;AACzC,GAAA;AAED;EACA,MAAM2J,MAAM,GAAGpK,IAAI,CAACyB,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAA;EAE9C,MAAMhC,SAAS,GAAI4K,CAAM,IACvBA,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGpF,MAAM,CAACoF,CAAC,CAAC,CAAA;AAExD,EAAA,MAAM1C,QAAQ,GAAG3H,IAAI,CAClB4H,KAAK,CAAC,KAAK,CAAC,CACZ3J,GAAG,CAAC,CAACoL,OAAO,EAAElL,KAAK,EAAEmM,KAAK,KAAI;IAC7B,MAAMC,aAAa,GAAGpM,KAAK,KAAKmM,KAAK,CAAC9L,MAAM,GAAG,CAAC,CAAA;AAEhD;AACA,IAAA,IAAI+L,aAAa,IAAIlB,OAAO,KAAK,GAAG,EAAE;MACpC,MAAMmB,IAAI,GAAG,GAAsB,CAAA;AACnC;AACA,MAAA,OAAO/K,SAAS,CAAC+G,MAAM,CAACgE,IAAI,CAAC,CAAC,CAAA;AAC/B,KAAA;AAED,IAAA,MAAMC,QAAQ,GAAGpB,OAAO,CAAC/C,KAAK,CAAC,kBAAkB,CAAC,CAAA;AAClD,IAAA,IAAImE,QAAQ,EAAE;AACZ,MAAA,MAAM,GAAGvL,GAAG,EAAEwL,QAAQ,CAAC,GAAGD,QAAQ,CAAA;AAClC,MAAA,IAAIE,KAAK,GAAGnE,MAAM,CAACtH,GAAsB,CAAC,CAAA;MAC1CmD,SAAS,CAACqI,QAAQ,KAAK,GAAG,IAAIC,KAAK,IAAI,IAAI,EAAA,aAAA,GAAezL,GAAG,GAAA,UAAS,CAAC,CAAA;MACvE,OAAOO,SAAS,CAACkL,KAAK,CAAC,CAAA;AACxB,KAAA;AAED;AACA,IAAA,OAAOtB,OAAO,CAAC5I,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;GACnC,CAAA;AACD;AAAA,GACC0I,MAAM,CAAEE,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC,CAAA;AAEjC,EAAA,OAAOe,MAAM,GAAGzC,QAAQ,CAACxC,IAAI,CAAC,GAAG,CAAC,CAAA;AACpC,CAAA;AAiDA;;;;;AAKG;AACa,SAAA2E,SAASA,CAIvBc,OAAiC,EACjCvL,QAAgB,EAAA;AAEhB,EAAA,IAAI,OAAOuL,OAAO,KAAK,QAAQ,EAAE;AAC/BA,IAAAA,OAAO,GAAG;AAAE5K,MAAAA,IAAI,EAAE4K,OAAO;AAAE7D,MAAAA,aAAa,EAAE,KAAK;AAAE6C,MAAAA,GAAG,EAAE,IAAA;KAAM,CAAA;AAC7D,GAAA;AAED,EAAA,IAAI,CAACiB,OAAO,EAAEC,cAAc,CAAC,GAAGC,WAAW,CACzCH,OAAO,CAAC5K,IAAI,EACZ4K,OAAO,CAAC7D,aAAa,EACrB6D,OAAO,CAAChB,GAAG,CACZ,CAAA;AAED,EAAA,IAAItD,KAAK,GAAGjH,QAAQ,CAACiH,KAAK,CAACuE,OAAO,CAAC,CAAA;AACnC,EAAA,IAAI,CAACvE,KAAK,EAAE,OAAO,IAAI,CAAA;AAEvB,EAAA,IAAIqD,eAAe,GAAGrD,KAAK,CAAC,CAAC,CAAC,CAAA;EAC9B,IAAI0D,YAAY,GAAGL,eAAe,CAAClJ,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AAC3D,EAAA,IAAIuK,aAAa,GAAG1E,KAAK,CAACnE,KAAK,CAAC,CAAC,CAAC,CAAA;AAClC,EAAA,IAAIqE,MAAM,GAAWsE,cAAc,CAAC1B,MAAM,CACxC,CAAC6B,IAAI,EAAA7H,IAAA,EAA6BjF,KAAK,KAAI;IAAA,IAApC;MAAE+M,SAAS;AAAEnD,MAAAA,UAAAA;KAAY,GAAA3E,IAAA,CAAA;AAC9B;AACA;IACA,IAAI8H,SAAS,KAAK,GAAG,EAAE;AACrB,MAAA,IAAIC,UAAU,GAAGH,aAAa,CAAC7M,KAAK,CAAC,IAAI,EAAE,CAAA;MAC3C6L,YAAY,GAAGL,eAAe,CAC3BxH,KAAK,CAAC,CAAC,EAAEwH,eAAe,CAACnL,MAAM,GAAG2M,UAAU,CAAC3M,MAAM,CAAC,CACpDiC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AAC5B,KAAA;AAED,IAAA,MAAM6B,KAAK,GAAG0I,aAAa,CAAC7M,KAAK,CAAC,CAAA;AAClC,IAAA,IAAI4J,UAAU,IAAI,CAACzF,KAAK,EAAE;AACxB2I,MAAAA,IAAI,CAACC,SAAS,CAAC,GAAG5M,SAAS,CAAA;AAC5B,KAAA,MAAM;AACL2M,MAAAA,IAAI,CAACC,SAAS,CAAC,GAAG,CAAC5I,KAAK,IAAI,EAAE,EAAE7B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;AACrD,KAAA;AACD,IAAA,OAAOwK,IAAI,CAAA;GACZ,EACD,EAAE,CACH,CAAA;EAED,OAAO;IACLzE,MAAM;AACNnH,IAAAA,QAAQ,EAAEsK,eAAe;IACzBK,YAAY;AACZY,IAAAA,OAAAA;GACD,CAAA;AACH,CAAA;AAIA,SAASG,WAAWA,CAClB/K,IAAY,EACZ+G,aAAa,EACb6C,GAAG,EAAO;AAAA,EAAA,IADV7C,aAAa,KAAA,KAAA,CAAA,EAAA;AAAbA,IAAAA,aAAa,GAAG,KAAK,CAAA;AAAA,GAAA;AAAA,EAAA,IACrB6C,GAAG,KAAA,KAAA,CAAA,EAAA;AAAHA,IAAAA,GAAG,GAAG,IAAI,CAAA;AAAA,GAAA;AAEVtK,EAAAA,OAAO,CACLU,IAAI,KAAK,GAAG,IAAI,CAACA,IAAI,CAACgI,QAAQ,CAAC,GAAG,CAAC,IAAIhI,IAAI,CAACgI,QAAQ,CAAC,IAAI,CAAC,EAC1D,eAAA,GAAehI,IAAI,GACbA,mCAAAA,IAAAA,IAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAqC,oCAAA,CAAA,GAAA,kEACE,2CAChCT,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,SAAI,CACpE,CAAA;EAED,IAAI+F,MAAM,GAAwB,EAAE,CAAA;AACpC,EAAA,IAAI4E,YAAY,GACd,GAAG,GACHpL,IAAI,CACDS,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;AAAC,GACvBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AAAC,GACrBA,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC;GACrCA,OAAO,CACN,mBAAmB,EACnB,CAAC4K,CAAS,EAAEH,SAAiB,EAAEnD,UAAU,KAAI;IAC3CvB,MAAM,CAACpG,IAAI,CAAC;MAAE8K,SAAS;MAAEnD,UAAU,EAAEA,UAAU,IAAI,IAAA;AAAI,KAAE,CAAC,CAAA;AAC1D,IAAA,OAAOA,UAAU,GAAG,cAAc,GAAG,YAAY,CAAA;AACnD,GAAC,CACF,CAAA;AAEL,EAAA,IAAI/H,IAAI,CAACgI,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtBxB,MAAM,CAACpG,IAAI,CAAC;AAAE8K,MAAAA,SAAS,EAAE,GAAA;AAAK,KAAA,CAAC,CAAA;IAC/BE,YAAY,IACVpL,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,IAAI,GACzB,OAAO;MACP,mBAAmB,CAAC;GAC3B,MAAM,IAAI4J,GAAG,EAAE;AACd;AACAwB,IAAAA,YAAY,IAAI,OAAO,CAAA;GACxB,MAAM,IAAIpL,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,GAAG,EAAE;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACAoL,IAAAA,YAAY,IAAI,eAAe,CAAA;AAChC,GAAA,MAAM,CACL;AAGF,EAAA,IAAIP,OAAO,GAAG,IAAIS,MAAM,CAACF,YAAY,EAAErE,aAAa,GAAGzI,SAAS,GAAG,GAAG,CAAC,CAAA;AAEvE,EAAA,OAAO,CAACuM,OAAO,EAAErE,MAAM,CAAC,CAAA;AAC1B,CAAA;AAEM,SAAUL,UAAUA,CAAC7D,KAAa,EAAA;EACtC,IAAI;IACF,OAAOA,KAAK,CACTsF,KAAK,CAAC,GAAG,CAAC,CACV3J,GAAG,CAAEsN,CAAC,IAAKC,kBAAkB,CAACD,CAAC,CAAC,CAAC9K,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CACvD0E,IAAI,CAAC,GAAG,CAAC,CAAA;GACb,CAAC,OAAOpB,KAAK,EAAE;IACdzE,OAAO,CACL,KAAK,EACL,iBAAA,GAAiBgD,KAAK,GAC2C,6CAAA,GAAA,+DAAA,IAAA,YAAA,GAClDyB,KAAK,GAAA,IAAA,CAAI,CACzB,CAAA;AAED,IAAA,OAAOzB,KAAK,CAAA;AACb,GAAA;AACH,CAAA;AAEA;;AAEG;AACa,SAAAsD,aAAaA,CAC3BvG,QAAgB,EAChBoG,QAAgB,EAAA;AAEhB,EAAA,IAAIA,QAAQ,KAAK,GAAG,EAAE,OAAOpG,QAAQ,CAAA;AAErC,EAAA,IAAI,CAACA,QAAQ,CAACoM,WAAW,EAAE,CAAChK,UAAU,CAACgE,QAAQ,CAACgG,WAAW,EAAE,CAAC,EAAE;AAC9D,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED;AACA;AACA,EAAA,IAAIC,UAAU,GAAGjG,QAAQ,CAACuC,QAAQ,CAAC,GAAG,CAAC,GACnCvC,QAAQ,CAACjH,MAAM,GAAG,CAAC,GACnBiH,QAAQ,CAACjH,MAAM,CAAA;AACnB,EAAA,IAAImN,QAAQ,GAAGtM,QAAQ,CAACE,MAAM,CAACmM,UAAU,CAAC,CAAA;AAC1C,EAAA,IAAIC,QAAQ,IAAIA,QAAQ,KAAK,GAAG,EAAE;AAChC;AACA,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED,EAAA,OAAOtM,QAAQ,CAAC8C,KAAK,CAACuJ,UAAU,CAAC,IAAI,GAAG,CAAA;AAC1C,CAAA;AAEA;;;;AAIG;SACaE,WAAWA,CAAC3M,EAAM,EAAE4M,YAAY,EAAM;AAAA,EAAA,IAAlBA,YAAY,KAAA,KAAA,CAAA,EAAA;AAAZA,IAAAA,YAAY,GAAG,GAAG,CAAA;AAAA,GAAA;EACpD,IAAI;AACFxM,IAAAA,QAAQ,EAAEyM,UAAU;AACpB5L,IAAAA,MAAM,GAAG,EAAE;AACXC,IAAAA,IAAI,GAAG,EAAA;GACR,GAAG,OAAOlB,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,CAAA;EAE/C,IAAII,QAAQ,GAAGyM,UAAU,GACrBA,UAAU,CAACrK,UAAU,CAAC,GAAG,CAAC,GACxBqK,UAAU,GACVC,eAAe,CAACD,UAAU,EAAED,YAAY,CAAC,GAC3CA,YAAY,CAAA;EAEhB,OAAO;IACLxM,QAAQ;AACRa,IAAAA,MAAM,EAAE8L,eAAe,CAAC9L,MAAM,CAAC;IAC/BC,IAAI,EAAE8L,aAAa,CAAC9L,IAAI,CAAA;GACzB,CAAA;AACH,CAAA;AAEA,SAAS4L,eAAeA,CAAClF,YAAoB,EAAEgF,YAAoB,EAAA;AACjE,EAAA,IAAIlE,QAAQ,GAAGkE,YAAY,CAACpL,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACmH,KAAK,CAAC,GAAG,CAAC,CAAA;AAC1D,EAAA,IAAIsE,gBAAgB,GAAGrF,YAAY,CAACe,KAAK,CAAC,GAAG,CAAC,CAAA;AAE9CsE,EAAAA,gBAAgB,CAAC5E,OAAO,CAAE+B,OAAO,IAAI;IACnC,IAAIA,OAAO,KAAK,IAAI,EAAE;AACpB;MACA,IAAI1B,QAAQ,CAACnJ,MAAM,GAAG,CAAC,EAAEmJ,QAAQ,CAACwE,GAAG,EAAE,CAAA;AACxC,KAAA,MAAM,IAAI9C,OAAO,KAAK,GAAG,EAAE;AAC1B1B,MAAAA,QAAQ,CAACvH,IAAI,CAACiJ,OAAO,CAAC,CAAA;AACvB,KAAA;AACH,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO1B,QAAQ,CAACnJ,MAAM,GAAG,CAAC,GAAGmJ,QAAQ,CAACxC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;AACvD,CAAA;AAEA,SAASiH,mBAAmBA,CAC1BC,IAAY,EACZC,KAAa,EACbC,IAAY,EACZvM,IAAmB,EAAA;AAEnB,EAAA,OACE,oBAAqBqM,GAAAA,IAAI,GACjBC,sCAAAA,IAAAA,MAAAA,GAAAA,KAAK,iBAAa9M,IAAI,CAACC,SAAS,CACtCO,IAAI,CACL,GAAA,oCAAA,CAAoC,IAC7BuM,MAAAA,GAAAA,IAAI,8DAA2D,GACJ,qEAAA,CAAA;AAEvE,CAAA;AAEA;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACG,SAAUC,0BAA0BA,CAExCxG,OAAY,EAAA;AACZ,EAAA,OAAOA,OAAO,CAACmD,MAAM,CACnB,CAAC7C,KAAK,EAAEnI,KAAK,KACXA,KAAK,KAAK,CAAC,IAAKmI,KAAK,CAAC5B,KAAK,CAAC1E,IAAI,IAAIsG,KAAK,CAAC5B,KAAK,CAAC1E,IAAI,CAACxB,MAAM,GAAG,CAAE,CACnE,CAAA;AACH,CAAA;AAEA;AACA;AACgB,SAAAiO,mBAAmBA,CAEjCzG,OAAY,EAAE0G,oBAA6B,EAAA;AAC3C,EAAA,IAAIC,WAAW,GAAGH,0BAA0B,CAACxG,OAAO,CAAC,CAAA;AAErD;AACA;AACA;AACA,EAAA,IAAI0G,oBAAoB,EAAE;IACxB,OAAOC,WAAW,CAAC1O,GAAG,CAAC,CAACqI,KAAK,EAAErD,GAAG,KAChCA,GAAG,KAAK0J,WAAW,CAACnO,MAAM,GAAG,CAAC,GAAG8H,KAAK,CAACjH,QAAQ,GAAGiH,KAAK,CAAC0D,YAAY,CACrE,CAAA;AACF,GAAA;EAED,OAAO2C,WAAW,CAAC1O,GAAG,CAAEqI,KAAK,IAAKA,KAAK,CAAC0D,YAAY,CAAC,CAAA;AACvD,CAAA;AAEA;;AAEG;AACG,SAAU4C,SAASA,CACvBC,KAAS,EACTC,cAAwB,EACxBC,gBAAwB,EACxBC,cAAc,EAAQ;AAAA,EAAA,IAAtBA,cAAc,KAAA,KAAA,CAAA,EAAA;AAAdA,IAAAA,cAAc,GAAG,KAAK,CAAA;AAAA,GAAA;AAEtB,EAAA,IAAI/N,EAAiB,CAAA;AACrB,EAAA,IAAI,OAAO4N,KAAK,KAAK,QAAQ,EAAE;AAC7B5N,IAAAA,EAAE,GAAGgB,SAAS,CAAC4M,KAAK,CAAC,CAAA;AACtB,GAAA,MAAM;AACL5N,IAAAA,EAAE,GAAAkE,QAAA,CAAQ0J,EAAAA,EAAAA,KAAK,CAAE,CAAA;IAEjBxK,SAAS,CACP,CAACpD,EAAE,CAACI,QAAQ,IAAI,CAACJ,EAAE,CAACI,QAAQ,CAACmI,QAAQ,CAAC,GAAG,CAAC,EAC1C4E,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAEnN,EAAE,CAAC,CACnD,CAAA;IACDoD,SAAS,CACP,CAACpD,EAAE,CAACI,QAAQ,IAAI,CAACJ,EAAE,CAACI,QAAQ,CAACmI,QAAQ,CAAC,GAAG,CAAC,EAC1C4E,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,EAAEnN,EAAE,CAAC,CACjD,CAAA;IACDoD,SAAS,CACP,CAACpD,EAAE,CAACiB,MAAM,IAAI,CAACjB,EAAE,CAACiB,MAAM,CAACsH,QAAQ,CAAC,GAAG,CAAC,EACtC4E,mBAAmB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAEnN,EAAE,CAAC,CAC/C,CAAA;AACF,GAAA;EAED,IAAIgO,WAAW,GAAGJ,KAAK,KAAK,EAAE,IAAI5N,EAAE,CAACI,QAAQ,KAAK,EAAE,CAAA;EACpD,IAAIyM,UAAU,GAAGmB,WAAW,GAAG,GAAG,GAAGhO,EAAE,CAACI,QAAQ,CAAA;AAEhD,EAAA,IAAI6N,IAAY,CAAA;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIpB,UAAU,IAAI,IAAI,EAAE;AACtBoB,IAAAA,IAAI,GAAGH,gBAAgB,CAAA;AACxB,GAAA,MAAM;AACL,IAAA,IAAII,kBAAkB,GAAGL,cAAc,CAACtO,MAAM,GAAG,CAAC,CAAA;AAElD;AACA;AACA;AACA;IACA,IAAI,CAACwO,cAAc,IAAIlB,UAAU,CAACrK,UAAU,CAAC,IAAI,CAAC,EAAE;AAClD,MAAA,IAAI2L,UAAU,GAAGtB,UAAU,CAAClE,KAAK,CAAC,GAAG,CAAC,CAAA;AAEtC,MAAA,OAAOwF,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QAC7BA,UAAU,CAACC,KAAK,EAAE,CAAA;AAClBF,QAAAA,kBAAkB,IAAI,CAAC,CAAA;AACxB,OAAA;MAEDlO,EAAE,CAACI,QAAQ,GAAG+N,UAAU,CAACjI,IAAI,CAAC,GAAG,CAAC,CAAA;AACnC,KAAA;IAED+H,IAAI,GAAGC,kBAAkB,IAAI,CAAC,GAAGL,cAAc,CAACK,kBAAkB,CAAC,GAAG,GAAG,CAAA;AAC1E,GAAA;AAED,EAAA,IAAInN,IAAI,GAAG4L,WAAW,CAAC3M,EAAE,EAAEiO,IAAI,CAAC,CAAA;AAEhC;AACA,EAAA,IAAII,wBAAwB,GAC1BxB,UAAU,IAAIA,UAAU,KAAK,GAAG,IAAIA,UAAU,CAAC9D,QAAQ,CAAC,GAAG,CAAC,CAAA;AAC9D;AACA,EAAA,IAAIuF,uBAAuB,GACzB,CAACN,WAAW,IAAInB,UAAU,KAAK,GAAG,KAAKiB,gBAAgB,CAAC/E,QAAQ,CAAC,GAAG,CAAC,CAAA;AACvE,EAAA,IACE,CAAChI,IAAI,CAACX,QAAQ,CAAC2I,QAAQ,CAAC,GAAG,CAAC,KAC3BsF,wBAAwB,IAAIC,uBAAuB,CAAC,EACrD;IACAvN,IAAI,CAACX,QAAQ,IAAI,GAAG,CAAA;AACrB,GAAA;AAED,EAAA,OAAOW,IAAI,CAAA;AACb,CAAA;AAEA;;AAEG;AACG,SAAUwN,aAAaA,CAACvO,EAAM,EAAA;AAClC;EACA,OAAOA,EAAE,KAAK,EAAE,IAAKA,EAAW,CAACI,QAAQ,KAAK,EAAE,GAC5C,GAAG,GACH,OAAOJ,EAAE,KAAK,QAAQ,GACtBgB,SAAS,CAAChB,EAAE,CAAC,CAACI,QAAQ,GACtBJ,EAAE,CAACI,QAAQ,CAAA;AACjB,CAAA;AAEA;;AAEG;MACU4H,SAAS,GAAIwG,KAAe,IACvCA,KAAK,CAACtI,IAAI,CAAC,GAAG,CAAC,CAAC1E,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAC;AAExC;;AAEG;MACUwJ,iBAAiB,GAAI5K,QAAgB,IAChDA,QAAQ,CAACoB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,EAAC;AAEnD;;AAEG;AACI,MAAMuL,eAAe,GAAI9L,MAAc,IAC5C,CAACA,MAAM,IAAIA,MAAM,KAAK,GAAG,GACrB,EAAE,GACFA,MAAM,CAACuB,UAAU,CAAC,GAAG,CAAC,GACtBvB,MAAM,GACN,GAAG,GAAGA,MAAM,CAAA;AAElB;;AAEG;AACI,MAAM+L,aAAa,GAAI9L,IAAY,IACxC,CAACA,IAAI,IAAIA,IAAI,KAAK,GAAG,GAAG,EAAE,GAAGA,IAAI,CAACsB,UAAU,CAAC,GAAG,CAAC,GAAGtB,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAA;AAOvE;;;AAGG;AACI,MAAMuN,IAAI,GAAiB,SAArBA,IAAIA,CAAkBjH,IAAI,EAAEkH,IAAI,EAAS;AAAA,EAAA,IAAbA,IAAI,KAAA,KAAA,CAAA,EAAA;IAAJA,IAAI,GAAG,EAAE,CAAA;AAAA,GAAA;AAChD,EAAA,IAAIC,YAAY,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG;AAAEE,IAAAA,MAAM,EAAEF,IAAAA;AAAI,GAAE,GAAGA,IAAI,CAAA;EAErE,IAAIG,OAAO,GAAG,IAAIC,OAAO,CAACH,YAAY,CAACE,OAAO,CAAC,CAAA;AAC/C,EAAA,IAAI,CAACA,OAAO,CAACE,GAAG,CAAC,cAAc,CAAC,EAAE;AAChCF,IAAAA,OAAO,CAACG,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAA;AAC/D,GAAA;AAED,EAAA,OAAO,IAAIC,QAAQ,CAAC1O,IAAI,CAACC,SAAS,CAACgH,IAAI,CAAC,EAAAtD,QAAA,CAAA,EAAA,EACnCyK,YAAY,EAAA;AACfE,IAAAA,OAAAA;AAAO,GAAA,CACR,CAAC,CAAA;AACJ,EAAC;MAEYK,oBAAoB,CAAA;AAK/BC,EAAAA,WAAYA,CAAA3H,IAAO,EAAEkH,IAAmB,EAAA;IAJxC,IAAI,CAAAU,IAAA,GAAW,sBAAsB,CAAA;IAKnC,IAAI,CAAC5H,IAAI,GAAGA,IAAI,CAAA;AAChB,IAAA,IAAI,CAACkH,IAAI,GAAGA,IAAI,IAAI,IAAI,CAAA;AAC1B,GAAA;AACD,CAAA;AAED;;;AAGG;AACa,SAAAlH,IAAIA,CAAIA,IAAO,EAAEkH,IAA4B,EAAA;EAC3D,OAAO,IAAIQ,oBAAoB,CAC7B1H,IAAI,EACJ,OAAOkH,IAAI,KAAK,QAAQ,GAAG;AAAEE,IAAAA,MAAM,EAAEF,IAAAA;GAAM,GAAGA,IAAI,CACnD,CAAA;AACH,CAAA;AAQM,MAAOW,oBAAqB,SAAQ9L,KAAK,CAAA,EAAA;MAElC+L,YAAY,CAAA;AAWvBH,EAAAA,WAAYA,CAAA3H,IAA6B,EAAEmH,YAA2B,EAAA;AAV9D,IAAA,IAAA,CAAAY,cAAc,GAAgB,IAAIhK,GAAG,EAAU,CAAA;AAI/C,IAAA,IAAA,CAAAiK,WAAW,GACjB,IAAIjK,GAAG,EAAE,CAAA;IAGX,IAAY,CAAAkK,YAAA,GAAa,EAAE,CAAA;AAGzBrM,IAAAA,SAAS,CACPoE,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACkI,KAAK,CAACC,OAAO,CAACnI,IAAI,CAAC,EACxD,oCAAoC,CACrC,CAAA;AAED;AACA;AACA,IAAA,IAAIoI,MAAyC,CAAA;AAC7C,IAAA,IAAI,CAACC,YAAY,GAAG,IAAIC,OAAO,CAAC,CAAC1D,CAAC,EAAE2D,CAAC,KAAMH,MAAM,GAAGG,CAAE,CAAC,CAAA;AACvD,IAAA,IAAI,CAACC,UAAU,GAAG,IAAIC,eAAe,EAAE,CAAA;IACvC,IAAIC,OAAO,GAAGA,MACZN,MAAM,CAAC,IAAIP,oBAAoB,CAAC,uBAAuB,CAAC,CAAC,CAAA;AAC3D,IAAA,IAAI,CAACc,mBAAmB,GAAG,MACzB,IAAI,CAACH,UAAU,CAACI,MAAM,CAAChL,mBAAmB,CAAC,OAAO,EAAE8K,OAAO,CAAC,CAAA;IAC9D,IAAI,CAACF,UAAU,CAACI,MAAM,CAACjL,gBAAgB,CAAC,OAAO,EAAE+K,OAAO,CAAC,CAAA;AAEzD,IAAA,IAAI,CAAC1I,IAAI,GAAGsD,MAAM,CAAC/L,OAAO,CAACyI,IAAI,CAAC,CAAC2C,MAAM,CACrC,CAACkG,GAAG,EAAAC,KAAA,KAAA;AAAA,MAAA,IAAE,CAACrQ,GAAG,EAAEoD,KAAK,CAAC,GAAAiN,KAAA,CAAA;AAAA,MAAA,OAChBxF,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAE;QACjB,CAACpQ,GAAG,GAAG,IAAI,CAACsQ,YAAY,CAACtQ,GAAG,EAAEoD,KAAK,CAAA;OACpC,CAAC,CAAA;KACJ,EAAA,EAAE,CACH,CAAA;IAED,IAAI,IAAI,CAACmN,IAAI,EAAE;AACb;MACA,IAAI,CAACL,mBAAmB,EAAE,CAAA;AAC3B,KAAA;IAED,IAAI,CAACzB,IAAI,GAAGC,YAAY,CAAA;AAC1B,GAAA;AAEQ4B,EAAAA,YAAYA,CAClBtQ,GAAW,EACXoD,KAAiC,EAAA;AAEjC,IAAA,IAAI,EAAEA,KAAK,YAAYyM,OAAO,CAAC,EAAE;AAC/B,MAAA,OAAOzM,KAAK,CAAA;AACb,KAAA;AAED,IAAA,IAAI,CAACoM,YAAY,CAACtO,IAAI,CAAClB,GAAG,CAAC,CAAA;AAC3B,IAAA,IAAI,CAACsP,cAAc,CAACkB,GAAG,CAACxQ,GAAG,CAAC,CAAA;AAE5B;AACA;IACA,IAAIyQ,OAAO,GAAmBZ,OAAO,CAACa,IAAI,CAAC,CAACtN,KAAK,EAAE,IAAI,CAACwM,YAAY,CAAC,CAAC,CAACe,IAAI,CACxEpJ,IAAI,IAAK,IAAI,CAACqJ,QAAQ,CAACH,OAAO,EAAEzQ,GAAG,EAAEZ,SAAS,EAAEmI,IAAe,CAAC,EAChE1C,KAAK,IAAK,IAAI,CAAC+L,QAAQ,CAACH,OAAO,EAAEzQ,GAAG,EAAE6E,KAAgB,CAAC,CACzD,CAAA;AAED;AACA;AACA4L,IAAAA,OAAO,CAACI,KAAK,CAAC,MAAO,EAAC,CAAC,CAAA;AAEvBhG,IAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,UAAU,EAAE;MAAEM,GAAG,EAAEA,MAAM,IAAA;AAAI,KAAE,CAAC,CAAA;AAC/D,IAAA,OAAON,OAAO,CAAA;AAChB,GAAA;EAEQG,QAAQA,CACdH,OAAuB,EACvBzQ,GAAW,EACX6E,KAAc,EACd0C,IAAc,EAAA;IAEd,IACE,IAAI,CAACwI,UAAU,CAACI,MAAM,CAACa,OAAO,IAC9BnM,KAAK,YAAYuK,oBAAoB,EACrC;MACA,IAAI,CAACc,mBAAmB,EAAE,CAAA;AAC1BrF,MAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,MAAMlM,KAAAA;AAAK,OAAE,CAAC,CAAA;AAC9D,MAAA,OAAOgL,OAAO,CAACF,MAAM,CAAC9K,KAAK,CAAC,CAAA;AAC7B,KAAA;AAED,IAAA,IAAI,CAACyK,cAAc,CAAC2B,MAAM,CAACjR,GAAG,CAAC,CAAA;IAE/B,IAAI,IAAI,CAACuQ,IAAI,EAAE;AACb;MACA,IAAI,CAACL,mBAAmB,EAAE,CAAA;AAC3B,KAAA;AAED;AACA;AACA,IAAA,IAAIrL,KAAK,KAAKzF,SAAS,IAAImI,IAAI,KAAKnI,SAAS,EAAE;MAC7C,IAAI8R,cAAc,GAAG,IAAI5N,KAAK,CAC5B,0BAA0BtD,GAAAA,GAAG,gGACwB,CACtD,CAAA;AACD6K,MAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,MAAMG,cAAAA;AAAc,OAAE,CAAC,CAAA;AACvE,MAAA,IAAI,CAACC,IAAI,CAAC,KAAK,EAAEnR,GAAG,CAAC,CAAA;AACrB,MAAA,OAAO6P,OAAO,CAACF,MAAM,CAACuB,cAAc,CAAC,CAAA;AACtC,KAAA;IAED,IAAI3J,IAAI,KAAKnI,SAAS,EAAE;AACtByL,MAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,MAAMlM,KAAAA;AAAK,OAAE,CAAC,CAAA;AAC9D,MAAA,IAAI,CAACsM,IAAI,CAAC,KAAK,EAAEnR,GAAG,CAAC,CAAA;AACrB,MAAA,OAAO6P,OAAO,CAACF,MAAM,CAAC9K,KAAK,CAAC,CAAA;AAC7B,KAAA;AAEDgG,IAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,OAAO,EAAE;MAAEM,GAAG,EAAEA,MAAMxJ,IAAAA;AAAI,KAAE,CAAC,CAAA;AAC5D,IAAA,IAAI,CAAC4J,IAAI,CAAC,KAAK,EAAEnR,GAAG,CAAC,CAAA;AACrB,IAAA,OAAOuH,IAAI,CAAA;AACb,GAAA;AAEQ4J,EAAAA,IAAIA,CAACH,OAAgB,EAAEI,UAAmB,EAAA;AAChD,IAAA,IAAI,CAAC7B,WAAW,CAACnH,OAAO,CAAEiJ,UAAU,IAAKA,UAAU,CAACL,OAAO,EAAEI,UAAU,CAAC,CAAC,CAAA;AAC3E,GAAA;EAEAE,SAASA,CAAC1P,EAAmD,EAAA;AAC3D,IAAA,IAAI,CAAC2N,WAAW,CAACiB,GAAG,CAAC5O,EAAE,CAAC,CAAA;IACxB,OAAO,MAAM,IAAI,CAAC2N,WAAW,CAAC0B,MAAM,CAACrP,EAAE,CAAC,CAAA;AAC1C,GAAA;AAEA2P,EAAAA,MAAMA,GAAA;AACJ,IAAA,IAAI,CAACxB,UAAU,CAACyB,KAAK,EAAE,CAAA;AACvB,IAAA,IAAI,CAAClC,cAAc,CAAClH,OAAO,CAAC,CAACiE,CAAC,EAAEoF,CAAC,KAAK,IAAI,CAACnC,cAAc,CAAC2B,MAAM,CAACQ,CAAC,CAAC,CAAC,CAAA;AACpE,IAAA,IAAI,CAACN,IAAI,CAAC,IAAI,CAAC,CAAA;AACjB,GAAA;EAEA,MAAMO,WAAWA,CAACvB,MAAmB,EAAA;IACnC,IAAIa,OAAO,GAAG,KAAK,CAAA;AACnB,IAAA,IAAI,CAAC,IAAI,CAACT,IAAI,EAAE;MACd,IAAIN,OAAO,GAAGA,MAAM,IAAI,CAACsB,MAAM,EAAE,CAAA;AACjCpB,MAAAA,MAAM,CAACjL,gBAAgB,CAAC,OAAO,EAAE+K,OAAO,CAAC,CAAA;AACzCe,MAAAA,OAAO,GAAG,MAAM,IAAInB,OAAO,CAAE8B,OAAO,IAAI;AACtC,QAAA,IAAI,CAACL,SAAS,CAAEN,OAAO,IAAI;AACzBb,UAAAA,MAAM,CAAChL,mBAAmB,CAAC,OAAO,EAAE8K,OAAO,CAAC,CAAA;AAC5C,UAAA,IAAIe,OAAO,IAAI,IAAI,CAACT,IAAI,EAAE;YACxBoB,OAAO,CAACX,OAAO,CAAC,CAAA;AACjB,WAAA;AACH,SAAC,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;AACH,KAAA;AACD,IAAA,OAAOA,OAAO,CAAA;AAChB,GAAA;EAEA,IAAIT,IAAIA,GAAA;AACN,IAAA,OAAO,IAAI,CAACjB,cAAc,CAACsC,IAAI,KAAK,CAAC,CAAA;AACvC,GAAA;EAEA,IAAIC,aAAaA,GAAA;AACf1O,IAAAA,SAAS,CACP,IAAI,CAACoE,IAAI,KAAK,IAAI,IAAI,IAAI,CAACgJ,IAAI,EAC/B,2DAA2D,CAC5D,CAAA;AAED,IAAA,OAAO1F,MAAM,CAAC/L,OAAO,CAAC,IAAI,CAACyI,IAAI,CAAC,CAAC2C,MAAM,CACrC,CAACkG,GAAG,EAAA0B,KAAA,KAAA;AAAA,MAAA,IAAE,CAAC9R,GAAG,EAAEoD,KAAK,CAAC,GAAA0O,KAAA,CAAA;AAAA,MAAA,OAChBjH,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAE;AACjB,QAAA,CAACpQ,GAAG,GAAG+R,oBAAoB,CAAC3O,KAAK,CAAA;OAClC,CAAC,CAAA;KACJ,EAAA,EAAE,CACH,CAAA;AACH,GAAA;EAEA,IAAI4O,WAAWA,GAAA;AACb,IAAA,OAAOvC,KAAK,CAACzB,IAAI,CAAC,IAAI,CAACsB,cAAc,CAAC,CAAA;AACxC,GAAA;AACD,CAAA;AAED,SAAS2C,gBAAgBA,CAAC7O,KAAU,EAAA;EAClC,OACEA,KAAK,YAAYyM,OAAO,IAAKzM,KAAwB,CAAC8O,QAAQ,KAAK,IAAI,CAAA;AAE3E,CAAA;AAEA,SAASH,oBAAoBA,CAAC3O,KAAU,EAAA;AACtC,EAAA,IAAI,CAAC6O,gBAAgB,CAAC7O,KAAK,CAAC,EAAE;AAC5B,IAAA,OAAOA,KAAK,CAAA;AACb,GAAA;EAED,IAAIA,KAAK,CAAC+O,MAAM,EAAE;IAChB,MAAM/O,KAAK,CAAC+O,MAAM,CAAA;AACnB,GAAA;EACD,OAAO/O,KAAK,CAACgP,KAAK,CAAA;AACpB,CAAA;AAOO,MAAMC,KAAK,GAAkB,SAAvBA,KAAKA,CAAmB9K,IAAI,EAAEkH,IAAI,EAAS;AAAA,EAAA,IAAbA,IAAI,KAAA,KAAA,CAAA,EAAA;IAAJA,IAAI,GAAG,EAAE,CAAA;AAAA,GAAA;AAClD,EAAA,IAAIC,YAAY,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG;AAAEE,IAAAA,MAAM,EAAEF,IAAAA;AAAI,GAAE,GAAGA,IAAI,CAAA;AAErE,EAAA,OAAO,IAAIY,YAAY,CAAC9H,IAAI,EAAEmH,YAAY,CAAC,CAAA;AAC7C,EAAC;AAOD;;;AAGG;AACI,MAAM4D,QAAQ,GAAqB,SAA7BA,QAAQA,CAAsBxP,GAAG,EAAE2L,IAAI,EAAU;AAAA,EAAA,IAAdA,IAAI,KAAA,KAAA,CAAA,EAAA;AAAJA,IAAAA,IAAI,GAAG,GAAG,CAAA;AAAA,GAAA;EACxD,IAAIC,YAAY,GAAGD,IAAI,CAAA;AACvB,EAAA,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;AACpCA,IAAAA,YAAY,GAAG;AAAEC,MAAAA,MAAM,EAAED,YAAAA;KAAc,CAAA;GACxC,MAAM,IAAI,OAAOA,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;IACrDD,YAAY,CAACC,MAAM,GAAG,GAAG,CAAA;AAC1B,GAAA;EAED,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAACH,YAAY,CAACE,OAAO,CAAC,CAAA;AAC/CA,EAAAA,OAAO,CAACG,GAAG,CAAC,UAAU,EAAEjM,GAAG,CAAC,CAAA;AAE5B,EAAA,OAAO,IAAIkM,QAAQ,CAAC,IAAI,EAAA/K,QAAA,KACnByK,YAAY,EAAA;AACfE,IAAAA,OAAAA;AAAO,GAAA,CACR,CAAC,CAAA;AACJ,EAAC;AAED;;;;AAIG;MACU2D,gBAAgB,GAAqBA,CAACzP,GAAG,EAAE2L,IAAI,KAAI;AAC9D,EAAA,IAAI+D,QAAQ,GAAGF,QAAQ,CAACxP,GAAG,EAAE2L,IAAI,CAAC,CAAA;EAClC+D,QAAQ,CAAC5D,OAAO,CAACG,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAA;AACvD,EAAA,OAAOyD,QAAQ,CAAA;AACjB,EAAC;AAED;;;;;AAKG;MACUjR,OAAO,GAAqBA,CAACuB,GAAG,EAAE2L,IAAI,KAAI;AACrD,EAAA,IAAI+D,QAAQ,GAAGF,QAAQ,CAACxP,GAAG,EAAE2L,IAAI,CAAC,CAAA;EAClC+D,QAAQ,CAAC5D,OAAO,CAACG,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;AAC/C,EAAA,OAAOyD,QAAQ,CAAA;AACjB,EAAC;AAQD;;;;;;;AAOG;MACUC,iBAAiB,CAAA;EAO5BvD,WACEA,CAAAP,MAAc,EACd+D,UAA8B,EAC9BnL,IAAS,EACToL,QAAQ,EAAQ;AAAA,IAAA,IAAhBA,QAAQ,KAAA,KAAA,CAAA,EAAA;AAARA,MAAAA,QAAQ,GAAG,KAAK,CAAA;AAAA,KAAA;IAEhB,IAAI,CAAChE,MAAM,GAAGA,MAAM,CAAA;AACpB,IAAA,IAAI,CAAC+D,UAAU,GAAGA,UAAU,IAAI,EAAE,CAAA;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ,CAAA;IACxB,IAAIpL,IAAI,YAAYjE,KAAK,EAAE;AACzB,MAAA,IAAI,CAACiE,IAAI,GAAGA,IAAI,CAAC1D,QAAQ,EAAE,CAAA;MAC3B,IAAI,CAACgB,KAAK,GAAG0C,IAAI,CAAA;AAClB,KAAA,MAAM;MACL,IAAI,CAACA,IAAI,GAAGA,IAAI,CAAA;AACjB,KAAA;AACH,GAAA;AACD,CAAA;AAED;;;AAGG;AACG,SAAUqL,oBAAoBA,CAAC/N,KAAU,EAAA;EAC7C,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAAC8J,MAAM,KAAK,QAAQ,IAChC,OAAO9J,KAAK,CAAC6N,UAAU,KAAK,QAAQ,IACpC,OAAO7N,KAAK,CAAC8N,QAAQ,KAAK,SAAS,IACnC,MAAM,IAAI9N,KAAK,CAAA;AAEnB;;AC3/BA,MAAMgO,uBAAuB,GAAyB,CACpD,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,CACT,CAAA;AACD,MAAMC,oBAAoB,GAAG,IAAIxN,GAAG,CAClCuN,uBAAuB,CACxB,CAAA;AAED,MAAME,sBAAsB,GAAiB,CAC3C,KAAK,EACL,GAAGF,uBAAuB,CAC3B,CAAA;AACD,MAAMG,mBAAmB,GAAG,IAAI1N,GAAG,CAAayN,sBAAsB,CAAC,CAAA;AAEvE,MAAME,mBAAmB,GAAG,IAAI3N,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;AAC9D,MAAM4N,iCAAiC,GAAG,IAAI5N,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;AAEtD,MAAM6N,eAAe,GAA6B;AACvDhU,EAAAA,KAAK,EAAE,MAAM;AACbc,EAAAA,QAAQ,EAAEb,SAAS;AACnBgU,EAAAA,UAAU,EAAEhU,SAAS;AACrBiU,EAAAA,UAAU,EAAEjU,SAAS;AACrBkU,EAAAA,WAAW,EAAElU,SAAS;AACtBmU,EAAAA,QAAQ,EAAEnU,SAAS;AACnBoP,EAAAA,IAAI,EAAEpP,SAAS;AACfoU,EAAAA,IAAI,EAAEpU,SAAAA;EACP;AAEM,MAAMqU,YAAY,GAA0B;AACjDtU,EAAAA,KAAK,EAAE,MAAM;AACboI,EAAAA,IAAI,EAAEnI,SAAS;AACfgU,EAAAA,UAAU,EAAEhU,SAAS;AACrBiU,EAAAA,UAAU,EAAEjU,SAAS;AACrBkU,EAAAA,WAAW,EAAElU,SAAS;AACtBmU,EAAAA,QAAQ,EAAEnU,SAAS;AACnBoP,EAAAA,IAAI,EAAEpP,SAAS;AACfoU,EAAAA,IAAI,EAAEpU,SAAAA;EACP;AAEM,MAAMsU,YAAY,GAAqB;AAC5CvU,EAAAA,KAAK,EAAE,WAAW;AAClBwU,EAAAA,OAAO,EAAEvU,SAAS;AAClBwU,EAAAA,KAAK,EAAExU,SAAS;AAChBa,EAAAA,QAAQ,EAAEb,SAAAA;EACX;AAED,MAAMyU,kBAAkB,GAAG,+BAA+B,CAAA;AAE1D,MAAMC,yBAAyB,GAAgCtO,KAAK,KAAM;AACxEuO,EAAAA,gBAAgB,EAAEC,OAAO,CAACxO,KAAK,CAACuO,gBAAgB,CAAA;AACjD,CAAA,CAAC,CAAA;AAEF,MAAME,uBAAuB,GAAG,0BAA0B,CAAA;AAE1D;AAEA;AACA;AACA;AAEA;;AAEG;AACG,SAAUC,YAAYA,CAACzF,IAAgB,EAAA;AAC3C,EAAA,MAAM0F,YAAY,GAAG1F,IAAI,CAAC1M,MAAM,GAC5B0M,IAAI,CAAC1M,MAAM,GACX,OAAOA,MAAM,KAAK,WAAW,GAC7BA,MAAM,GACN3C,SAAS,CAAA;EACb,MAAMgV,SAAS,GACb,OAAOD,YAAY,KAAK,WAAW,IACnC,OAAOA,YAAY,CAACzR,QAAQ,KAAK,WAAW,IAC5C,OAAOyR,YAAY,CAACzR,QAAQ,CAAC2R,aAAa,KAAK,WAAW,CAAA;EAC5D,MAAMC,QAAQ,GAAG,CAACF,SAAS,CAAA;EAE3BjR,SAAS,CACPsL,IAAI,CAAC/I,MAAM,CAACpG,MAAM,GAAG,CAAC,EACtB,2DAA2D,CAC5D,CAAA;AAED,EAAA,IAAIqG,kBAA8C,CAAA;EAClD,IAAI8I,IAAI,CAAC9I,kBAAkB,EAAE;IAC3BA,kBAAkB,GAAG8I,IAAI,CAAC9I,kBAAkB,CAAA;AAC7C,GAAA,MAAM,IAAI8I,IAAI,CAAC8F,mBAAmB,EAAE;AACnC;AACA,IAAA,IAAIA,mBAAmB,GAAG9F,IAAI,CAAC8F,mBAAmB,CAAA;IAClD5O,kBAAkB,GAAIH,KAAK,KAAM;MAC/BuO,gBAAgB,EAAEQ,mBAAmB,CAAC/O,KAAK,CAAA;AAC5C,KAAA,CAAC,CAAA;AACH,GAAA,MAAM;AACLG,IAAAA,kBAAkB,GAAGmO,yBAAyB,CAAA;AAC/C,GAAA;AAED;EACA,IAAIjO,QAAQ,GAAkB,EAAE,CAAA;AAChC;AACA,EAAA,IAAI2O,UAAU,GAAG/O,yBAAyB,CACxCgJ,IAAI,CAAC/I,MAAM,EACXC,kBAAkB,EAClBvG,SAAS,EACTyG,QAAQ,CACT,CAAA;AACD,EAAA,IAAI4O,kBAAyD,CAAA;AAC7D,EAAA,IAAIlO,QAAQ,GAAGkI,IAAI,CAAClI,QAAQ,IAAI,GAAG,CAAA;AACnC,EAAA,IAAImO,gBAAgB,GAAGjG,IAAI,CAACkG,YAAY,IAAIC,mBAAmB,CAAA;AAC/D,EAAA,IAAIC,2BAA2B,GAAGpG,IAAI,CAACqG,uBAAuB,CAAA;AAE9D;EACA,IAAIC,MAAM,GAAA9Q,QAAA,CAAA;AACR+Q,IAAAA,iBAAiB,EAAE,KAAK;AACxBC,IAAAA,sBAAsB,EAAE,KAAK;AAC7BC,IAAAA,mBAAmB,EAAE,KAAK;AAC1BC,IAAAA,kBAAkB,EAAE,KAAK;AACzB3H,IAAAA,oBAAoB,EAAE,KAAK;AAC3B4H,IAAAA,8BAA8B,EAAE,KAAA;GAC7B3G,EAAAA,IAAI,CAACsG,MAAM,CACf,CAAA;AACD;EACA,IAAIM,eAAe,GAAwB,IAAI,CAAA;AAC/C;AACA,EAAA,IAAI9F,WAAW,GAAG,IAAIjK,GAAG,EAAoB,CAAA;AAC7C;EACA,IAAIgQ,oBAAoB,GAAkC,IAAI,CAAA;AAC9D;EACA,IAAIC,uBAAuB,GAA2C,IAAI,CAAA;AAC1E;EACA,IAAIC,iBAAiB,GAAqC,IAAI,CAAA;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,IAAIC,qBAAqB,GAAGhH,IAAI,CAACiH,aAAa,IAAI,IAAI,CAAA;AAEtD,EAAA,IAAIC,cAAc,GAAGtP,WAAW,CAACmO,UAAU,EAAE/F,IAAI,CAAC/N,OAAO,CAACT,QAAQ,EAAEsG,QAAQ,CAAC,CAAA;EAC7E,IAAIqP,aAAa,GAAqB,IAAI,CAAA;AAE1C,EAAA,IAAID,cAAc,IAAI,IAAI,IAAI,CAACd,2BAA2B,EAAE;AAC1D;AACA;AACA,IAAA,IAAIhQ,KAAK,GAAGgR,sBAAsB,CAAC,GAAG,EAAE;AACtC1V,MAAAA,QAAQ,EAAEsO,IAAI,CAAC/N,OAAO,CAACT,QAAQ,CAACE,QAAAA;AACjC,KAAA,CAAC,CAAA;IACF,IAAI;MAAE2G,OAAO;AAAEtB,MAAAA,KAAAA;AAAK,KAAE,GAAGsQ,sBAAsB,CAACtB,UAAU,CAAC,CAAA;AAC3DmB,IAAAA,cAAc,GAAG7O,OAAO,CAAA;AACxB8O,IAAAA,aAAa,GAAG;MAAE,CAACpQ,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;KAAO,CAAA;AACtC,GAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,IAAI8Q,cAAc,IAAI,CAAClH,IAAI,CAACiH,aAAa,EAAE;AACzC,IAAA,IAAIK,QAAQ,GAAGC,aAAa,CAC1BL,cAAc,EACdnB,UAAU,EACV/F,IAAI,CAAC/N,OAAO,CAACT,QAAQ,CAACE,QAAQ,CAC/B,CAAA;IACD,IAAI4V,QAAQ,CAACE,MAAM,EAAE;AACnBN,MAAAA,cAAc,GAAG,IAAI,CAAA;AACtB,KAAA;AACF,GAAA;AAED,EAAA,IAAIO,WAAoB,CAAA;EACxB,IAAI,CAACP,cAAc,EAAE;AACnBO,IAAAA,WAAW,GAAG,KAAK,CAAA;AACnBP,IAAAA,cAAc,GAAG,EAAE,CAAA;AAEnB;AACA;AACA;IACA,IAAIZ,MAAM,CAACG,mBAAmB,EAAE;AAC9B,MAAA,IAAIa,QAAQ,GAAGC,aAAa,CAC1B,IAAI,EACJxB,UAAU,EACV/F,IAAI,CAAC/N,OAAO,CAACT,QAAQ,CAACE,QAAQ,CAC/B,CAAA;AACD,MAAA,IAAI4V,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACjP,OAAO,EAAE;QACvC6O,cAAc,GAAGI,QAAQ,CAACjP,OAAO,CAAA;AAClC,OAAA;AACF,KAAA;AACF,GAAA,MAAM,IAAI6O,cAAc,CAAC3L,IAAI,CAAEmM,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAAC4Q,IAAI,CAAC,EAAE;AACnD;AACA;AACAF,IAAAA,WAAW,GAAG,KAAK,CAAA;AACpB,GAAA,MAAM,IAAI,CAACP,cAAc,CAAC3L,IAAI,CAAEmM,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAAC6Q,MAAM,CAAC,EAAE;AACtD;AACAH,IAAAA,WAAW,GAAG,IAAI,CAAA;AACnB,GAAA,MAAM,IAAInB,MAAM,CAACG,mBAAmB,EAAE;AACrC;AACA;AACA;AACA,IAAA,IAAI7N,UAAU,GAAGoH,IAAI,CAACiH,aAAa,GAAGjH,IAAI,CAACiH,aAAa,CAACrO,UAAU,GAAG,IAAI,CAAA;AAC1E,IAAA,IAAIiP,MAAM,GAAG7H,IAAI,CAACiH,aAAa,GAAGjH,IAAI,CAACiH,aAAa,CAACY,MAAM,GAAG,IAAI,CAAA;AAClE;AACA,IAAA,IAAIA,MAAM,EAAE;AACV,MAAA,IAAIvS,GAAG,GAAG4R,cAAc,CAACY,SAAS,CAC/BJ,CAAC,IAAKG,MAAO,CAACH,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,CAAC,KAAK5G,SAAS,CACzC,CAAA;MACD8W,WAAW,GAAGP,cAAc,CACzB1S,KAAK,CAAC,CAAC,EAAEc,GAAG,GAAG,CAAC,CAAC,CACjBuG,KAAK,CAAE6L,CAAC,IAAK,CAACK,0BAA0B,CAACL,CAAC,CAAC3Q,KAAK,EAAE6B,UAAU,EAAEiP,MAAM,CAAC,CAAC,CAAA;AAC1E,KAAA,MAAM;AACLJ,MAAAA,WAAW,GAAGP,cAAc,CAACrL,KAAK,CAC/B6L,CAAC,IAAK,CAACK,0BAA0B,CAACL,CAAC,CAAC3Q,KAAK,EAAE6B,UAAU,EAAEiP,MAAM,CAAC,CAChE,CAAA;AACF,KAAA;AACF,GAAA,MAAM;AACL;AACA;AACAJ,IAAAA,WAAW,GAAGzH,IAAI,CAACiH,aAAa,IAAI,IAAI,CAAA;AACzC,GAAA;AAED,EAAA,IAAIe,MAAc,CAAA;AAClB,EAAA,IAAItX,KAAK,GAAgB;AACvBuX,IAAAA,aAAa,EAAEjI,IAAI,CAAC/N,OAAO,CAACnB,MAAM;AAClCU,IAAAA,QAAQ,EAAEwO,IAAI,CAAC/N,OAAO,CAACT,QAAQ;AAC/B6G,IAAAA,OAAO,EAAE6O,cAAc;IACvBO,WAAW;AACXS,IAAAA,UAAU,EAAExD,eAAe;AAC3B;IACAyD,qBAAqB,EAAEnI,IAAI,CAACiH,aAAa,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI;AAChEmB,IAAAA,kBAAkB,EAAE,KAAK;AACzBC,IAAAA,YAAY,EAAE,MAAM;AACpBzP,IAAAA,UAAU,EAAGoH,IAAI,CAACiH,aAAa,IAAIjH,IAAI,CAACiH,aAAa,CAACrO,UAAU,IAAK,EAAE;IACvE0P,UAAU,EAAGtI,IAAI,CAACiH,aAAa,IAAIjH,IAAI,CAACiH,aAAa,CAACqB,UAAU,IAAK,IAAI;IACzET,MAAM,EAAG7H,IAAI,CAACiH,aAAa,IAAIjH,IAAI,CAACiH,aAAa,CAACY,MAAM,IAAKV,aAAa;AAC1EoB,IAAAA,QAAQ,EAAE,IAAIC,GAAG,EAAE;IACnBC,QAAQ,EAAE,IAAID,GAAG,EAAE;GACpB,CAAA;AAED;AACA;AACA,EAAA,IAAIE,aAAa,GAAkBC,MAAa,CAAC5X,GAAG,CAAA;AAEpD;AACA;EACA,IAAI6X,yBAAyB,GAAG,KAAK,CAAA;AAErC;AACA,EAAA,IAAIC,2BAAmD,CAAA;AAEvD;EACA,IAAIC,4BAA4B,GAAG,KAAK,CAAA;AAExC;AACA,EAAA,IAAIC,sBAAsB,GAA6B,IAAIP,GAAG,EAG3D,CAAA;AAEH;EACA,IAAIQ,2BAA2B,GAAwB,IAAI,CAAA;AAE3D;AACA;EACA,IAAIC,2BAA2B,GAAG,KAAK,CAAA;AAEvC;AACA;AACA;AACA;EACA,IAAIC,sBAAsB,GAAG,KAAK,CAAA;AAElC;AACA;EACA,IAAIC,uBAAuB,GAAa,EAAE,CAAA;AAE1C;AACA;AACA,EAAA,IAAIC,qBAAqB,GAAgB,IAAIvS,GAAG,EAAE,CAAA;AAElD;AACA,EAAA,IAAIwS,gBAAgB,GAAG,IAAIb,GAAG,EAA2B,CAAA;AAEzD;EACA,IAAIc,kBAAkB,GAAG,CAAC,CAAA;AAE1B;AACA;AACA;EACA,IAAIC,uBAAuB,GAAG,CAAC,CAAC,CAAA;AAEhC;AACA,EAAA,IAAIC,cAAc,GAAG,IAAIhB,GAAG,EAAkB,CAAA;AAE9C;AACA,EAAA,IAAIiB,gBAAgB,GAAG,IAAI5S,GAAG,EAAU,CAAA;AAExC;AACA,EAAA,IAAI6S,gBAAgB,GAAG,IAAIlB,GAAG,EAA0B,CAAA;AAExD;AACA,EAAA,IAAImB,cAAc,GAAG,IAAInB,GAAG,EAAkB,CAAA;AAE9C;AACA;AACA,EAAA,IAAIoB,eAAe,GAAG,IAAI/S,GAAG,EAAU,CAAA;AAEvC;AACA;AACA;AACA;AACA,EAAA,IAAIgT,eAAe,GAAG,IAAIrB,GAAG,EAAwB,CAAA;AAErD;AACA;AACA,EAAA,IAAIsB,gBAAgB,GAAG,IAAItB,GAAG,EAA2B,CAAA;AASzD;AACA;EACA,IAAIuB,2BAA2B,GAA6BpZ,SAAS,CAAA;AAErE;AACA;AACA;EACA,SAASqZ,UAAUA,GAAA;AACjB;AACA;IACApD,eAAe,GAAG5G,IAAI,CAAC/N,OAAO,CAACiB,MAAM,CACnCuC,IAAA,IAA+C;MAAA,IAA9C;AAAE3E,QAAAA,MAAM,EAAEmX,aAAa;QAAEzW,QAAQ;AAAEqB,QAAAA,KAAAA;AAAK,OAAE,GAAA4C,IAAA,CAAA;AACzC;AACA;AACA,MAAA,IAAIsU,2BAA2B,EAAE;AAC/BA,QAAAA,2BAA2B,EAAE,CAAA;AAC7BA,QAAAA,2BAA2B,GAAGpZ,SAAS,CAAA;AACvC,QAAA,OAAA;AACD,OAAA;MAEDgB,OAAO,CACLmY,gBAAgB,CAAC3G,IAAI,KAAK,CAAC,IAAItQ,KAAK,IAAI,IAAI,EAC5C,oEAAoE,GAClE,wEAAwE,GACxE,uEAAuE,GACvE,yEAAyE,GACzE,iEAAiE,GACjE,yDAAyD,CAC5D,CAAA;MAED,IAAIoX,UAAU,GAAGC,qBAAqB,CAAC;QACrCC,eAAe,EAAEzZ,KAAK,CAACc,QAAQ;AAC/BmB,QAAAA,YAAY,EAAEnB,QAAQ;AACtByW,QAAAA,aAAAA;AACD,OAAA,CAAC,CAAA;AAEF,MAAA,IAAIgC,UAAU,IAAIpX,KAAK,IAAI,IAAI,EAAE;AAC/B;AACA,QAAA,IAAIuX,wBAAwB,GAAG,IAAIhJ,OAAO,CAAQ8B,OAAO,IAAI;AAC3D6G,UAAAA,2BAA2B,GAAG7G,OAAO,CAAA;AACvC,SAAC,CAAC,CAAA;QACFlD,IAAI,CAAC/N,OAAO,CAACe,EAAE,CAACH,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;AAE3B;QACAwX,aAAa,CAACJ,UAAU,EAAE;AACxBvZ,UAAAA,KAAK,EAAE,SAAS;UAChBc,QAAQ;AACR0T,UAAAA,OAAOA,GAAA;YACLmF,aAAa,CAACJ,UAAW,EAAE;AACzBvZ,cAAAA,KAAK,EAAE,YAAY;AACnBwU,cAAAA,OAAO,EAAEvU,SAAS;AAClBwU,cAAAA,KAAK,EAAExU,SAAS;AAChBa,cAAAA,QAAAA;AACD,aAAA,CAAC,CAAA;AACF;AACA;AACA;AACA4Y,YAAAA,wBAAwB,CAAClI,IAAI,CAAC,MAAMlC,IAAI,CAAC/N,OAAO,CAACe,EAAE,CAACH,KAAK,CAAC,CAAC,CAAA;WAC5D;AACDsS,UAAAA,KAAKA,GAAA;YACH,IAAIsD,QAAQ,GAAG,IAAID,GAAG,CAAC9X,KAAK,CAAC+X,QAAQ,CAAC,CAAA;AACtCA,YAAAA,QAAQ,CAACnI,GAAG,CAAC2J,UAAW,EAAEhF,YAAY,CAAC,CAAA;AACvCqF,YAAAA,WAAW,CAAC;AAAE7B,cAAAA,QAAAA;AAAQ,aAAE,CAAC,CAAA;AAC3B,WAAA;AACD,SAAA,CAAC,CAAA;AACF,QAAA,OAAA;AACD,OAAA;AAED,MAAA,OAAO8B,eAAe,CAACtC,aAAa,EAAEzW,QAAQ,CAAC,CAAA;AACjD,KAAC,CACF,CAAA;AAED,IAAA,IAAImU,SAAS,EAAE;AACb;AACA;AACA6E,MAAAA,yBAAyB,CAAC9E,YAAY,EAAEqD,sBAAsB,CAAC,CAAA;MAC/D,IAAI0B,uBAAuB,GAAGA,MAC5BC,yBAAyB,CAAChF,YAAY,EAAEqD,sBAAsB,CAAC,CAAA;AACjErD,MAAAA,YAAY,CAACjP,gBAAgB,CAAC,UAAU,EAAEgU,uBAAuB,CAAC,CAAA;MAClEzB,2BAA2B,GAAGA,MAC5BtD,YAAY,CAAChP,mBAAmB,CAAC,UAAU,EAAE+T,uBAAuB,CAAC,CAAA;AACxE,KAAA;AAED;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI,CAAC/Z,KAAK,CAAC+W,WAAW,EAAE;MACtB8C,eAAe,CAAC5B,MAAa,CAAC5X,GAAG,EAAEL,KAAK,CAACc,QAAQ,EAAE;AACjDmZ,QAAAA,gBAAgB,EAAE,IAAA;AACnB,OAAA,CAAC,CAAA;AACH,KAAA;AAED,IAAA,OAAO3C,MAAM,CAAA;AACf,GAAA;AAEA;EACA,SAAS4C,OAAOA,GAAA;AACd,IAAA,IAAIhE,eAAe,EAAE;AACnBA,MAAAA,eAAe,EAAE,CAAA;AAClB,KAAA;AACD,IAAA,IAAIoC,2BAA2B,EAAE;AAC/BA,MAAAA,2BAA2B,EAAE,CAAA;AAC9B,KAAA;IACDlI,WAAW,CAAC+J,KAAK,EAAE,CAAA;AACnBhC,IAAAA,2BAA2B,IAAIA,2BAA2B,CAAC9F,KAAK,EAAE,CAAA;AAClErS,IAAAA,KAAK,CAAC6X,QAAQ,CAAC5O,OAAO,CAAC,CAAC+D,CAAC,EAAEnM,GAAG,KAAKuZ,aAAa,CAACvZ,GAAG,CAAC,CAAC,CAAA;AACtDb,IAAAA,KAAK,CAAC+X,QAAQ,CAAC9O,OAAO,CAAC,CAAC+D,CAAC,EAAEnM,GAAG,KAAKwZ,aAAa,CAACxZ,GAAG,CAAC,CAAC,CAAA;AACxD,GAAA;AAEA;EACA,SAASsR,SAASA,CAAC1P,EAAoB,EAAA;AACrC2N,IAAAA,WAAW,CAACiB,GAAG,CAAC5O,EAAE,CAAC,CAAA;AACnB,IAAA,OAAO,MAAM2N,WAAW,CAAC0B,MAAM,CAACrP,EAAE,CAAC,CAAA;AACrC,GAAA;AAEA;AACA,EAAA,SAASmX,WAAWA,CAClBU,QAA8B,EAC9BC,MAGM;AAAA,IAAA,IAHNA;MAAAA,OAGI,EAAE,CAAA;AAAA,KAAA;AAENva,IAAAA,KAAK,GAAA8E,QAAA,CAAA,EAAA,EACA9E,KAAK,EACLsa,QAAQ,CACZ,CAAA;AAED;AACA;IACA,IAAIE,iBAAiB,GAAa,EAAE,CAAA;IACpC,IAAIC,mBAAmB,GAAa,EAAE,CAAA;IAEtC,IAAI7E,MAAM,CAACC,iBAAiB,EAAE;MAC5B7V,KAAK,CAAC6X,QAAQ,CAAC5O,OAAO,CAAC,CAACyR,OAAO,EAAE7Z,GAAG,KAAI;AACtC,QAAA,IAAI6Z,OAAO,CAAC1a,KAAK,KAAK,MAAM,EAAE;AAC5B,UAAA,IAAIkZ,eAAe,CAACvJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;AAC5B;AACA4Z,YAAAA,mBAAmB,CAAC1Y,IAAI,CAAClB,GAAG,CAAC,CAAA;AAC9B,WAAA,MAAM;AACL;AACA;AACA2Z,YAAAA,iBAAiB,CAACzY,IAAI,CAAClB,GAAG,CAAC,CAAA;AAC5B,WAAA;AACF,SAAA;AACH,OAAC,CAAC,CAAA;AACH,KAAA;AAED;AACA;AACA;IACA,CAAC,GAAGuP,WAAW,CAAC,CAACnH,OAAO,CAAEiJ,UAAU,IAClCA,UAAU,CAAClS,KAAK,EAAE;AAChBkZ,MAAAA,eAAe,EAAEuB,mBAAmB;MACpCE,kBAAkB,EAAEJ,IAAI,CAACI,kBAAkB;AAC3CC,MAAAA,SAAS,EAAEL,IAAI,CAACK,SAAS,KAAK,IAAA;AAC/B,KAAA,CAAC,CACH,CAAA;AAED;IACA,IAAIhF,MAAM,CAACC,iBAAiB,EAAE;AAC5B2E,MAAAA,iBAAiB,CAACvR,OAAO,CAAEpI,GAAG,IAAKb,KAAK,CAAC6X,QAAQ,CAAC/F,MAAM,CAACjR,GAAG,CAAC,CAAC,CAAA;MAC9D4Z,mBAAmB,CAACxR,OAAO,CAAEpI,GAAG,IAAKuZ,aAAa,CAACvZ,GAAG,CAAC,CAAC,CAAA;AACzD,KAAA;AACH,GAAA;AAEA;AACA;AACA;AACA;AACA;AACA,EAAA,SAASga,kBAAkBA,CACzB/Z,QAAkB,EAClBwZ,QAA0E,EAAAQ,KAAA,EAC/B;IAAA,IAAAC,eAAA,EAAAC,gBAAA,CAAA;IAAA,IAA3C;AAAEJ,MAAAA,SAAAA;AAAS,KAAA,GAAAE,KAAA,KAAA,KAAA,CAAA,GAA8B,EAAE,GAAAA,KAAA,CAAA;AAE3C;AACA;AACA;AACA;AACA;IACA,IAAIG,cAAc,GAChBjb,KAAK,CAAC4X,UAAU,IAAI,IAAI,IACxB5X,KAAK,CAACwX,UAAU,CAACvD,UAAU,IAAI,IAAI,IACnCiH,gBAAgB,CAAClb,KAAK,CAACwX,UAAU,CAACvD,UAAU,CAAC,IAC7CjU,KAAK,CAACwX,UAAU,CAACxX,KAAK,KAAK,SAAS,IACpC,CAAA,CAAA+a,eAAA,GAAAja,QAAQ,CAACd,KAAK,KAAA,IAAA,GAAA,KAAA,CAAA,GAAd+a,eAAA,CAAgBI,WAAW,MAAK,IAAI,CAAA;AAEtC,IAAA,IAAIvD,UAA4B,CAAA;IAChC,IAAI0C,QAAQ,CAAC1C,UAAU,EAAE;AACvB,MAAA,IAAIlM,MAAM,CAAC0P,IAAI,CAACd,QAAQ,CAAC1C,UAAU,CAAC,CAACzX,MAAM,GAAG,CAAC,EAAE;QAC/CyX,UAAU,GAAG0C,QAAQ,CAAC1C,UAAU,CAAA;AACjC,OAAA,MAAM;AACL;AACAA,QAAAA,UAAU,GAAG,IAAI,CAAA;AAClB,OAAA;KACF,MAAM,IAAIqD,cAAc,EAAE;AACzB;MACArD,UAAU,GAAG5X,KAAK,CAAC4X,UAAU,CAAA;AAC9B,KAAA,MAAM;AACL;AACAA,MAAAA,UAAU,GAAG,IAAI,CAAA;AAClB,KAAA;AAED;AACA,IAAA,IAAI1P,UAAU,GAAGoS,QAAQ,CAACpS,UAAU,GAChCmT,eAAe,CACbrb,KAAK,CAACkI,UAAU,EAChBoS,QAAQ,CAACpS,UAAU,EACnBoS,QAAQ,CAAC3S,OAAO,IAAI,EAAE,EACtB2S,QAAQ,CAACnD,MAAM,CAChB,GACDnX,KAAK,CAACkI,UAAU,CAAA;AAEpB;AACA;AACA,IAAA,IAAI6P,QAAQ,GAAG/X,KAAK,CAAC+X,QAAQ,CAAA;AAC7B,IAAA,IAAIA,QAAQ,CAACtF,IAAI,GAAG,CAAC,EAAE;AACrBsF,MAAAA,QAAQ,GAAG,IAAID,GAAG,CAACC,QAAQ,CAAC,CAAA;AAC5BA,MAAAA,QAAQ,CAAC9O,OAAO,CAAC,CAAC+D,CAAC,EAAEsF,CAAC,KAAKyF,QAAQ,CAACnI,GAAG,CAAC0C,CAAC,EAAEiC,YAAY,CAAC,CAAC,CAAA;AAC1D,KAAA;AAED;AACA;AACA,IAAA,IAAImD,kBAAkB,GACpBQ,yBAAyB,KAAK,IAAI,IACjClY,KAAK,CAACwX,UAAU,CAACvD,UAAU,IAAI,IAAI,IAClCiH,gBAAgB,CAAClb,KAAK,CAACwX,UAAU,CAACvD,UAAU,CAAC,IAC7C,EAAA+G,gBAAA,GAAAla,QAAQ,CAACd,KAAK,KAAdgb,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAAgBG,WAAW,MAAK,IAAK,CAAA;AAEzC;AACA,IAAA,IAAI7F,kBAAkB,EAAE;AACtBD,MAAAA,UAAU,GAAGC,kBAAkB,CAAA;AAC/BA,MAAAA,kBAAkB,GAAGrV,SAAS,CAAA;AAC/B,KAAA;AAED,IAAA,IAAIsY,2BAA2B,EAAE,CAEhC,MAAM,IAAIP,aAAa,KAAKC,MAAa,CAAC5X,GAAG,EAAE,CAE/C,MAAM,IAAI2X,aAAa,KAAKC,MAAa,CAACjW,IAAI,EAAE;MAC/CsN,IAAI,CAAC/N,OAAO,CAACQ,IAAI,CAACjB,QAAQ,EAAEA,QAAQ,CAACd,KAAK,CAAC,CAAA;AAC5C,KAAA,MAAM,IAAIgY,aAAa,KAAKC,MAAa,CAAC5V,OAAO,EAAE;MAClDiN,IAAI,CAAC/N,OAAO,CAACa,OAAO,CAACtB,QAAQ,EAAEA,QAAQ,CAACd,KAAK,CAAC,CAAA;AAC/C,KAAA;AAED,IAAA,IAAI2a,kBAAkD,CAAA;AAEtD;AACA,IAAA,IAAI3C,aAAa,KAAKC,MAAa,CAAC5X,GAAG,EAAE;AACvC;MACA,IAAIib,UAAU,GAAGjD,sBAAsB,CAACzG,GAAG,CAAC5R,KAAK,CAACc,QAAQ,CAACE,QAAQ,CAAC,CAAA;MACpE,IAAIsa,UAAU,IAAIA,UAAU,CAAC3L,GAAG,CAAC7O,QAAQ,CAACE,QAAQ,CAAC,EAAE;AACnD2Z,QAAAA,kBAAkB,GAAG;UACnBlB,eAAe,EAAEzZ,KAAK,CAACc,QAAQ;AAC/BmB,UAAAA,YAAY,EAAEnB,QAAAA;SACf,CAAA;OACF,MAAM,IAAIuX,sBAAsB,CAAC1I,GAAG,CAAC7O,QAAQ,CAACE,QAAQ,CAAC,EAAE;AACxD;AACA;AACA2Z,QAAAA,kBAAkB,GAAG;AACnBlB,UAAAA,eAAe,EAAE3Y,QAAQ;UACzBmB,YAAY,EAAEjC,KAAK,CAACc,QAAAA;SACrB,CAAA;AACF,OAAA;KACF,MAAM,IAAIsX,4BAA4B,EAAE;AACvC;MACA,IAAImD,OAAO,GAAGlD,sBAAsB,CAACzG,GAAG,CAAC5R,KAAK,CAACc,QAAQ,CAACE,QAAQ,CAAC,CAAA;AACjE,MAAA,IAAIua,OAAO,EAAE;AACXA,QAAAA,OAAO,CAAClK,GAAG,CAACvQ,QAAQ,CAACE,QAAQ,CAAC,CAAA;AAC/B,OAAA,MAAM;QACLua,OAAO,GAAG,IAAIpV,GAAG,CAAS,CAACrF,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAA;QAC9CqX,sBAAsB,CAACzI,GAAG,CAAC5P,KAAK,CAACc,QAAQ,CAACE,QAAQ,EAAEua,OAAO,CAAC,CAAA;AAC7D,OAAA;AACDZ,MAAAA,kBAAkB,GAAG;QACnBlB,eAAe,EAAEzZ,KAAK,CAACc,QAAQ;AAC/BmB,QAAAA,YAAY,EAAEnB,QAAAA;OACf,CAAA;AACF,KAAA;IAED8Y,WAAW,CAAA9U,QAAA,CAAA,EAAA,EAEJwV,QAAQ,EAAA;MACX1C,UAAU;MACV1P,UAAU;AACVqP,MAAAA,aAAa,EAAES,aAAa;MAC5BlX,QAAQ;AACRiW,MAAAA,WAAW,EAAE,IAAI;AACjBS,MAAAA,UAAU,EAAExD,eAAe;AAC3B2D,MAAAA,YAAY,EAAE,MAAM;AACpBF,MAAAA,qBAAqB,EAAE+D,sBAAsB,CAC3C1a,QAAQ,EACRwZ,QAAQ,CAAC3S,OAAO,IAAI3H,KAAK,CAAC2H,OAAO,CAClC;MACD+P,kBAAkB;AAClBK,MAAAA,QAAAA;KAEF,CAAA,EAAA;MACE4C,kBAAkB;MAClBC,SAAS,EAAEA,SAAS,KAAK,IAAA;AAC1B,KAAA,CACF,CAAA;AAED;IACA5C,aAAa,GAAGC,MAAa,CAAC5X,GAAG,CAAA;AACjC6X,IAAAA,yBAAyB,GAAG,KAAK,CAAA;AACjCE,IAAAA,4BAA4B,GAAG,KAAK,CAAA;AACpCG,IAAAA,2BAA2B,GAAG,KAAK,CAAA;AACnCC,IAAAA,sBAAsB,GAAG,KAAK,CAAA;AAC9BC,IAAAA,uBAAuB,GAAG,EAAE,CAAA;AAC9B,GAAA;AAEA;AACA;AACA,EAAA,eAAegD,QAAQA,CACrB7a,EAAsB,EACtB2Z,IAA4B,EAAA;AAE5B,IAAA,IAAI,OAAO3Z,EAAE,KAAK,QAAQ,EAAE;AAC1B0O,MAAAA,IAAI,CAAC/N,OAAO,CAACe,EAAE,CAAC1B,EAAE,CAAC,CAAA;AACnB,MAAA,OAAA;AACD,KAAA;AAED,IAAA,IAAI8a,cAAc,GAAGC,WAAW,CAC9B3b,KAAK,CAACc,QAAQ,EACdd,KAAK,CAAC2H,OAAO,EACbP,QAAQ,EACRwO,MAAM,CAACI,kBAAkB,EACzBpV,EAAE,EACFgV,MAAM,CAACvH,oBAAoB,EAC3BkM,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEqB,WAAW,EACjBrB,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEsB,QAAQ,CACf,CAAA;IACD,IAAI;MAAEla,IAAI;MAAEma,UAAU;AAAEpW,MAAAA,KAAAA;AAAK,KAAE,GAAGqW,wBAAwB,CACxDnG,MAAM,CAACE,sBAAsB,EAC7B,KAAK,EACL4F,cAAc,EACdnB,IAAI,CACL,CAAA;AAED,IAAA,IAAId,eAAe,GAAGzZ,KAAK,CAACc,QAAQ,CAAA;AACpC,IAAA,IAAImB,YAAY,GAAGlB,cAAc,CAACf,KAAK,CAACc,QAAQ,EAAEa,IAAI,EAAE4Y,IAAI,IAAIA,IAAI,CAACva,KAAK,CAAC,CAAA;AAE3E;AACA;AACA;AACA;AACA;AACAiC,IAAAA,YAAY,GAAA6C,QAAA,CACP7C,EAAAA,EAAAA,YAAY,EACZqN,IAAI,CAAC/N,OAAO,CAACG,cAAc,CAACO,YAAY,CAAC,CAC7C,CAAA;AAED,IAAA,IAAI+Z,WAAW,GAAGzB,IAAI,IAAIA,IAAI,CAACnY,OAAO,IAAI,IAAI,GAAGmY,IAAI,CAACnY,OAAO,GAAGnC,SAAS,CAAA;AAEzE,IAAA,IAAIsX,aAAa,GAAGU,MAAa,CAACjW,IAAI,CAAA;IAEtC,IAAIga,WAAW,KAAK,IAAI,EAAE;MACxBzE,aAAa,GAAGU,MAAa,CAAC5V,OAAO,CAAA;AACtC,KAAA,MAAM,IAAI2Z,WAAW,KAAK,KAAK,EAAE,CAEjC,MAAM,IACLF,UAAU,IAAI,IAAI,IAClBZ,gBAAgB,CAACY,UAAU,CAAC7H,UAAU,CAAC,IACvC6H,UAAU,CAAC5H,UAAU,KAAKlU,KAAK,CAACc,QAAQ,CAACE,QAAQ,GAAGhB,KAAK,CAACc,QAAQ,CAACe,MAAM,EACzE;AACA;AACA;AACA;AACA;MACA0V,aAAa,GAAGU,MAAa,CAAC5V,OAAO,CAAA;AACtC,KAAA;AAED,IAAA,IAAIqV,kBAAkB,GACpB6C,IAAI,IAAI,oBAAoB,IAAIA,IAAI,GAChCA,IAAI,CAAC7C,kBAAkB,KAAK,IAAI,GAChCzX,SAAS,CAAA;IAEf,IAAI2a,SAAS,GAAG,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAI,CAAA;IAEjD,IAAIrB,UAAU,GAAGC,qBAAqB,CAAC;MACrCC,eAAe;MACfxX,YAAY;AACZsV,MAAAA,aAAAA;AACD,KAAA,CAAC,CAAA;AAEF,IAAA,IAAIgC,UAAU,EAAE;AACd;MACAI,aAAa,CAACJ,UAAU,EAAE;AACxBvZ,QAAAA,KAAK,EAAE,SAAS;AAChBc,QAAAA,QAAQ,EAAEmB,YAAY;AACtBuS,QAAAA,OAAOA,GAAA;UACLmF,aAAa,CAACJ,UAAW,EAAE;AACzBvZ,YAAAA,KAAK,EAAE,YAAY;AACnBwU,YAAAA,OAAO,EAAEvU,SAAS;AAClBwU,YAAAA,KAAK,EAAExU,SAAS;AAChBa,YAAAA,QAAQ,EAAEmB,YAAAA;AACX,WAAA,CAAC,CAAA;AACF;AACAwZ,UAAAA,QAAQ,CAAC7a,EAAE,EAAE2Z,IAAI,CAAC,CAAA;SACnB;AACD9F,QAAAA,KAAKA,GAAA;UACH,IAAIsD,QAAQ,GAAG,IAAID,GAAG,CAAC9X,KAAK,CAAC+X,QAAQ,CAAC,CAAA;AACtCA,UAAAA,QAAQ,CAACnI,GAAG,CAAC2J,UAAW,EAAEhF,YAAY,CAAC,CAAA;AACvCqF,UAAAA,WAAW,CAAC;AAAE7B,YAAAA,QAAAA;AAAQ,WAAE,CAAC,CAAA;AAC3B,SAAA;AACD,OAAA,CAAC,CAAA;AACF,MAAA,OAAA;AACD,KAAA;AAED,IAAA,OAAO,MAAM8B,eAAe,CAACtC,aAAa,EAAEtV,YAAY,EAAE;MACxD6Z,UAAU;AACV;AACA;AACAG,MAAAA,YAAY,EAAEvW,KAAK;MACnBgS,kBAAkB;AAClBtV,MAAAA,OAAO,EAAEmY,IAAI,IAAIA,IAAI,CAACnY,OAAO;AAC7B8Z,MAAAA,oBAAoB,EAAE3B,IAAI,IAAIA,IAAI,CAAC4B,cAAc;AACjDvB,MAAAA,SAAAA;AACD,KAAA,CAAC,CAAA;AACJ,GAAA;AAEA;AACA;AACA;EACA,SAASwB,UAAUA,GAAA;AACjBC,IAAAA,oBAAoB,EAAE,CAAA;AACtBzC,IAAAA,WAAW,CAAC;AAAEjC,MAAAA,YAAY,EAAE,SAAA;AAAS,KAAE,CAAC,CAAA;AAExC;AACA;AACA,IAAA,IAAI3X,KAAK,CAACwX,UAAU,CAACxX,KAAK,KAAK,YAAY,EAAE;AAC3C,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA;AACA,IAAA,IAAIA,KAAK,CAACwX,UAAU,CAACxX,KAAK,KAAK,MAAM,EAAE;MACrC6Z,eAAe,CAAC7Z,KAAK,CAACuX,aAAa,EAAEvX,KAAK,CAACc,QAAQ,EAAE;AACnDwb,QAAAA,8BAA8B,EAAE,IAAA;AACjC,OAAA,CAAC,CAAA;AACF,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA;AACAzC,IAAAA,eAAe,CACb7B,aAAa,IAAIhY,KAAK,CAACuX,aAAa,EACpCvX,KAAK,CAACwX,UAAU,CAAC1W,QAAQ,EACzB;MACEyb,kBAAkB,EAAEvc,KAAK,CAACwX,UAAU;AACpC;MACA0E,oBAAoB,EAAE9D,4BAA4B,KAAK,IAAA;AACxD,KAAA,CACF,CAAA;AACH,GAAA;AAEA;AACA;AACA;AACA,EAAA,eAAeyB,eAAeA,CAC5BtC,aAA4B,EAC5BzW,QAAkB,EAClByZ,IAWC,EAAA;AAED;AACA;AACA;AACApC,IAAAA,2BAA2B,IAAIA,2BAA2B,CAAC9F,KAAK,EAAE,CAAA;AAClE8F,IAAAA,2BAA2B,GAAG,IAAI,CAAA;AAClCH,IAAAA,aAAa,GAAGT,aAAa,CAAA;IAC7BgB,2BAA2B,GACzB,CAACgC,IAAI,IAAIA,IAAI,CAAC+B,8BAA8B,MAAM,IAAI,CAAA;AAExD;AACA;IACAE,kBAAkB,CAACxc,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAAC2H,OAAO,CAAC,CAAA;IACjDuQ,yBAAyB,GAAG,CAACqC,IAAI,IAAIA,IAAI,CAAC7C,kBAAkB,MAAM,IAAI,CAAA;IAEtEU,4BAA4B,GAAG,CAACmC,IAAI,IAAIA,IAAI,CAAC2B,oBAAoB,MAAM,IAAI,CAAA;AAE3E,IAAA,IAAIO,WAAW,GAAGnH,kBAAkB,IAAID,UAAU,CAAA;AAClD,IAAA,IAAIqH,iBAAiB,GAAGnC,IAAI,IAAIA,IAAI,CAACgC,kBAAkB,CAAA;IACvD,IAAI5U,OAAO,GAAGT,WAAW,CAACuV,WAAW,EAAE3b,QAAQ,EAAEsG,QAAQ,CAAC,CAAA;IAC1D,IAAIwT,SAAS,GAAG,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAI,CAAA;IAEjD,IAAIhE,QAAQ,GAAGC,aAAa,CAAClP,OAAO,EAAE8U,WAAW,EAAE3b,QAAQ,CAACE,QAAQ,CAAC,CAAA;AACrE,IAAA,IAAI4V,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACjP,OAAO,EAAE;MACvCA,OAAO,GAAGiP,QAAQ,CAACjP,OAAO,CAAA;AAC3B,KAAA;AAED;IACA,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI;QAAEjC,KAAK;QAAEiX,eAAe;AAAEtW,QAAAA,KAAAA;AAAK,OAAE,GAAGuW,qBAAqB,CAC3D9b,QAAQ,CAACE,QAAQ,CAClB,CAAA;MACD6Z,kBAAkB,CAChB/Z,QAAQ,EACR;AACE6G,QAAAA,OAAO,EAAEgV,eAAe;QACxBzU,UAAU,EAAE,EAAE;AACdiP,QAAAA,MAAM,EAAE;UACN,CAAC9Q,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;AACb,SAAA;AACF,OAAA,EACD;AAAEkV,QAAAA,SAAAA;AAAW,OAAA,CACd,CAAA;AACD,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IACE5a,KAAK,CAAC+W,WAAW,IACjB,CAACyB,sBAAsB,IACvBqE,gBAAgB,CAAC7c,KAAK,CAACc,QAAQ,EAAEA,QAAQ,CAAC,IAC1C,EAAEyZ,IAAI,IAAIA,IAAI,CAACuB,UAAU,IAAIZ,gBAAgB,CAACX,IAAI,CAACuB,UAAU,CAAC7H,UAAU,CAAC,CAAC,EAC1E;MACA4G,kBAAkB,CAAC/Z,QAAQ,EAAE;AAAE6G,QAAAA,OAAAA;AAAS,OAAA,EAAE;AAAEiT,QAAAA,SAAAA;AAAW,OAAA,CAAC,CAAA;AACxD,MAAA,OAAA;AACD,KAAA;AAED;AACAzC,IAAAA,2BAA2B,GAAG,IAAItH,eAAe,EAAE,CAAA;AACnD,IAAA,IAAIiM,OAAO,GAAGC,uBAAuB,CACnCzN,IAAI,CAAC/N,OAAO,EACZT,QAAQ,EACRqX,2BAA2B,CAACnH,MAAM,EAClCuJ,IAAI,IAAIA,IAAI,CAACuB,UAAU,CACxB,CAAA;AACD,IAAA,IAAIkB,mBAAoD,CAAA;AAExD,IAAA,IAAIzC,IAAI,IAAIA,IAAI,CAAC0B,YAAY,EAAE;AAC7B;AACA;AACA;AACA;MACAe,mBAAmB,GAAG,CACpBC,mBAAmB,CAACtV,OAAO,CAAC,CAACtB,KAAK,CAACQ,EAAE,EACrC;QAAEmJ,IAAI,EAAE/J,UAAU,CAACP,KAAK;QAAEA,KAAK,EAAE6U,IAAI,CAAC0B,YAAAA;AAAc,OAAA,CACrD,CAAA;AACF,KAAA,MAAM,IACL1B,IAAI,IACJA,IAAI,CAACuB,UAAU,IACfZ,gBAAgB,CAACX,IAAI,CAACuB,UAAU,CAAC7H,UAAU,CAAC,EAC5C;AACA;AACA,MAAA,IAAIiJ,YAAY,GAAG,MAAMC,YAAY,CACnCL,OAAO,EACPhc,QAAQ,EACRyZ,IAAI,CAACuB,UAAU,EACfnU,OAAO,EACPiP,QAAQ,CAACE,MAAM,EACf;QAAE1U,OAAO,EAAEmY,IAAI,CAACnY,OAAO;AAAEwY,QAAAA,SAAAA;AAAS,OAAE,CACrC,CAAA;MAED,IAAIsC,YAAY,CAACE,cAAc,EAAE;AAC/B,QAAA,OAAA;AACD,OAAA;AAED;AACA;MACA,IAAIF,YAAY,CAACF,mBAAmB,EAAE;QACpC,IAAI,CAACK,OAAO,EAAEvT,MAAM,CAAC,GAAGoT,YAAY,CAACF,mBAAmB,CAAA;AACxD,QAAA,IACEM,aAAa,CAACxT,MAAM,CAAC,IACrB2J,oBAAoB,CAAC3J,MAAM,CAACpE,KAAK,CAAC,IAClCoE,MAAM,CAACpE,KAAK,CAAC8J,MAAM,KAAK,GAAG,EAC3B;AACA2I,UAAAA,2BAA2B,GAAG,IAAI,CAAA;UAElC0C,kBAAkB,CAAC/Z,QAAQ,EAAE;YAC3B6G,OAAO,EAAEuV,YAAY,CAACvV,OAAO;YAC7BO,UAAU,EAAE,EAAE;AACdiP,YAAAA,MAAM,EAAE;cACN,CAACkG,OAAO,GAAGvT,MAAM,CAACpE,KAAAA;AACnB,aAAA;AACF,WAAA,CAAC,CAAA;AACF,UAAA,OAAA;AACD,SAAA;AACF,OAAA;AAEDiC,MAAAA,OAAO,GAAGuV,YAAY,CAACvV,OAAO,IAAIA,OAAO,CAAA;MACzCqV,mBAAmB,GAAGE,YAAY,CAACF,mBAAmB,CAAA;MACtDN,iBAAiB,GAAGa,oBAAoB,CAACzc,QAAQ,EAAEyZ,IAAI,CAACuB,UAAU,CAAC,CAAA;AACnElB,MAAAA,SAAS,GAAG,KAAK,CAAA;AACjB;MACAhE,QAAQ,CAACE,MAAM,GAAG,KAAK,CAAA;AAEvB;AACAgG,MAAAA,OAAO,GAAGC,uBAAuB,CAC/BzN,IAAI,CAAC/N,OAAO,EACZub,OAAO,CAACnZ,GAAG,EACXmZ,OAAO,CAAC9L,MAAM,CACf,CAAA;AACF,KAAA;AAED;IACA,IAAI;MACFoM,cAAc;AACdzV,MAAAA,OAAO,EAAE6V,cAAc;MACvBtV,UAAU;AACViP,MAAAA,MAAAA;KACD,GAAG,MAAMsG,aAAa,CACrBX,OAAO,EACPhc,QAAQ,EACR6G,OAAO,EACPiP,QAAQ,CAACE,MAAM,EACf4F,iBAAiB,EACjBnC,IAAI,IAAIA,IAAI,CAACuB,UAAU,EACvBvB,IAAI,IAAIA,IAAI,CAACmD,iBAAiB,EAC9BnD,IAAI,IAAIA,IAAI,CAACnY,OAAO,EACpBmY,IAAI,IAAIA,IAAI,CAACN,gBAAgB,KAAK,IAAI,EACtCW,SAAS,EACToC,mBAAmB,CACpB,CAAA;AAED,IAAA,IAAII,cAAc,EAAE;AAClB,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA;AACAjF,IAAAA,2BAA2B,GAAG,IAAI,CAAA;IAElC0C,kBAAkB,CAAC/Z,QAAQ,EAAAgE,QAAA,CAAA;MACzB6C,OAAO,EAAE6V,cAAc,IAAI7V,OAAAA;KACxBgW,EAAAA,sBAAsB,CAACX,mBAAmB,CAAC,EAAA;MAC9C9U,UAAU;AACViP,MAAAA,MAAAA;AAAM,KAAA,CACP,CAAC,CAAA;AACJ,GAAA;AAEA;AACA;AACA,EAAA,eAAegG,YAAYA,CACzBL,OAAgB,EAChBhc,QAAkB,EAClBgb,UAAsB,EACtBnU,OAAiC,EACjCiW,UAAmB,EACnBrD,MAAqD;AAAA,IAAA,IAArDA;MAAAA,OAAmD,EAAE,CAAA;AAAA,KAAA;AAErD8B,IAAAA,oBAAoB,EAAE,CAAA;AAEtB;AACA,IAAA,IAAI7E,UAAU,GAAGqG,uBAAuB,CAAC/c,QAAQ,EAAEgb,UAAU,CAAC,CAAA;AAC9DlC,IAAAA,WAAW,CAAC;AAAEpC,MAAAA,UAAAA;AAAU,KAAE,EAAE;AAAEoD,MAAAA,SAAS,EAAEL,IAAI,CAACK,SAAS,KAAK,IAAA;AAAI,KAAE,CAAC,CAAA;AAEnE,IAAA,IAAIgD,UAAU,EAAE;AACd,MAAA,IAAIE,cAAc,GAAG,MAAMC,cAAc,CACvCpW,OAAO,EACP7G,QAAQ,CAACE,QAAQ,EACjB8b,OAAO,CAAC9L,MAAM,CACf,CAAA;AACD,MAAA,IAAI8M,cAAc,CAAC9N,IAAI,KAAK,SAAS,EAAE;QACrC,OAAO;AAAEoN,UAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;AAChC,OAAA,MAAM,IAAIU,cAAc,CAAC9N,IAAI,KAAK,OAAO,EAAE;QAC1C,IAAIgO,UAAU,GAAGf,mBAAmB,CAACa,cAAc,CAACG,cAAc,CAAC,CAChE5X,KAAK,CAACQ,EAAE,CAAA;QACX,OAAO;UACLc,OAAO,EAAEmW,cAAc,CAACG,cAAc;UACtCjB,mBAAmB,EAAE,CACnBgB,UAAU,EACV;YACEhO,IAAI,EAAE/J,UAAU,CAACP,KAAK;YACtBA,KAAK,EAAEoY,cAAc,CAACpY,KAAAA;WACvB,CAAA;SAEJ,CAAA;AACF,OAAA,MAAM,IAAI,CAACoY,cAAc,CAACnW,OAAO,EAAE;QAClC,IAAI;UAAEgV,eAAe;UAAEjX,KAAK;AAAEW,UAAAA,KAAAA;AAAK,SAAE,GAAGuW,qBAAqB,CAC3D9b,QAAQ,CAACE,QAAQ,CAClB,CAAA;QACD,OAAO;AACL2G,UAAAA,OAAO,EAAEgV,eAAe;AACxBK,UAAAA,mBAAmB,EAAE,CACnB3W,KAAK,CAACQ,EAAE,EACR;YACEmJ,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,YAAAA,KAAAA;WACD,CAAA;SAEJ,CAAA;AACF,OAAA,MAAM;QACLiC,OAAO,GAAGmW,cAAc,CAACnW,OAAO,CAAA;AACjC,OAAA;AACF,KAAA;AAED;AACA,IAAA,IAAImC,MAAkB,CAAA;AACtB,IAAA,IAAIoU,WAAW,GAAGC,cAAc,CAACxW,OAAO,EAAE7G,QAAQ,CAAC,CAAA;AAEnD,IAAA,IAAI,CAACod,WAAW,CAAC7X,KAAK,CAACjG,MAAM,IAAI,CAAC8d,WAAW,CAAC7X,KAAK,CAAC4Q,IAAI,EAAE;AACxDnN,MAAAA,MAAM,GAAG;QACPkG,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAK,EAAEgR,sBAAsB,CAAC,GAAG,EAAE;UACjC0H,MAAM,EAAEtB,OAAO,CAACsB,MAAM;UACtBpd,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;AAC3Bqc,UAAAA,OAAO,EAAEa,WAAW,CAAC7X,KAAK,CAACQ,EAAAA;SAC5B,CAAA;OACF,CAAA;AACF,KAAA,MAAM;AACL,MAAA,IAAIwX,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRte,KAAK,EACL8c,OAAO,EACP,CAACoB,WAAW,CAAC,EACbvW,OAAO,EACP,IAAI,CACL,CAAA;MACDmC,MAAM,GAAGuU,OAAO,CAACH,WAAW,CAAC7X,KAAK,CAACQ,EAAE,CAAC,CAAA;AAEtC,MAAA,IAAIiW,OAAO,CAAC9L,MAAM,CAACa,OAAO,EAAE;QAC1B,OAAO;AAAEuL,UAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;AAChC,OAAA;AACF,KAAA;AAED,IAAA,IAAImB,gBAAgB,CAACzU,MAAM,CAAC,EAAE;AAC5B,MAAA,IAAI1H,OAAgB,CAAA;AACpB,MAAA,IAAImY,IAAI,IAAIA,IAAI,CAACnY,OAAO,IAAI,IAAI,EAAE;QAChCA,OAAO,GAAGmY,IAAI,CAACnY,OAAO,CAAA;AACvB,OAAA,MAAM;AACL;AACA;AACA;QACA,IAAItB,QAAQ,GAAG0d,yBAAyB,CACtC1U,MAAM,CAACuJ,QAAQ,CAAC5D,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAE,EACxC,IAAInQ,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,EACpByD,QAAQ,CACT,CAAA;AACDhF,QAAAA,OAAO,GAAGtB,QAAQ,KAAKd,KAAK,CAACc,QAAQ,CAACE,QAAQ,GAAGhB,KAAK,CAACc,QAAQ,CAACe,MAAM,CAAA;AACvE,OAAA;AACD,MAAA,MAAM4c,uBAAuB,CAAC3B,OAAO,EAAEhT,MAAM,EAAE,IAAI,EAAE;QACnDgS,UAAU;AACV1Z,QAAAA,OAAAA;AACD,OAAA,CAAC,CAAA;MACF,OAAO;AAAEgb,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AAChC,KAAA;AAED,IAAA,IAAIsB,gBAAgB,CAAC5U,MAAM,CAAC,EAAE;MAC5B,MAAM4M,sBAAsB,CAAC,GAAG,EAAE;AAAE1G,QAAAA,IAAI,EAAE,cAAA;AAAgB,OAAA,CAAC,CAAA;AAC5D,KAAA;AAED,IAAA,IAAIsN,aAAa,CAACxT,MAAM,CAAC,EAAE;AACzB;AACA;MACA,IAAI6U,aAAa,GAAG1B,mBAAmB,CAACtV,OAAO,EAAEuW,WAAW,CAAC7X,KAAK,CAACQ,EAAE,CAAC,CAAA;AAEtE;AACA;AACA;AACA;AACA;MACA,IAAI,CAAC0T,IAAI,IAAIA,IAAI,CAACnY,OAAO,MAAM,IAAI,EAAE;QACnC4V,aAAa,GAAGC,MAAa,CAACjW,IAAI,CAAA;AACnC,OAAA;MAED,OAAO;QACL2F,OAAO;QACPqV,mBAAmB,EAAE,CAAC2B,aAAa,CAACtY,KAAK,CAACQ,EAAE,EAAEiD,MAAM,CAAA;OACrD,CAAA;AACF,KAAA;IAED,OAAO;MACLnC,OAAO;MACPqV,mBAAmB,EAAE,CAACkB,WAAW,CAAC7X,KAAK,CAACQ,EAAE,EAAEiD,MAAM,CAAA;KACnD,CAAA;AACH,GAAA;AAEA;AACA;EACA,eAAe2T,aAAaA,CAC1BX,OAAgB,EAChBhc,QAAkB,EAClB6G,OAAiC,EACjCiW,UAAmB,EACnBrB,kBAA+B,EAC/BT,UAAuB,EACvB4B,iBAA8B,EAC9Btb,OAAiB,EACjB6X,gBAA0B,EAC1BW,SAAmB,EACnBoC,mBAAyC,EAAA;AAEzC;IACA,IAAIN,iBAAiB,GACnBH,kBAAkB,IAAIgB,oBAAoB,CAACzc,QAAQ,EAAEgb,UAAU,CAAC,CAAA;AAElE;AACA;IACA,IAAI8C,gBAAgB,GAClB9C,UAAU,IACV4B,iBAAiB,IACjBmB,2BAA2B,CAACnC,iBAAiB,CAAC,CAAA;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAIoC,2BAA2B,GAC7B,CAACvG,2BAA2B,KAC3B,CAAC3C,MAAM,CAACG,mBAAmB,IAAI,CAACkE,gBAAgB,CAAC,CAAA;AAEpD;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI2D,UAAU,EAAE;AACd,MAAA,IAAIkB,2BAA2B,EAAE;AAC/B,QAAA,IAAIlH,UAAU,GAAGmH,oBAAoB,CAAC/B,mBAAmB,CAAC,CAAA;AAC1DpD,QAAAA,WAAW,CAAA9U,QAAA,CAAA;AAEP0S,UAAAA,UAAU,EAAEkF,iBAAAA;SACR9E,EAAAA,UAAU,KAAK3X,SAAS,GAAG;AAAE2X,UAAAA,UAAAA;SAAY,GAAG,EAAE,CAEpD,EAAA;AACEgD,UAAAA,SAAAA;AACD,SAAA,CACF,CAAA;AACF,OAAA;AAED,MAAA,IAAIkD,cAAc,GAAG,MAAMC,cAAc,CACvCpW,OAAO,EACP7G,QAAQ,CAACE,QAAQ,EACjB8b,OAAO,CAAC9L,MAAM,CACf,CAAA;AAED,MAAA,IAAI8M,cAAc,CAAC9N,IAAI,KAAK,SAAS,EAAE;QACrC,OAAO;AAAEoN,UAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;AAChC,OAAA,MAAM,IAAIU,cAAc,CAAC9N,IAAI,KAAK,OAAO,EAAE;QAC1C,IAAIgO,UAAU,GAAGf,mBAAmB,CAACa,cAAc,CAACG,cAAc,CAAC,CAChE5X,KAAK,CAACQ,EAAE,CAAA;QACX,OAAO;UACLc,OAAO,EAAEmW,cAAc,CAACG,cAAc;UACtC/V,UAAU,EAAE,EAAE;AACdiP,UAAAA,MAAM,EAAE;YACN,CAAC6G,UAAU,GAAGF,cAAc,CAACpY,KAAAA;AAC9B,WAAA;SACF,CAAA;AACF,OAAA,MAAM,IAAI,CAACoY,cAAc,CAACnW,OAAO,EAAE;QAClC,IAAI;UAAEjC,KAAK;UAAEiX,eAAe;AAAEtW,UAAAA,KAAAA;AAAK,SAAE,GAAGuW,qBAAqB,CAC3D9b,QAAQ,CAACE,QAAQ,CAClB,CAAA;QACD,OAAO;AACL2G,UAAAA,OAAO,EAAEgV,eAAe;UACxBzU,UAAU,EAAE,EAAE;AACdiP,UAAAA,MAAM,EAAE;YACN,CAAC9Q,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;AACb,WAAA;SACF,CAAA;AACF,OAAA,MAAM;QACLiC,OAAO,GAAGmW,cAAc,CAACnW,OAAO,CAAA;AACjC,OAAA;AACF,KAAA;AAED,IAAA,IAAI8U,WAAW,GAAGnH,kBAAkB,IAAID,UAAU,CAAA;IAClD,IAAI,CAAC2J,aAAa,EAAEC,oBAAoB,CAAC,GAAGC,gBAAgB,CAC1D5P,IAAI,CAAC/N,OAAO,EACZvB,KAAK,EACL2H,OAAO,EACPiX,gBAAgB,EAChB9d,QAAQ,EACR8U,MAAM,CAACG,mBAAmB,IAAIkE,gBAAgB,KAAK,IAAI,EACvDrE,MAAM,CAACK,8BAA8B,EACrCuC,sBAAsB,EACtBC,uBAAuB,EACvBC,qBAAqB,EACrBQ,eAAe,EACfF,gBAAgB,EAChBD,gBAAgB,EAChB0D,WAAW,EACXrV,QAAQ,EACR4V,mBAAmB,CACpB,CAAA;AAED;AACA;AACA;AACAmC,IAAAA,qBAAqB,CAClB9B,OAAO,IACN,EAAE1V,OAAO,IAAIA,OAAO,CAACkD,IAAI,CAAEmM,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAKwW,OAAO,CAAC,CAAC,IACxD2B,aAAa,IAAIA,aAAa,CAACnU,IAAI,CAAEmM,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAKwW,OAAO,CAAE,CACvE,CAAA;IAEDxE,uBAAuB,GAAG,EAAED,kBAAkB,CAAA;AAE9C;IACA,IAAIoG,aAAa,CAAC7e,MAAM,KAAK,CAAC,IAAI8e,oBAAoB,CAAC9e,MAAM,KAAK,CAAC,EAAE;AACnE,MAAA,IAAIif,eAAe,GAAGC,sBAAsB,EAAE,CAAA;MAC9CxE,kBAAkB,CAChB/Z,QAAQ,EAAAgE,QAAA,CAAA;QAEN6C,OAAO;QACPO,UAAU,EAAE,EAAE;AACd;QACAiP,MAAM,EACJ6F,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACxD;UAAE,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,CAACtX,KAAAA;AAAO,SAAA,GAC1D,IAAA;AAAI,OAAA,EACPiY,sBAAsB,CAACX,mBAAmB,CAAC,EAC1CoC,eAAe,GAAG;AAAEvH,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAA;OAAG,GAAG,EAAE,CAElE,EAAA;AAAE+C,QAAAA,SAAAA;AAAW,OAAA,CACd,CAAA;MACD,OAAO;AAAEwC,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AAChC,KAAA;AAED,IAAA,IAAI0B,2BAA2B,EAAE;MAC/B,IAAIQ,OAAO,GAAyB,EAAE,CAAA;MACtC,IAAI,CAAC1B,UAAU,EAAE;AACf;QACA0B,OAAO,CAAC9H,UAAU,GAAGkF,iBAAiB,CAAA;AACtC,QAAA,IAAI9E,UAAU,GAAGmH,oBAAoB,CAAC/B,mBAAmB,CAAC,CAAA;QAC1D,IAAIpF,UAAU,KAAK3X,SAAS,EAAE;UAC5Bqf,OAAO,CAAC1H,UAAU,GAAGA,UAAU,CAAA;AAChC,SAAA;AACF,OAAA;AACD,MAAA,IAAIqH,oBAAoB,CAAC9e,MAAM,GAAG,CAAC,EAAE;AACnCmf,QAAAA,OAAO,CAACzH,QAAQ,GAAG0H,8BAA8B,CAACN,oBAAoB,CAAC,CAAA;AACxE,OAAA;MACDrF,WAAW,CAAC0F,OAAO,EAAE;AAAE1E,QAAAA,SAAAA;AAAS,OAAE,CAAC,CAAA;AACpC,KAAA;AAEDqE,IAAAA,oBAAoB,CAAChW,OAAO,CAAEuW,EAAE,IAAI;AAClCC,MAAAA,YAAY,CAACD,EAAE,CAAC3e,GAAG,CAAC,CAAA;MACpB,IAAI2e,EAAE,CAAC5O,UAAU,EAAE;AACjB;AACA;AACA;QACA+H,gBAAgB,CAAC/I,GAAG,CAAC4P,EAAE,CAAC3e,GAAG,EAAE2e,EAAE,CAAC5O,UAAU,CAAC,CAAA;AAC5C,OAAA;AACH,KAAC,CAAC,CAAA;AAEF;AACA,IAAA,IAAI8O,8BAA8B,GAAGA,MACnCT,oBAAoB,CAAChW,OAAO,CAAE0W,CAAC,IAAKF,YAAY,CAACE,CAAC,CAAC9e,GAAG,CAAC,CAAC,CAAA;AAC1D,IAAA,IAAIsX,2BAA2B,EAAE;MAC/BA,2BAA2B,CAACnH,MAAM,CAACjL,gBAAgB,CACjD,OAAO,EACP2Z,8BAA8B,CAC/B,CAAA;AACF,KAAA;IAED,IAAI;MAAEE,aAAa;AAAEC,MAAAA,cAAAA;AAAgB,KAAA,GACnC,MAAMC,8BAA8B,CAClC9f,KAAK,EACL2H,OAAO,EACPqX,aAAa,EACbC,oBAAoB,EACpBnC,OAAO,CACR,CAAA;AAEH,IAAA,IAAIA,OAAO,CAAC9L,MAAM,CAACa,OAAO,EAAE;MAC1B,OAAO;AAAEuL,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AAChC,KAAA;AAED;AACA;AACA;AACA,IAAA,IAAIjF,2BAA2B,EAAE;MAC/BA,2BAA2B,CAACnH,MAAM,CAAChL,mBAAmB,CACpD,OAAO,EACP0Z,8BAA8B,CAC/B,CAAA;AACF,KAAA;AAEDT,IAAAA,oBAAoB,CAAChW,OAAO,CAAEuW,EAAE,IAAK7G,gBAAgB,CAAC7G,MAAM,CAAC0N,EAAE,CAAC3e,GAAG,CAAC,CAAC,CAAA;AAErE;AACA,IAAA,IAAIsS,QAAQ,GAAG4M,YAAY,CAACH,aAAa,CAAC,CAAA;AAC1C,IAAA,IAAIzM,QAAQ,EAAE;MACZ,MAAMsL,uBAAuB,CAAC3B,OAAO,EAAE3J,QAAQ,CAACrJ,MAAM,EAAE,IAAI,EAAE;AAC5D1H,QAAAA,OAAAA;AACD,OAAA,CAAC,CAAA;MACF,OAAO;AAAEgb,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AAChC,KAAA;AAEDjK,IAAAA,QAAQ,GAAG4M,YAAY,CAACF,cAAc,CAAC,CAAA;AACvC,IAAA,IAAI1M,QAAQ,EAAE;AACZ;AACA;AACA;AACA4F,MAAAA,gBAAgB,CAAC1H,GAAG,CAAC8B,QAAQ,CAACtS,GAAG,CAAC,CAAA;MAClC,MAAM4d,uBAAuB,CAAC3B,OAAO,EAAE3J,QAAQ,CAACrJ,MAAM,EAAE,IAAI,EAAE;AAC5D1H,QAAAA,OAAAA;AACD,OAAA,CAAC,CAAA;MACF,OAAO;AAAEgb,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AAChC,KAAA;AAED;IACA,IAAI;MAAElV,UAAU;AAAEiP,MAAAA,MAAAA;KAAQ,GAAG6I,iBAAiB,CAC5ChgB,KAAK,EACL2H,OAAO,EACPiY,aAAa,EACb5C,mBAAmB,EACnBiC,oBAAoB,EACpBY,cAAc,EACd1G,eAAe,CAChB,CAAA;AAED;AACAA,IAAAA,eAAe,CAAClQ,OAAO,CAAC,CAACgX,YAAY,EAAE5C,OAAO,KAAI;AAChD4C,MAAAA,YAAY,CAAC9N,SAAS,CAAEN,OAAO,IAAI;AACjC;AACA;AACA;AACA,QAAA,IAAIA,OAAO,IAAIoO,YAAY,CAAC7O,IAAI,EAAE;AAChC+H,UAAAA,eAAe,CAACrH,MAAM,CAACuL,OAAO,CAAC,CAAA;AAChC,SAAA;AACH,OAAC,CAAC,CAAA;AACJ,KAAC,CAAC,CAAA;AAEF;IACA,IAAIzH,MAAM,CAACG,mBAAmB,IAAIkE,gBAAgB,IAAIja,KAAK,CAACmX,MAAM,EAAE;MAClEA,MAAM,GAAArS,QAAA,CAAQ9E,EAAAA,EAAAA,KAAK,CAACmX,MAAM,EAAKA,MAAM,CAAE,CAAA;AACxC,KAAA;AAED,IAAA,IAAIiI,eAAe,GAAGC,sBAAsB,EAAE,CAAA;AAC9C,IAAA,IAAIa,kBAAkB,GAAGC,oBAAoB,CAACtH,uBAAuB,CAAC,CAAA;IACtE,IAAIuH,oBAAoB,GACtBhB,eAAe,IAAIc,kBAAkB,IAAIjB,oBAAoB,CAAC9e,MAAM,GAAG,CAAC,CAAA;AAE1E,IAAA,OAAA2E,QAAA,CAAA;MACE6C,OAAO;MACPO,UAAU;AACViP,MAAAA,MAAAA;AAAM,KAAA,EACFiJ,oBAAoB,GAAG;AAAEvI,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAA;KAAG,GAAG,EAAE,CAAA,CAAA;AAEzE,GAAA;EAEA,SAASkH,oBAAoBA,CAC3B/B,mBAAoD,EAAA;IAEpD,IAAIA,mBAAmB,IAAI,CAACM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE;AACjE;AACA;AACA;MACA,OAAO;QACL,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,CAAC5U,IAAAA;OAClD,CAAA;AACF,KAAA,MAAM,IAAIpI,KAAK,CAAC4X,UAAU,EAAE;AAC3B,MAAA,IAAIlM,MAAM,CAAC0P,IAAI,CAACpb,KAAK,CAAC4X,UAAU,CAAC,CAACzX,MAAM,KAAK,CAAC,EAAE;AAC9C,QAAA,OAAO,IAAI,CAAA;AACZ,OAAA,MAAM;QACL,OAAOH,KAAK,CAAC4X,UAAU,CAAA;AACxB,OAAA;AACF,KAAA;AACH,GAAA;EAEA,SAAS2H,8BAA8BA,CACrCN,oBAA2C,EAAA;AAE3CA,IAAAA,oBAAoB,CAAChW,OAAO,CAAEuW,EAAE,IAAI;MAClC,IAAI9E,OAAO,GAAG1a,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC4N,EAAE,CAAC3e,GAAG,CAAC,CAAA;AACxC,MAAA,IAAIwf,mBAAmB,GAAGC,iBAAiB,CACzCrgB,SAAS,EACTya,OAAO,GAAGA,OAAO,CAACtS,IAAI,GAAGnI,SAAS,CACnC,CAAA;MACDD,KAAK,CAAC6X,QAAQ,CAACjI,GAAG,CAAC4P,EAAE,CAAC3e,GAAG,EAAEwf,mBAAmB,CAAC,CAAA;AACjD,KAAC,CAAC,CAAA;AACF,IAAA,OAAO,IAAIvI,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAC,CAAA;AAChC,GAAA;AAEA;EACA,SAAS0I,KAAKA,CACZ1f,GAAW,EACXwc,OAAe,EACf5Z,IAAmB,EACnB8W,IAAyB,EAAA;AAEzB,IAAA,IAAIpF,QAAQ,EAAE;MACZ,MAAM,IAAIhR,KAAK,CACb,2EAA2E,GACzE,8EAA8E,GAC9E,6CAA6C,CAChD,CAAA;AACF,KAAA;IAEDsb,YAAY,CAAC5e,GAAG,CAAC,CAAA;IAEjB,IAAI+Z,SAAS,GAAG,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAI,CAAA;AAEjD,IAAA,IAAI6B,WAAW,GAAGnH,kBAAkB,IAAID,UAAU,CAAA;AAClD,IAAA,IAAIqG,cAAc,GAAGC,WAAW,CAC9B3b,KAAK,CAACc,QAAQ,EACdd,KAAK,CAAC2H,OAAO,EACbP,QAAQ,EACRwO,MAAM,CAACI,kBAAkB,EACzBvS,IAAI,EACJmS,MAAM,CAACvH,oBAAoB,EAC3BgP,OAAO,EACP9C,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEsB,QAAQ,CACf,CAAA;IACD,IAAIlU,OAAO,GAAGT,WAAW,CAACuV,WAAW,EAAEf,cAAc,EAAEtU,QAAQ,CAAC,CAAA;IAEhE,IAAIwP,QAAQ,GAAGC,aAAa,CAAClP,OAAO,EAAE8U,WAAW,EAAEf,cAAc,CAAC,CAAA;AAClE,IAAA,IAAI9E,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACjP,OAAO,EAAE;MACvCA,OAAO,GAAGiP,QAAQ,CAACjP,OAAO,CAAA;AAC3B,KAAA;IAED,IAAI,CAACA,OAAO,EAAE;MACZ6Y,eAAe,CACb3f,GAAG,EACHwc,OAAO,EACP3G,sBAAsB,CAAC,GAAG,EAAE;AAAE1V,QAAAA,QAAQ,EAAE0a,cAAAA;OAAgB,CAAC,EACzD;AAAEd,QAAAA,SAAAA;AAAS,OAAE,CACd,CAAA;AACD,MAAA,OAAA;AACD,KAAA;IAED,IAAI;MAAEjZ,IAAI;MAAEma,UAAU;AAAEpW,MAAAA,KAAAA;AAAK,KAAE,GAAGqW,wBAAwB,CACxDnG,MAAM,CAACE,sBAAsB,EAC7B,IAAI,EACJ4F,cAAc,EACdnB,IAAI,CACL,CAAA;AAED,IAAA,IAAI7U,KAAK,EAAE;AACT8a,MAAAA,eAAe,CAAC3f,GAAG,EAAEwc,OAAO,EAAE3X,KAAK,EAAE;AAAEkV,QAAAA,SAAAA;AAAW,OAAA,CAAC,CAAA;AACnD,MAAA,OAAA;AACD,KAAA;AAED,IAAA,IAAI3S,KAAK,GAAGkW,cAAc,CAACxW,OAAO,EAAEhG,IAAI,CAAC,CAAA;IAEzC,IAAI+V,kBAAkB,GAAG,CAAC6C,IAAI,IAAIA,IAAI,CAAC7C,kBAAkB,MAAM,IAAI,CAAA;IAEnE,IAAIoE,UAAU,IAAIZ,gBAAgB,CAACY,UAAU,CAAC7H,UAAU,CAAC,EAAE;MACzDwM,mBAAmB,CACjB5f,GAAG,EACHwc,OAAO,EACP1b,IAAI,EACJsG,KAAK,EACLN,OAAO,EACPiP,QAAQ,CAACE,MAAM,EACf8D,SAAS,EACTlD,kBAAkB,EAClBoE,UAAU,CACX,CAAA;AACD,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA9C,IAAAA,gBAAgB,CAACpJ,GAAG,CAAC/O,GAAG,EAAE;MAAEwc,OAAO;AAAE1b,MAAAA,IAAAA;AAAM,KAAA,CAAC,CAAA;IAC5C+e,mBAAmB,CACjB7f,GAAG,EACHwc,OAAO,EACP1b,IAAI,EACJsG,KAAK,EACLN,OAAO,EACPiP,QAAQ,CAACE,MAAM,EACf8D,SAAS,EACTlD,kBAAkB,EAClBoE,UAAU,CACX,CAAA;AACH,GAAA;AAEA;AACA;AACA,EAAA,eAAe2E,mBAAmBA,CAChC5f,GAAW,EACXwc,OAAe,EACf1b,IAAY,EACZsG,KAA6B,EAC7B0Y,cAAwC,EACxC/C,UAAmB,EACnBhD,SAAkB,EAClBlD,kBAA2B,EAC3BoE,UAAsB,EAAA;AAEtBO,IAAAA,oBAAoB,EAAE,CAAA;AACtBrD,IAAAA,gBAAgB,CAAClH,MAAM,CAACjR,GAAG,CAAC,CAAA;IAE5B,SAAS+f,uBAAuBA,CAAC5J,CAAyB,EAAA;AACxD,MAAA,IAAI,CAACA,CAAC,CAAC3Q,KAAK,CAACjG,MAAM,IAAI,CAAC4W,CAAC,CAAC3Q,KAAK,CAAC4Q,IAAI,EAAE;AACpC,QAAA,IAAIvR,KAAK,GAAGgR,sBAAsB,CAAC,GAAG,EAAE;UACtC0H,MAAM,EAAEtC,UAAU,CAAC7H,UAAU;AAC7BjT,UAAAA,QAAQ,EAAEW,IAAI;AACd0b,UAAAA,OAAO,EAAEA,OAAAA;AACV,SAAA,CAAC,CAAA;AACFmD,QAAAA,eAAe,CAAC3f,GAAG,EAAEwc,OAAO,EAAE3X,KAAK,EAAE;AAAEkV,UAAAA,SAAAA;AAAW,SAAA,CAAC,CAAA;AACnD,QAAA,OAAO,IAAI,CAAA;AACZ,OAAA;AACD,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,IAAI,CAACgD,UAAU,IAAIgD,uBAAuB,CAAC3Y,KAAK,CAAC,EAAE;AACjD,MAAA,OAAA;AACD,KAAA;AAED;IACA,IAAI4Y,eAAe,GAAG7gB,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;IAC7CigB,kBAAkB,CAACjgB,GAAG,EAAEkgB,oBAAoB,CAACjF,UAAU,EAAE+E,eAAe,CAAC,EAAE;AACzEjG,MAAAA,SAAAA;AACD,KAAA,CAAC,CAAA;AAEF,IAAA,IAAIoG,eAAe,GAAG,IAAInQ,eAAe,EAAE,CAAA;AAC3C,IAAA,IAAIoQ,YAAY,GAAGlE,uBAAuB,CACxCzN,IAAI,CAAC/N,OAAO,EACZI,IAAI,EACJqf,eAAe,CAAChQ,MAAM,EACtB8K,UAAU,CACX,CAAA;AAED,IAAA,IAAI8B,UAAU,EAAE;AACd,MAAA,IAAIE,cAAc,GAAG,MAAMC,cAAc,CACvC4C,cAAc,EACdhf,IAAI,EACJsf,YAAY,CAACjQ,MAAM,CACpB,CAAA;AAED,MAAA,IAAI8M,cAAc,CAAC9N,IAAI,KAAK,SAAS,EAAE;AACrC,QAAA,OAAA;AACD,OAAA,MAAM,IAAI8N,cAAc,CAAC9N,IAAI,KAAK,OAAO,EAAE;QAC1CwQ,eAAe,CAAC3f,GAAG,EAAEwc,OAAO,EAAES,cAAc,CAACpY,KAAK,EAAE;AAAEkV,UAAAA,SAAAA;AAAS,SAAE,CAAC,CAAA;AAClE,QAAA,OAAA;AACD,OAAA,MAAM,IAAI,CAACkD,cAAc,CAACnW,OAAO,EAAE;QAClC6Y,eAAe,CACb3f,GAAG,EACHwc,OAAO,EACP3G,sBAAsB,CAAC,GAAG,EAAE;AAAE1V,UAAAA,QAAQ,EAAEW,IAAAA;SAAM,CAAC,EAC/C;AAAEiZ,UAAAA,SAAAA;AAAS,SAAE,CACd,CAAA;AACD,QAAA,OAAA;AACD,OAAA,MAAM;QACL+F,cAAc,GAAG7C,cAAc,CAACnW,OAAO,CAAA;AACvCM,QAAAA,KAAK,GAAGkW,cAAc,CAACwC,cAAc,EAAEhf,IAAI,CAAC,CAAA;AAE5C,QAAA,IAAIif,uBAAuB,CAAC3Y,KAAK,CAAC,EAAE;AAClC,UAAA,OAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AAED;AACA0Q,IAAAA,gBAAgB,CAAC/I,GAAG,CAAC/O,GAAG,EAAEmgB,eAAe,CAAC,CAAA;IAE1C,IAAIE,iBAAiB,GAAGtI,kBAAkB,CAAA;AAC1C,IAAA,IAAIuI,aAAa,GAAG,MAAM7C,gBAAgB,CACxC,QAAQ,EACRte,KAAK,EACLihB,YAAY,EACZ,CAAChZ,KAAK,CAAC,EACP0Y,cAAc,EACd9f,GAAG,CACJ,CAAA;IACD,IAAIqc,YAAY,GAAGiE,aAAa,CAAClZ,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;AAEhD,IAAA,IAAIoa,YAAY,CAACjQ,MAAM,CAACa,OAAO,EAAE;AAC/B;AACA;MACA,IAAI8G,gBAAgB,CAAC/G,GAAG,CAAC/Q,GAAG,CAAC,KAAKmgB,eAAe,EAAE;AACjDrI,QAAAA,gBAAgB,CAAC7G,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC7B,OAAA;AACD,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA;IACA,IAAI+U,MAAM,CAACC,iBAAiB,IAAIqD,eAAe,CAACvJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;MACxD,IAAI0d,gBAAgB,CAACrB,YAAY,CAAC,IAAII,aAAa,CAACJ,YAAY,CAAC,EAAE;AACjE4D,QAAAA,kBAAkB,CAACjgB,GAAG,EAAEugB,cAAc,CAACnhB,SAAS,CAAC,CAAC,CAAA;AAClD,QAAA,OAAA;AACD,OAAA;AACD;AACD,KAAA,MAAM;AACL,MAAA,IAAIse,gBAAgB,CAACrB,YAAY,CAAC,EAAE;AAClCvE,QAAAA,gBAAgB,CAAC7G,MAAM,CAACjR,GAAG,CAAC,CAAA;QAC5B,IAAIgY,uBAAuB,GAAGqI,iBAAiB,EAAE;AAC/C;AACA;AACA;AACA;AACAJ,UAAAA,kBAAkB,CAACjgB,GAAG,EAAEugB,cAAc,CAACnhB,SAAS,CAAC,CAAC,CAAA;AAClD,UAAA,OAAA;AACD,SAAA,MAAM;AACL8Y,UAAAA,gBAAgB,CAAC1H,GAAG,CAACxQ,GAAG,CAAC,CAAA;AACzBigB,UAAAA,kBAAkB,CAACjgB,GAAG,EAAEyf,iBAAiB,CAACxE,UAAU,CAAC,CAAC,CAAA;AACtD,UAAA,OAAO2C,uBAAuB,CAACwC,YAAY,EAAE/D,YAAY,EAAE,KAAK,EAAE;AAChEQ,YAAAA,iBAAiB,EAAE5B,UAAU;AAC7BpE,YAAAA,kBAAAA;AACD,WAAA,CAAC,CAAA;AACH,SAAA;AACF,OAAA;AAED;AACA,MAAA,IAAI4F,aAAa,CAACJ,YAAY,CAAC,EAAE;QAC/BsD,eAAe,CAAC3f,GAAG,EAAEwc,OAAO,EAAEH,YAAY,CAACxX,KAAK,CAAC,CAAA;AACjD,QAAA,OAAA;AACD,OAAA;AACF,KAAA;AAED,IAAA,IAAIgZ,gBAAgB,CAACxB,YAAY,CAAC,EAAE;MAClC,MAAMxG,sBAAsB,CAAC,GAAG,EAAE;AAAE1G,QAAAA,IAAI,EAAE,cAAA;AAAgB,OAAA,CAAC,CAAA;AAC5D,KAAA;AAED;AACA;IACA,IAAI/N,YAAY,GAAGjC,KAAK,CAACwX,UAAU,CAAC1W,QAAQ,IAAId,KAAK,CAACc,QAAQ,CAAA;AAC9D,IAAA,IAAIugB,mBAAmB,GAAGtE,uBAAuB,CAC/CzN,IAAI,CAAC/N,OAAO,EACZU,YAAY,EACZ+e,eAAe,CAAChQ,MAAM,CACvB,CAAA;AACD,IAAA,IAAIyL,WAAW,GAAGnH,kBAAkB,IAAID,UAAU,CAAA;IAClD,IAAI1N,OAAO,GACT3H,KAAK,CAACwX,UAAU,CAACxX,KAAK,KAAK,MAAM,GAC7BkH,WAAW,CAACuV,WAAW,EAAEzc,KAAK,CAACwX,UAAU,CAAC1W,QAAQ,EAAEsG,QAAQ,CAAC,GAC7DpH,KAAK,CAAC2H,OAAO,CAAA;AAEnB3D,IAAAA,SAAS,CAAC2D,OAAO,EAAE,8CAA8C,CAAC,CAAA;IAElE,IAAI2Z,MAAM,GAAG,EAAE1I,kBAAkB,CAAA;AACjCE,IAAAA,cAAc,CAAClJ,GAAG,CAAC/O,GAAG,EAAEygB,MAAM,CAAC,CAAA;IAE/B,IAAIC,WAAW,GAAGjB,iBAAiB,CAACxE,UAAU,EAAEoB,YAAY,CAAC9U,IAAI,CAAC,CAAA;IAClEpI,KAAK,CAAC6X,QAAQ,CAACjI,GAAG,CAAC/O,GAAG,EAAE0gB,WAAW,CAAC,CAAA;IAEpC,IAAI,CAACvC,aAAa,EAAEC,oBAAoB,CAAC,GAAGC,gBAAgB,CAC1D5P,IAAI,CAAC/N,OAAO,EACZvB,KAAK,EACL2H,OAAO,EACPmU,UAAU,EACV7Z,YAAY,EACZ,KAAK,EACL2T,MAAM,CAACK,8BAA8B,EACrCuC,sBAAsB,EACtBC,uBAAuB,EACvBC,qBAAqB,EACrBQ,eAAe,EACfF,gBAAgB,EAChBD,gBAAgB,EAChB0D,WAAW,EACXrV,QAAQ,EACR,CAACa,KAAK,CAAC5B,KAAK,CAACQ,EAAE,EAAEqW,YAAY,CAAC,CAC/B,CAAA;AAED;AACA;AACA;AACA+B,IAAAA,oBAAoB,CACjBnU,MAAM,CAAE0U,EAAE,IAAKA,EAAE,CAAC3e,GAAG,KAAKA,GAAG,CAAC,CAC9BoI,OAAO,CAAEuW,EAAE,IAAI;AACd,MAAA,IAAIgC,QAAQ,GAAGhC,EAAE,CAAC3e,GAAG,CAAA;MACrB,IAAIggB,eAAe,GAAG7gB,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC4P,QAAQ,CAAC,CAAA;AAClD,MAAA,IAAInB,mBAAmB,GAAGC,iBAAiB,CACzCrgB,SAAS,EACT4gB,eAAe,GAAGA,eAAe,CAACzY,IAAI,GAAGnI,SAAS,CACnD,CAAA;MACDD,KAAK,CAAC6X,QAAQ,CAACjI,GAAG,CAAC4R,QAAQ,EAAEnB,mBAAmB,CAAC,CAAA;MACjDZ,YAAY,CAAC+B,QAAQ,CAAC,CAAA;MACtB,IAAIhC,EAAE,CAAC5O,UAAU,EAAE;QACjB+H,gBAAgB,CAAC/I,GAAG,CAAC4R,QAAQ,EAAEhC,EAAE,CAAC5O,UAAU,CAAC,CAAA;AAC9C,OAAA;AACH,KAAC,CAAC,CAAA;AAEJgJ,IAAAA,WAAW,CAAC;AAAE/B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAA;AAAC,KAAE,CAAC,CAAA;AAElD,IAAA,IAAI6H,8BAA8B,GAAGA,MACnCT,oBAAoB,CAAChW,OAAO,CAAEuW,EAAE,IAAKC,YAAY,CAACD,EAAE,CAAC3e,GAAG,CAAC,CAAC,CAAA;IAE5DmgB,eAAe,CAAChQ,MAAM,CAACjL,gBAAgB,CACrC,OAAO,EACP2Z,8BAA8B,CAC/B,CAAA;IAED,IAAI;MAAEE,aAAa;AAAEC,MAAAA,cAAAA;AAAgB,KAAA,GACnC,MAAMC,8BAA8B,CAClC9f,KAAK,EACL2H,OAAO,EACPqX,aAAa,EACbC,oBAAoB,EACpBoC,mBAAmB,CACpB,CAAA;AAEH,IAAA,IAAIL,eAAe,CAAChQ,MAAM,CAACa,OAAO,EAAE;AAClC,MAAA,OAAA;AACD,KAAA;IAEDmP,eAAe,CAAChQ,MAAM,CAAChL,mBAAmB,CACxC,OAAO,EACP0Z,8BAA8B,CAC/B,CAAA;AAED5G,IAAAA,cAAc,CAAChH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC1B8X,IAAAA,gBAAgB,CAAC7G,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC5Boe,IAAAA,oBAAoB,CAAChW,OAAO,CAAE0H,CAAC,IAAKgI,gBAAgB,CAAC7G,MAAM,CAACnB,CAAC,CAAC9P,GAAG,CAAC,CAAC,CAAA;AAEnE,IAAA,IAAIsS,QAAQ,GAAG4M,YAAY,CAACH,aAAa,CAAC,CAAA;AAC1C,IAAA,IAAIzM,QAAQ,EAAE;MACZ,OAAOsL,uBAAuB,CAC5B4C,mBAAmB,EACnBlO,QAAQ,CAACrJ,MAAM,EACf,KAAK,EACL;AAAE4N,QAAAA,kBAAAA;AAAkB,OAAE,CACvB,CAAA;AACF,KAAA;AAEDvE,IAAAA,QAAQ,GAAG4M,YAAY,CAACF,cAAc,CAAC,CAAA;AACvC,IAAA,IAAI1M,QAAQ,EAAE;AACZ;AACA;AACA;AACA4F,MAAAA,gBAAgB,CAAC1H,GAAG,CAAC8B,QAAQ,CAACtS,GAAG,CAAC,CAAA;MAClC,OAAO4d,uBAAuB,CAC5B4C,mBAAmB,EACnBlO,QAAQ,CAACrJ,MAAM,EACf,KAAK,EACL;AAAE4N,QAAAA,kBAAAA;AAAkB,OAAE,CACvB,CAAA;AACF,KAAA;AAED;IACA,IAAI;MAAExP,UAAU;AAAEiP,MAAAA,MAAAA;KAAQ,GAAG6I,iBAAiB,CAC5ChgB,KAAK,EACL2H,OAAO,EACPiY,aAAa,EACb3f,SAAS,EACTgf,oBAAoB,EACpBY,cAAc,EACd1G,eAAe,CAChB,CAAA;AAED;AACA;IACA,IAAInZ,KAAK,CAAC6X,QAAQ,CAAClI,GAAG,CAAC9O,GAAG,CAAC,EAAE;AAC3B,MAAA,IAAI4gB,WAAW,GAAGL,cAAc,CAAClE,YAAY,CAAC9U,IAAI,CAAC,CAAA;MACnDpI,KAAK,CAAC6X,QAAQ,CAACjI,GAAG,CAAC/O,GAAG,EAAE4gB,WAAW,CAAC,CAAA;AACrC,KAAA;IAEDtB,oBAAoB,CAACmB,MAAM,CAAC,CAAA;AAE5B;AACA;AACA;IACA,IACEthB,KAAK,CAACwX,UAAU,CAACxX,KAAK,KAAK,SAAS,IACpCshB,MAAM,GAAGzI,uBAAuB,EAChC;AACA7U,MAAAA,SAAS,CAACgU,aAAa,EAAE,yBAAyB,CAAC,CAAA;AACnDG,MAAAA,2BAA2B,IAAIA,2BAA2B,CAAC9F,KAAK,EAAE,CAAA;AAElEwI,MAAAA,kBAAkB,CAAC7a,KAAK,CAACwX,UAAU,CAAC1W,QAAQ,EAAE;QAC5C6G,OAAO;QACPO,UAAU;QACViP,MAAM;AACNU,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAA;AACjC,OAAA,CAAC,CAAA;AACH,KAAA,MAAM;AACL;AACA;AACA;AACA+B,MAAAA,WAAW,CAAC;QACVzC,MAAM;AACNjP,QAAAA,UAAU,EAAEmT,eAAe,CACzBrb,KAAK,CAACkI,UAAU,EAChBA,UAAU,EACVP,OAAO,EACPwP,MAAM,CACP;AACDU,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAA;AACjC,OAAA,CAAC,CAAA;AACFW,MAAAA,sBAAsB,GAAG,KAAK,CAAA;AAC/B,KAAA;AACH,GAAA;AAEA;AACA,EAAA,eAAekI,mBAAmBA,CAChC7f,GAAW,EACXwc,OAAe,EACf1b,IAAY,EACZsG,KAA6B,EAC7BN,OAAiC,EACjCiW,UAAmB,EACnBhD,SAAkB,EAClBlD,kBAA2B,EAC3BoE,UAAuB,EAAA;IAEvB,IAAI+E,eAAe,GAAG7gB,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;AAC7CigB,IAAAA,kBAAkB,CAChBjgB,GAAG,EACHyf,iBAAiB,CACfxE,UAAU,EACV+E,eAAe,GAAGA,eAAe,CAACzY,IAAI,GAAGnI,SAAS,CACnD,EACD;AAAE2a,MAAAA,SAAAA;AAAW,KAAA,CACd,CAAA;AAED,IAAA,IAAIoG,eAAe,GAAG,IAAInQ,eAAe,EAAE,CAAA;AAC3C,IAAA,IAAIoQ,YAAY,GAAGlE,uBAAuB,CACxCzN,IAAI,CAAC/N,OAAO,EACZI,IAAI,EACJqf,eAAe,CAAChQ,MAAM,CACvB,CAAA;AAED,IAAA,IAAI4M,UAAU,EAAE;AACd,MAAA,IAAIE,cAAc,GAAG,MAAMC,cAAc,CACvCpW,OAAO,EACPhG,IAAI,EACJsf,YAAY,CAACjQ,MAAM,CACpB,CAAA;AAED,MAAA,IAAI8M,cAAc,CAAC9N,IAAI,KAAK,SAAS,EAAE;AACrC,QAAA,OAAA;AACD,OAAA,MAAM,IAAI8N,cAAc,CAAC9N,IAAI,KAAK,OAAO,EAAE;QAC1CwQ,eAAe,CAAC3f,GAAG,EAAEwc,OAAO,EAAES,cAAc,CAACpY,KAAK,EAAE;AAAEkV,UAAAA,SAAAA;AAAS,SAAE,CAAC,CAAA;AAClE,QAAA,OAAA;AACD,OAAA,MAAM,IAAI,CAACkD,cAAc,CAACnW,OAAO,EAAE;QAClC6Y,eAAe,CACb3f,GAAG,EACHwc,OAAO,EACP3G,sBAAsB,CAAC,GAAG,EAAE;AAAE1V,UAAAA,QAAQ,EAAEW,IAAAA;SAAM,CAAC,EAC/C;AAAEiZ,UAAAA,SAAAA;AAAS,SAAE,CACd,CAAA;AACD,QAAA,OAAA;AACD,OAAA,MAAM;QACLjT,OAAO,GAAGmW,cAAc,CAACnW,OAAO,CAAA;AAChCM,QAAAA,KAAK,GAAGkW,cAAc,CAACxW,OAAO,EAAEhG,IAAI,CAAC,CAAA;AACtC,OAAA;AACF,KAAA;AAED;AACAgX,IAAAA,gBAAgB,CAAC/I,GAAG,CAAC/O,GAAG,EAAEmgB,eAAe,CAAC,CAAA;IAE1C,IAAIE,iBAAiB,GAAGtI,kBAAkB,CAAA;AAC1C,IAAA,IAAIyF,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRte,KAAK,EACLihB,YAAY,EACZ,CAAChZ,KAAK,CAAC,EACPN,OAAO,EACP9G,GAAG,CACJ,CAAA;IACD,IAAIiJ,MAAM,GAAGuU,OAAO,CAACpW,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;AAEpC;AACA;AACA;AACA;AACA,IAAA,IAAI6X,gBAAgB,CAAC5U,MAAM,CAAC,EAAE;AAC5BA,MAAAA,MAAM,GACJ,CAAC,MAAM4X,mBAAmB,CAAC5X,MAAM,EAAEmX,YAAY,CAACjQ,MAAM,EAAE,IAAI,CAAC,KAC7DlH,MAAM,CAAA;AACT,KAAA;AAED;AACA;IACA,IAAI6O,gBAAgB,CAAC/G,GAAG,CAAC/Q,GAAG,CAAC,KAAKmgB,eAAe,EAAE;AACjDrI,MAAAA,gBAAgB,CAAC7G,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC7B,KAAA;AAED,IAAA,IAAIogB,YAAY,CAACjQ,MAAM,CAACa,OAAO,EAAE;AAC/B,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA,IAAA,IAAIqH,eAAe,CAACvJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;AAC5BigB,MAAAA,kBAAkB,CAACjgB,GAAG,EAAEugB,cAAc,CAACnhB,SAAS,CAAC,CAAC,CAAA;AAClD,MAAA,OAAA;AACD,KAAA;AAED;AACA,IAAA,IAAIse,gBAAgB,CAACzU,MAAM,CAAC,EAAE;MAC5B,IAAI+O,uBAAuB,GAAGqI,iBAAiB,EAAE;AAC/C;AACA;AACAJ,QAAAA,kBAAkB,CAACjgB,GAAG,EAAEugB,cAAc,CAACnhB,SAAS,CAAC,CAAC,CAAA;AAClD,QAAA,OAAA;AACD,OAAA,MAAM;AACL8Y,QAAAA,gBAAgB,CAAC1H,GAAG,CAACxQ,GAAG,CAAC,CAAA;AACzB,QAAA,MAAM4d,uBAAuB,CAACwC,YAAY,EAAEnX,MAAM,EAAE,KAAK,EAAE;AACzD4N,UAAAA,kBAAAA;AACD,SAAA,CAAC,CAAA;AACF,QAAA,OAAA;AACD,OAAA;AACF,KAAA;AAED;AACA,IAAA,IAAI4F,aAAa,CAACxT,MAAM,CAAC,EAAE;MACzB0W,eAAe,CAAC3f,GAAG,EAAEwc,OAAO,EAAEvT,MAAM,CAACpE,KAAK,CAAC,CAAA;AAC3C,MAAA,OAAA;AACD,KAAA;IAED1B,SAAS,CAAC,CAAC0a,gBAAgB,CAAC5U,MAAM,CAAC,EAAE,iCAAiC,CAAC,CAAA;AAEvE;IACAgX,kBAAkB,CAACjgB,GAAG,EAAEugB,cAAc,CAACtX,MAAM,CAAC1B,IAAI,CAAC,CAAC,CAAA;AACtD,GAAA;AAEA;;;;;;;;;;;;;;;;;;AAkBG;EACH,eAAeqW,uBAAuBA,CACpC3B,OAAgB,EAChB3J,QAAwB,EACxBwO,YAAqB,EAAAC,MAAA,EAWf;IAAA,IAVN;MACE9F,UAAU;MACV4B,iBAAiB;MACjBhG,kBAAkB;AAClBtV,MAAAA,OAAAA;4BAME,EAAE,GAAAwf,MAAA,CAAA;IAEN,IAAIzO,QAAQ,CAACE,QAAQ,CAAC5D,OAAO,CAACE,GAAG,CAAC,oBAAoB,CAAC,EAAE;AACvD6I,MAAAA,sBAAsB,GAAG,IAAI,CAAA;AAC9B,KAAA;IAED,IAAI1X,QAAQ,GAAGqS,QAAQ,CAACE,QAAQ,CAAC5D,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAC,CAAA;AACxD5N,IAAAA,SAAS,CAAClD,QAAQ,EAAE,qDAAqD,CAAC,CAAA;AAC1EA,IAAAA,QAAQ,GAAG0d,yBAAyB,CAClC1d,QAAQ,EACR,IAAIW,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,EACpByD,QAAQ,CACT,CAAA;IACD,IAAIya,gBAAgB,GAAG9gB,cAAc,CAACf,KAAK,CAACc,QAAQ,EAAEA,QAAQ,EAAE;AAC9Dqa,MAAAA,WAAW,EAAE,IAAA;AACd,KAAA,CAAC,CAAA;AAEF,IAAA,IAAIlG,SAAS,EAAE;MACb,IAAI6M,gBAAgB,GAAG,KAAK,CAAA;MAE5B,IAAI3O,QAAQ,CAACE,QAAQ,CAAC5D,OAAO,CAACE,GAAG,CAAC,yBAAyB,CAAC,EAAE;AAC5D;AACAmS,QAAAA,gBAAgB,GAAG,IAAI,CAAA;OACxB,MAAM,IAAIpN,kBAAkB,CAACzJ,IAAI,CAACnK,QAAQ,CAAC,EAAE;QAC5C,MAAM6C,GAAG,GAAG2L,IAAI,CAAC/N,OAAO,CAACC,SAAS,CAACV,QAAQ,CAAC,CAAA;QAC5CghB,gBAAgB;AACd;AACAne,QAAAA,GAAG,CAACmC,MAAM,KAAKkP,YAAY,CAAClU,QAAQ,CAACgF,MAAM;AAC3C;QACAyB,aAAa,CAAC5D,GAAG,CAAC3C,QAAQ,EAAEoG,QAAQ,CAAC,IAAI,IAAI,CAAA;AAChD,OAAA;AAED,MAAA,IAAI0a,gBAAgB,EAAE;AACpB,QAAA,IAAI1f,OAAO,EAAE;AACX4S,UAAAA,YAAY,CAAClU,QAAQ,CAACsB,OAAO,CAACtB,QAAQ,CAAC,CAAA;AACxC,SAAA,MAAM;AACLkU,UAAAA,YAAY,CAAClU,QAAQ,CAAC+E,MAAM,CAAC/E,QAAQ,CAAC,CAAA;AACvC,SAAA;AACD,QAAA,OAAA;AACD,OAAA;AACF,KAAA;AAED;AACA;AACAqX,IAAAA,2BAA2B,GAAG,IAAI,CAAA;IAElC,IAAI4J,qBAAqB,GACvB3f,OAAO,KAAK,IAAI,IAAI+Q,QAAQ,CAACE,QAAQ,CAAC5D,OAAO,CAACE,GAAG,CAAC,iBAAiB,CAAC,GAChEsI,MAAa,CAAC5V,OAAO,GACrB4V,MAAa,CAACjW,IAAI,CAAA;AAExB;AACA;IACA,IAAI;MAAEiS,UAAU;MAAEC,UAAU;AAAEC,MAAAA,WAAAA;KAAa,GAAGnU,KAAK,CAACwX,UAAU,CAAA;IAC9D,IACE,CAACsE,UAAU,IACX,CAAC4B,iBAAiB,IAClBzJ,UAAU,IACVC,UAAU,IACVC,WAAW,EACX;AACA2H,MAAAA,UAAU,GAAG+C,2BAA2B,CAAC7e,KAAK,CAACwX,UAAU,CAAC,CAAA;AAC3D,KAAA;AAED;AACA;AACA;AACA,IAAA,IAAIoH,gBAAgB,GAAG9C,UAAU,IAAI4B,iBAAiB,CAAA;AACtD,IAAA,IACE3J,iCAAiC,CAACpE,GAAG,CAACwD,QAAQ,CAACE,QAAQ,CAAC7D,MAAM,CAAC,IAC/DoP,gBAAgB,IAChB1D,gBAAgB,CAAC0D,gBAAgB,CAAC3K,UAAU,CAAC,EAC7C;AACA,MAAA,MAAM4F,eAAe,CAACkI,qBAAqB,EAAEF,gBAAgB,EAAE;QAC7D/F,UAAU,EAAAhX,QAAA,CAAA,EAAA,EACL8Z,gBAAgB,EAAA;AACnB1K,UAAAA,UAAU,EAAEpT,QAAAA;SACb,CAAA;AACD;QACA4W,kBAAkB,EAAEA,kBAAkB,IAAIQ,yBAAyB;AACnEgE,QAAAA,oBAAoB,EAAEyF,YAAY,GAC9BvJ,4BAA4B,GAC5BnY,SAAAA;AACL,OAAA,CAAC,CAAA;AACH,KAAA,MAAM;AACL;AACA;AACA,MAAA,IAAIsc,kBAAkB,GAAGgB,oBAAoB,CAC3CsE,gBAAgB,EAChB/F,UAAU,CACX,CAAA;AACD,MAAA,MAAMjC,eAAe,CAACkI,qBAAqB,EAAEF,gBAAgB,EAAE;QAC7DtF,kBAAkB;AAClB;QACAmB,iBAAiB;AACjB;QACAhG,kBAAkB,EAAEA,kBAAkB,IAAIQ,yBAAyB;AACnEgE,QAAAA,oBAAoB,EAAEyF,YAAY,GAC9BvJ,4BAA4B,GAC5BnY,SAAAA;AACL,OAAA,CAAC,CAAA;AACH,KAAA;AACH,GAAA;AAEA;AACA;AACA,EAAA,eAAeqe,gBAAgBA,CAC7BtO,IAAyB,EACzBhQ,KAAkB,EAClB8c,OAAgB,EAChBkC,aAAuC,EACvCrX,OAAiC,EACjCqa,UAAyB,EAAA;AAEzB,IAAA,IAAI3D,OAA2C,CAAA;IAC/C,IAAI4D,WAAW,GAA+B,EAAE,CAAA;IAChD,IAAI;MACF5D,OAAO,GAAG,MAAM6D,oBAAoB,CAClC3M,gBAAgB,EAChBvF,IAAI,EACJhQ,KAAK,EACL8c,OAAO,EACPkC,aAAa,EACbrX,OAAO,EACPqa,UAAU,EACVtb,QAAQ,EACRF,kBAAkB,CACnB,CAAA;KACF,CAAC,OAAOjC,CAAC,EAAE;AACV;AACA;AACAya,MAAAA,aAAa,CAAC/V,OAAO,CAAE+N,CAAC,IAAI;AAC1BiL,QAAAA,WAAW,CAACjL,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,CAAC,GAAG;UACxBmJ,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,UAAAA,KAAK,EAAEnB,CAAAA;SACR,CAAA;AACH,OAAC,CAAC,CAAA;AACF,MAAA,OAAO0d,WAAW,CAAA;AACnB,KAAA;AAED,IAAA,KAAK,IAAI,CAAC5E,OAAO,EAAEvT,MAAM,CAAC,IAAI4B,MAAM,CAAC/L,OAAO,CAAC0e,OAAO,CAAC,EAAE;AACrD,MAAA,IAAI8D,kCAAkC,CAACrY,MAAM,CAAC,EAAE;AAC9C,QAAA,IAAIuJ,QAAQ,GAAGvJ,MAAM,CAACA,MAAkB,CAAA;QACxCmY,WAAW,CAAC5E,OAAO,CAAC,GAAG;UACrBrN,IAAI,EAAE/J,UAAU,CAACkN,QAAQ;AACzBE,UAAAA,QAAQ,EAAE+O,wCAAwC,CAChD/O,QAAQ,EACRyJ,OAAO,EACPO,OAAO,EACP1V,OAAO,EACPP,QAAQ,EACRwO,MAAM,CAACvH,oBAAoB,CAAA;SAE9B,CAAA;AACF,OAAA,MAAM;QACL4T,WAAW,CAAC5E,OAAO,CAAC,GAAG,MAAMgF,qCAAqC,CAChEvY,MAAM,CACP,CAAA;AACF,OAAA;AACF,KAAA;AAED,IAAA,OAAOmY,WAAW,CAAA;AACpB,GAAA;EAEA,eAAenC,8BAA8BA,CAC3C9f,KAAkB,EAClB2H,OAAiC,EACjCqX,aAAuC,EACvCsD,cAAqC,EACrCxF,OAAgB,EAAA;AAEhB,IAAA,IAAIyF,cAAc,GAAGviB,KAAK,CAAC2H,OAAO,CAAA;AAElC;AACA,IAAA,IAAI6a,oBAAoB,GAAGlE,gBAAgB,CACzC,QAAQ,EACRte,KAAK,EACL8c,OAAO,EACPkC,aAAa,EACbrX,OAAO,EACP,IAAI,CACL,CAAA;AAED,IAAA,IAAI8a,qBAAqB,GAAG/R,OAAO,CAACgS,GAAG,CACrCJ,cAAc,CAAC1iB,GAAG,CAAC,MAAO+f,CAAC,IAAI;MAC7B,IAAIA,CAAC,CAAChY,OAAO,IAAIgY,CAAC,CAAC1X,KAAK,IAAI0X,CAAC,CAAC/O,UAAU,EAAE;AACxC,QAAA,IAAIyN,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRte,KAAK,EACL+c,uBAAuB,CAACzN,IAAI,CAAC/N,OAAO,EAAEoe,CAAC,CAAChe,IAAI,EAAEge,CAAC,CAAC/O,UAAU,CAACI,MAAM,CAAC,EAClE,CAAC2O,CAAC,CAAC1X,KAAK,CAAC,EACT0X,CAAC,CAAChY,OAAO,EACTgY,CAAC,CAAC9e,GAAG,CACN,CAAA;QACD,IAAIiJ,MAAM,GAAGuU,OAAO,CAACsB,CAAC,CAAC1X,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;AACtC;QACA,OAAO;UAAE,CAAC8Y,CAAC,CAAC9e,GAAG,GAAGiJ,MAAAA;SAAQ,CAAA;AAC3B,OAAA,MAAM;QACL,OAAO4G,OAAO,CAAC8B,OAAO,CAAC;UACrB,CAACmN,CAAC,CAAC9e,GAAG,GAAG;YACPmP,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,YAAAA,KAAK,EAAEgR,sBAAsB,CAAC,GAAG,EAAE;cACjC1V,QAAQ,EAAE2e,CAAC,CAAChe,IAAAA;aACb,CAAA;AACa,WAAA;AACjB,SAAA,CAAC,CAAA;AACH,OAAA;AACH,KAAC,CAAC,CACH,CAAA;IAED,IAAIie,aAAa,GAAG,MAAM4C,oBAAoB,CAAA;IAC9C,IAAI3C,cAAc,GAAG,CAAC,MAAM4C,qBAAqB,EAAE1X,MAAM,CACvD,CAACkG,GAAG,EAAEN,CAAC,KAAKjF,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAEN,CAAC,CAAC,EACjC,EAAE,CACH,CAAA;AAED,IAAA,MAAMD,OAAO,CAACgS,GAAG,CAAC,CAChBC,gCAAgC,CAC9Bhb,OAAO,EACPiY,aAAa,EACb9C,OAAO,CAAC9L,MAAM,EACduR,cAAc,EACdviB,KAAK,CAACkI,UAAU,CACjB,EACD0a,6BAA6B,CAACjb,OAAO,EAAEkY,cAAc,EAAEyC,cAAc,CAAC,CACvE,CAAC,CAAA;IAEF,OAAO;MACL1C,aAAa;AACbC,MAAAA,cAAAA;KACD,CAAA;AACH,GAAA;EAEA,SAASxD,oBAAoBA,GAAA;AAC3B;AACA7D,IAAAA,sBAAsB,GAAG,IAAI,CAAA;AAE7B;AACA;AACAC,IAAAA,uBAAuB,CAAC1W,IAAI,CAAC,GAAGod,qBAAqB,EAAE,CAAC,CAAA;AAExD;AACAnG,IAAAA,gBAAgB,CAAC/P,OAAO,CAAC,CAAC+D,CAAC,EAAEnM,GAAG,KAAI;AAClC,MAAA,IAAI8X,gBAAgB,CAAChJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;AAC7B6X,QAAAA,qBAAqB,CAACrH,GAAG,CAACxQ,GAAG,CAAC,CAAA;AAC/B,OAAA;MACD4e,YAAY,CAAC5e,GAAG,CAAC,CAAA;AACnB,KAAC,CAAC,CAAA;AACJ,GAAA;AAEA,EAAA,SAASigB,kBAAkBA,CACzBjgB,GAAW,EACX6Z,OAAgB,EAChBH,MAAkC;AAAA,IAAA,IAAlCA;MAAAA,OAAgC,EAAE,CAAA;AAAA,KAAA;IAElCva,KAAK,CAAC6X,QAAQ,CAACjI,GAAG,CAAC/O,GAAG,EAAE6Z,OAAO,CAAC,CAAA;AAChCd,IAAAA,WAAW,CACT;AAAE/B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAA;AAAG,KAAA,EACrC;AAAE+C,MAAAA,SAAS,EAAE,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAA;AAAM,KAAA,CACjD,CAAA;AACH,GAAA;EAEA,SAAS4F,eAAeA,CACtB3f,GAAW,EACXwc,OAAe,EACf3X,KAAU,EACV6U,IAAA,EAAkC;AAAA,IAAA,IAAlCA,IAAA,KAAA,KAAA,CAAA,EAAA;MAAAA,IAAA,GAAgC,EAAE,CAAA;AAAA,KAAA;IAElC,IAAIoE,aAAa,GAAG1B,mBAAmB,CAACjd,KAAK,CAAC2H,OAAO,EAAE0V,OAAO,CAAC,CAAA;IAC/DjD,aAAa,CAACvZ,GAAG,CAAC,CAAA;AAClB+Y,IAAAA,WAAW,CACT;AACEzC,MAAAA,MAAM,EAAE;AACN,QAAA,CAACwH,aAAa,CAACtY,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;OAC3B;AACDmS,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAA;AACjC,KAAA,EACD;AAAE+C,MAAAA,SAAS,EAAE,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAA;AAAI,KAAE,CACjD,CAAA;AACH,GAAA;EAEA,SAASiI,UAAUA,CAAchiB,GAAW,EAAA;IAC1C,IAAI+U,MAAM,CAACC,iBAAiB,EAAE;AAC5BoD,MAAAA,cAAc,CAACrJ,GAAG,CAAC/O,GAAG,EAAE,CAACoY,cAAc,CAACrH,GAAG,CAAC/Q,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;AAC3D;AACA;AACA,MAAA,IAAIqY,eAAe,CAACvJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;AAC5BqY,QAAAA,eAAe,CAACpH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC5B,OAAA;AACF,KAAA;IACD,OAAOb,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC/Q,GAAG,CAAC,IAAIyT,YAAY,CAAA;AAChD,GAAA;EAEA,SAAS8F,aAAaA,CAACvZ,GAAW,EAAA;IAChC,IAAI6Z,OAAO,GAAG1a,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;AACrC;AACA;AACA;IACA,IACE8X,gBAAgB,CAAChJ,GAAG,CAAC9O,GAAG,CAAC,IACzB,EAAE6Z,OAAO,IAAIA,OAAO,CAAC1a,KAAK,KAAK,SAAS,IAAI8Y,cAAc,CAACnJ,GAAG,CAAC9O,GAAG,CAAC,CAAC,EACpE;MACA4e,YAAY,CAAC5e,GAAG,CAAC,CAAA;AAClB,KAAA;AACDmY,IAAAA,gBAAgB,CAAClH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC5BiY,IAAAA,cAAc,CAAChH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC1BkY,IAAAA,gBAAgB,CAACjH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC5BqY,IAAAA,eAAe,CAACpH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC3B6X,IAAAA,qBAAqB,CAAC5G,MAAM,CAACjR,GAAG,CAAC,CAAA;AACjCb,IAAAA,KAAK,CAAC6X,QAAQ,CAAC/F,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC5B,GAAA;EAEA,SAASiiB,2BAA2BA,CAACjiB,GAAW,EAAA;IAC9C,IAAI+U,MAAM,CAACC,iBAAiB,EAAE;AAC5B,MAAA,IAAIkN,KAAK,GAAG,CAAC9J,cAAc,CAACrH,GAAG,CAAC/Q,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;MAC9C,IAAIkiB,KAAK,IAAI,CAAC,EAAE;AACd9J,QAAAA,cAAc,CAACnH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC1BqY,QAAAA,eAAe,CAAC7H,GAAG,CAACxQ,GAAG,CAAC,CAAA;AACzB,OAAA,MAAM;AACLoY,QAAAA,cAAc,CAACrJ,GAAG,CAAC/O,GAAG,EAAEkiB,KAAK,CAAC,CAAA;AAC/B,OAAA;AACF,KAAA,MAAM;MACL3I,aAAa,CAACvZ,GAAG,CAAC,CAAA;AACnB,KAAA;AACD+Y,IAAAA,WAAW,CAAC;AAAE/B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC9X,KAAK,CAAC6X,QAAQ,CAAA;AAAC,KAAE,CAAC,CAAA;AACpD,GAAA;EAEA,SAAS4H,YAAYA,CAAC5e,GAAW,EAAA;AAC/B,IAAA,IAAI+P,UAAU,GAAG+H,gBAAgB,CAAC/G,GAAG,CAAC/Q,GAAG,CAAC,CAAA;AAC1C,IAAA,IAAI+P,UAAU,EAAE;MACdA,UAAU,CAACyB,KAAK,EAAE,CAAA;AAClBsG,MAAAA,gBAAgB,CAAC7G,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC7B,KAAA;AACH,GAAA;EAEA,SAASmiB,gBAAgBA,CAAC5H,IAAc,EAAA;AACtC,IAAA,KAAK,IAAIva,GAAG,IAAIua,IAAI,EAAE;AACpB,MAAA,IAAIV,OAAO,GAAGmI,UAAU,CAAChiB,GAAG,CAAC,CAAA;AAC7B,MAAA,IAAI4gB,WAAW,GAAGL,cAAc,CAAC1G,OAAO,CAACtS,IAAI,CAAC,CAAA;MAC9CpI,KAAK,CAAC6X,QAAQ,CAACjI,GAAG,CAAC/O,GAAG,EAAE4gB,WAAW,CAAC,CAAA;AACrC,KAAA;AACH,GAAA;EAEA,SAASpC,sBAAsBA,GAAA;IAC7B,IAAI4D,QAAQ,GAAG,EAAE,CAAA;IACjB,IAAI7D,eAAe,GAAG,KAAK,CAAA;AAC3B,IAAA,KAAK,IAAIve,GAAG,IAAIkY,gBAAgB,EAAE;MAChC,IAAI2B,OAAO,GAAG1a,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;AACrCmD,MAAAA,SAAS,CAAC0W,OAAO,EAAuB7Z,oBAAAA,GAAAA,GAAK,CAAC,CAAA;AAC9C,MAAA,IAAI6Z,OAAO,CAAC1a,KAAK,KAAK,SAAS,EAAE;AAC/B+Y,QAAAA,gBAAgB,CAACjH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC5BoiB,QAAAA,QAAQ,CAAClhB,IAAI,CAAClB,GAAG,CAAC,CAAA;AAClBue,QAAAA,eAAe,GAAG,IAAI,CAAA;AACvB,OAAA;AACF,KAAA;IACD4D,gBAAgB,CAACC,QAAQ,CAAC,CAAA;AAC1B,IAAA,OAAO7D,eAAe,CAAA;AACxB,GAAA;EAEA,SAASe,oBAAoBA,CAAC+C,QAAgB,EAAA;IAC5C,IAAIC,UAAU,GAAG,EAAE,CAAA;IACnB,KAAK,IAAI,CAACtiB,GAAG,EAAEgG,EAAE,CAAC,IAAIiS,cAAc,EAAE;MACpC,IAAIjS,EAAE,GAAGqc,QAAQ,EAAE;QACjB,IAAIxI,OAAO,GAAG1a,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;AACrCmD,QAAAA,SAAS,CAAC0W,OAAO,EAAuB7Z,oBAAAA,GAAAA,GAAK,CAAC,CAAA;AAC9C,QAAA,IAAI6Z,OAAO,CAAC1a,KAAK,KAAK,SAAS,EAAE;UAC/Byf,YAAY,CAAC5e,GAAG,CAAC,CAAA;AACjBiY,UAAAA,cAAc,CAAChH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC1BsiB,UAAAA,UAAU,CAACphB,IAAI,CAAClB,GAAG,CAAC,CAAA;AACrB,SAAA;AACF,OAAA;AACF,KAAA;IACDmiB,gBAAgB,CAACG,UAAU,CAAC,CAAA;AAC5B,IAAA,OAAOA,UAAU,CAAChjB,MAAM,GAAG,CAAC,CAAA;AAC9B,GAAA;AAEA,EAAA,SAASijB,UAAUA,CAACviB,GAAW,EAAE4B,EAAmB,EAAA;IAClD,IAAI4gB,OAAO,GAAYrjB,KAAK,CAAC+X,QAAQ,CAACnG,GAAG,CAAC/Q,GAAG,CAAC,IAAI0T,YAAY,CAAA;IAE9D,IAAI6E,gBAAgB,CAACxH,GAAG,CAAC/Q,GAAG,CAAC,KAAK4B,EAAE,EAAE;AACpC2W,MAAAA,gBAAgB,CAACxJ,GAAG,CAAC/O,GAAG,EAAE4B,EAAE,CAAC,CAAA;AAC9B,KAAA;AAED,IAAA,OAAO4gB,OAAO,CAAA;AAChB,GAAA;EAEA,SAAShJ,aAAaA,CAACxZ,GAAW,EAAA;AAChCb,IAAAA,KAAK,CAAC+X,QAAQ,CAACjG,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC1BuY,IAAAA,gBAAgB,CAACtH,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC9B,GAAA;AAEA;AACA,EAAA,SAAS8Y,aAAaA,CAAC9Y,GAAW,EAAEyiB,UAAmB,EAAA;IACrD,IAAID,OAAO,GAAGrjB,KAAK,CAAC+X,QAAQ,CAACnG,GAAG,CAAC/Q,GAAG,CAAC,IAAI0T,YAAY,CAAA;AAErD;AACA;AACAvQ,IAAAA,SAAS,CACNqf,OAAO,CAACrjB,KAAK,KAAK,WAAW,IAAIsjB,UAAU,CAACtjB,KAAK,KAAK,SAAS,IAC7DqjB,OAAO,CAACrjB,KAAK,KAAK,SAAS,IAAIsjB,UAAU,CAACtjB,KAAK,KAAK,SAAU,IAC9DqjB,OAAO,CAACrjB,KAAK,KAAK,SAAS,IAAIsjB,UAAU,CAACtjB,KAAK,KAAK,YAAa,IACjEqjB,OAAO,CAACrjB,KAAK,KAAK,SAAS,IAAIsjB,UAAU,CAACtjB,KAAK,KAAK,WAAY,IAChEqjB,OAAO,CAACrjB,KAAK,KAAK,YAAY,IAAIsjB,UAAU,CAACtjB,KAAK,KAAK,WAAY,EAAA,oCAAA,GACjCqjB,OAAO,CAACrjB,KAAK,GAAA,MAAA,GAAOsjB,UAAU,CAACtjB,KAAO,CAC5E,CAAA;IAED,IAAI+X,QAAQ,GAAG,IAAID,GAAG,CAAC9X,KAAK,CAAC+X,QAAQ,CAAC,CAAA;AACtCA,IAAAA,QAAQ,CAACnI,GAAG,CAAC/O,GAAG,EAAEyiB,UAAU,CAAC,CAAA;AAC7B1J,IAAAA,WAAW,CAAC;AAAE7B,MAAAA,QAAAA;AAAQ,KAAE,CAAC,CAAA;AAC3B,GAAA;EAEA,SAASyB,qBAAqBA,CAAAtI,KAAA,EAQ7B;IAAA,IAR8B;MAC7BuI,eAAe;MACfxX,YAAY;AACZsV,MAAAA,aAAAA;AAKD,KAAA,GAAArG,KAAA,CAAA;AACC,IAAA,IAAIkI,gBAAgB,CAAC3G,IAAI,KAAK,CAAC,EAAE;AAC/B,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA,IAAA,IAAI2G,gBAAgB,CAAC3G,IAAI,GAAG,CAAC,EAAE;AAC7BxR,MAAAA,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAAC,CAAA;AAC/D,KAAA;IAED,IAAItB,OAAO,GAAG2Q,KAAK,CAACzB,IAAI,CAACuK,gBAAgB,CAACzZ,OAAO,EAAE,CAAC,CAAA;AACpD,IAAA,IAAI,CAAC4Z,UAAU,EAAEgK,eAAe,CAAC,GAAG5jB,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,CAAA;IAC/D,IAAIkjB,OAAO,GAAGrjB,KAAK,CAAC+X,QAAQ,CAACnG,GAAG,CAAC2H,UAAU,CAAC,CAAA;AAE5C,IAAA,IAAI8J,OAAO,IAAIA,OAAO,CAACrjB,KAAK,KAAK,YAAY,EAAE;AAC7C;AACA;AACA,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA,IAAA,IAAIujB,eAAe,CAAC;MAAE9J,eAAe;MAAExX,YAAY;AAAEsV,MAAAA,aAAAA;AAAe,KAAA,CAAC,EAAE;AACrE,MAAA,OAAOgC,UAAU,CAAA;AAClB,KAAA;AACH,GAAA;EAEA,SAASqD,qBAAqBA,CAAC5b,QAAgB,EAAA;AAC7C,IAAA,IAAI0E,KAAK,GAAGgR,sBAAsB,CAAC,GAAG,EAAE;AAAE1V,MAAAA,QAAAA;AAAU,KAAA,CAAC,CAAA;AACrD,IAAA,IAAIyb,WAAW,GAAGnH,kBAAkB,IAAID,UAAU,CAAA;IAClD,IAAI;MAAE1N,OAAO;AAAEtB,MAAAA,KAAAA;AAAK,KAAE,GAAGsQ,sBAAsB,CAAC8F,WAAW,CAAC,CAAA;AAE5D;AACA0C,IAAAA,qBAAqB,EAAE,CAAA;IAEvB,OAAO;AAAExC,MAAAA,eAAe,EAAEhV,OAAO;MAAEtB,KAAK;AAAEX,MAAAA,KAAAA;KAAO,CAAA;AACnD,GAAA;EAEA,SAASyZ,qBAAqBA,CAC5BqE,SAAwC,EAAA;IAExC,IAAIC,iBAAiB,GAAa,EAAE,CAAA;AACpCtK,IAAAA,eAAe,CAAClQ,OAAO,CAAC,CAACya,GAAG,EAAErG,OAAO,KAAI;AACvC,MAAA,IAAI,CAACmG,SAAS,IAAIA,SAAS,CAACnG,OAAO,CAAC,EAAE;AACpC;AACA;AACA;QACAqG,GAAG,CAACtR,MAAM,EAAE,CAAA;AACZqR,QAAAA,iBAAiB,CAAC1hB,IAAI,CAACsb,OAAO,CAAC,CAAA;AAC/BlE,QAAAA,eAAe,CAACrH,MAAM,CAACuL,OAAO,CAAC,CAAA;AAChC,OAAA;AACH,KAAC,CAAC,CAAA;AACF,IAAA,OAAOoG,iBAAiB,CAAA;AAC1B,GAAA;AAEA;AACA;AACA,EAAA,SAASE,uBAAuBA,CAC9BC,SAAiC,EACjCC,WAAsC,EACtCC,MAAwC,EAAA;AAExC3N,IAAAA,oBAAoB,GAAGyN,SAAS,CAAA;AAChCvN,IAAAA,iBAAiB,GAAGwN,WAAW,CAAA;IAC/BzN,uBAAuB,GAAG0N,MAAM,IAAI,IAAI,CAAA;AAExC;AACA;AACA;IACA,IAAI,CAACxN,qBAAqB,IAAItW,KAAK,CAACwX,UAAU,KAAKxD,eAAe,EAAE;AAClEsC,MAAAA,qBAAqB,GAAG,IAAI,CAAA;MAC5B,IAAIyN,CAAC,GAAGvI,sBAAsB,CAACxb,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAAC2H,OAAO,CAAC,CAAA;MAC7D,IAAIoc,CAAC,IAAI,IAAI,EAAE;AACbnK,QAAAA,WAAW,CAAC;AAAEnC,UAAAA,qBAAqB,EAAEsM,CAAAA;AAAC,SAAE,CAAC,CAAA;AAC1C,OAAA;AACF,KAAA;AAED,IAAA,OAAO,MAAK;AACV5N,MAAAA,oBAAoB,GAAG,IAAI,CAAA;AAC3BE,MAAAA,iBAAiB,GAAG,IAAI,CAAA;AACxBD,MAAAA,uBAAuB,GAAG,IAAI,CAAA;KAC/B,CAAA;AACH,GAAA;AAEA,EAAA,SAAS4N,YAAYA,CAACljB,QAAkB,EAAE6G,OAAiC,EAAA;AACzE,IAAA,IAAIyO,uBAAuB,EAAE;MAC3B,IAAIvV,GAAG,GAAGuV,uBAAuB,CAC/BtV,QAAQ,EACR6G,OAAO,CAAC/H,GAAG,CAAEoX,CAAC,IAAKhP,0BAA0B,CAACgP,CAAC,EAAEhX,KAAK,CAACkI,UAAU,CAAC,CAAC,CACpE,CAAA;AACD,MAAA,OAAOrH,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAA;AAC3B,KAAA;IACD,OAAOC,QAAQ,CAACD,GAAG,CAAA;AACrB,GAAA;AAEA,EAAA,SAAS2b,kBAAkBA,CACzB1b,QAAkB,EAClB6G,OAAiC,EAAA;IAEjC,IAAIwO,oBAAoB,IAAIE,iBAAiB,EAAE;AAC7C,MAAA,IAAIxV,GAAG,GAAGmjB,YAAY,CAACljB,QAAQ,EAAE6G,OAAO,CAAC,CAAA;AACzCwO,MAAAA,oBAAoB,CAACtV,GAAG,CAAC,GAAGwV,iBAAiB,EAAE,CAAA;AAChD,KAAA;AACH,GAAA;AAEA,EAAA,SAASmF,sBAAsBA,CAC7B1a,QAAkB,EAClB6G,OAAiC,EAAA;AAEjC,IAAA,IAAIwO,oBAAoB,EAAE;AACxB,MAAA,IAAItV,GAAG,GAAGmjB,YAAY,CAACljB,QAAQ,EAAE6G,OAAO,CAAC,CAAA;AACzC,MAAA,IAAIoc,CAAC,GAAG5N,oBAAoB,CAACtV,GAAG,CAAC,CAAA;AACjC,MAAA,IAAI,OAAOkjB,CAAC,KAAK,QAAQ,EAAE;AACzB,QAAA,OAAOA,CAAC,CAAA;AACT,OAAA;AACF,KAAA;AACD,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA,EAAA,SAASlN,aAAaA,CACpBlP,OAAwC,EACxC8U,WAAsC,EACtCzb,QAAgB,EAAA;AAEhB,IAAA,IAAI0U,2BAA2B,EAAE;MAC/B,IAAI,CAAC/N,OAAO,EAAE;QACZ,IAAIsc,UAAU,GAAG5c,eAAe,CAC9BoV,WAAW,EACXzb,QAAQ,EACRoG,QAAQ,EACR,IAAI,CACL,CAAA;QAED,OAAO;AAAE0P,UAAAA,MAAM,EAAE,IAAI;UAAEnP,OAAO,EAAEsc,UAAU,IAAI,EAAA;SAAI,CAAA;AACnD,OAAA,MAAM;AACL,QAAA,IAAIvY,MAAM,CAAC0P,IAAI,CAACzT,OAAO,CAAC,CAAC,CAAC,CAACQ,MAAM,CAAC,CAAChI,MAAM,GAAG,CAAC,EAAE;AAC7C;AACA;AACA;UACA,IAAI8d,cAAc,GAAG5W,eAAe,CAClCoV,WAAW,EACXzb,QAAQ,EACRoG,QAAQ,EACR,IAAI,CACL,CAAA;UACD,OAAO;AAAE0P,YAAAA,MAAM,EAAE,IAAI;AAAEnP,YAAAA,OAAO,EAAEsW,cAAAA;WAAgB,CAAA;AACjD,SAAA;AACF,OAAA;AACF,KAAA;IAED,OAAO;AAAEnH,MAAAA,MAAM,EAAE,KAAK;AAAEnP,MAAAA,OAAO,EAAE,IAAA;KAAM,CAAA;AACzC,GAAA;AAiBA,EAAA,eAAeoW,cAAcA,CAC3BpW,OAAiC,EACjC3G,QAAgB,EAChBgQ,MAAmB,EAAA;IAEnB,IAAI,CAAC0E,2BAA2B,EAAE;MAChC,OAAO;AAAE1F,QAAAA,IAAI,EAAE,SAAS;AAAErI,QAAAA,OAAAA;OAAS,CAAA;AACpC,KAAA;IAED,IAAIsW,cAAc,GAAoCtW,OAAO,CAAA;AAC7D,IAAA,OAAO,IAAI,EAAE;AACX,MAAA,IAAIuc,QAAQ,GAAG5O,kBAAkB,IAAI,IAAI,CAAA;AACzC,MAAA,IAAImH,WAAW,GAAGnH,kBAAkB,IAAID,UAAU,CAAA;MAClD,IAAI8O,aAAa,GAAGzd,QAAQ,CAAA;MAC5B,IAAI;AACF,QAAA,MAAMgP,2BAA2B,CAAC;AAChC/T,UAAAA,IAAI,EAAEX,QAAQ;AACd2G,UAAAA,OAAO,EAAEsW,cAAc;AACvBmG,UAAAA,KAAK,EAAEA,CAAC/G,OAAO,EAAEtW,QAAQ,KAAI;YAC3B,IAAIiK,MAAM,CAACa,OAAO,EAAE,OAAA;YACpBwS,eAAe,CACbhH,OAAO,EACPtW,QAAQ,EACR0V,WAAW,EACX0H,aAAa,EACb3d,kBAAkB,CACnB,CAAA;AACH,WAAA;AACD,SAAA,CAAC,CAAA;OACH,CAAC,OAAOjC,CAAC,EAAE;QACV,OAAO;AAAEyL,UAAAA,IAAI,EAAE,OAAO;AAAEtK,UAAAA,KAAK,EAAEnB,CAAC;AAAE0Z,UAAAA,cAAAA;SAAgB,CAAA;AACnD,OAAA,SAAS;AACR;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,IAAIiG,QAAQ,IAAI,CAAClT,MAAM,CAACa,OAAO,EAAE;AAC/BwD,UAAAA,UAAU,GAAG,CAAC,GAAGA,UAAU,CAAC,CAAA;AAC7B,SAAA;AACF,OAAA;MAED,IAAIrE,MAAM,CAACa,OAAO,EAAE;QAClB,OAAO;AAAE7B,UAAAA,IAAI,EAAE,SAAA;SAAW,CAAA;AAC3B,OAAA;MAED,IAAIsU,UAAU,GAAGpd,WAAW,CAACuV,WAAW,EAAEzb,QAAQ,EAAEoG,QAAQ,CAAC,CAAA;AAC7D,MAAA,IAAIkd,UAAU,EAAE;QACd,OAAO;AAAEtU,UAAAA,IAAI,EAAE,SAAS;AAAErI,UAAAA,OAAO,EAAE2c,UAAAA;SAAY,CAAA;AAChD,OAAA;MAED,IAAIC,iBAAiB,GAAGld,eAAe,CACrCoV,WAAW,EACXzb,QAAQ,EACRoG,QAAQ,EACR,IAAI,CACL,CAAA;AAED;AACA,MAAA,IACE,CAACmd,iBAAiB,IACjBtG,cAAc,CAAC9d,MAAM,KAAKokB,iBAAiB,CAACpkB,MAAM,IACjD8d,cAAc,CAAC9S,KAAK,CAClB,CAAC6L,CAAC,EAAEpP,CAAC,KAAKoP,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAK0d,iBAAkB,CAAC3c,CAAC,CAAC,CAACvB,KAAK,CAACQ,EAAE,CACvD,EACJ;QACA,OAAO;AAAEmJ,UAAAA,IAAI,EAAE,SAAS;AAAErI,UAAAA,OAAO,EAAE,IAAA;SAAM,CAAA;AAC1C,OAAA;AAEDsW,MAAAA,cAAc,GAAGsG,iBAAiB,CAAA;AACnC,KAAA;AACH,GAAA;EAEA,SAASC,kBAAkBA,CAACC,SAAoC,EAAA;IAC9D/d,QAAQ,GAAG,EAAE,CAAA;IACb4O,kBAAkB,GAAGhP,yBAAyB,CAC5Cme,SAAS,EACTje,kBAAkB,EAClBvG,SAAS,EACTyG,QAAQ,CACT,CAAA;AACH,GAAA;AAEA,EAAA,SAASge,WAAWA,CAClBrH,OAAsB,EACtBtW,QAA+B,EAAA;AAE/B,IAAA,IAAImd,QAAQ,GAAG5O,kBAAkB,IAAI,IAAI,CAAA;AACzC,IAAA,IAAImH,WAAW,GAAGnH,kBAAkB,IAAID,UAAU,CAAA;IAClDgP,eAAe,CACbhH,OAAO,EACPtW,QAAQ,EACR0V,WAAW,EACX/V,QAAQ,EACRF,kBAAkB,CACnB,CAAA;AAED;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI0d,QAAQ,EAAE;AACZ7O,MAAAA,UAAU,GAAG,CAAC,GAAGA,UAAU,CAAC,CAAA;MAC5BuE,WAAW,CAAC,EAAE,CAAC,CAAA;AAChB,KAAA;AACH,GAAA;AAEAtC,EAAAA,MAAM,GAAG;IACP,IAAIlQ,QAAQA,GAAA;AACV,MAAA,OAAOA,QAAQ,CAAA;KAChB;IACD,IAAIwO,MAAMA,GAAA;AACR,MAAA,OAAOA,MAAM,CAAA;KACd;IACD,IAAI5V,KAAKA,GAAA;AACP,MAAA,OAAOA,KAAK,CAAA;KACb;IACD,IAAIuG,MAAMA,GAAA;AACR,MAAA,OAAO8O,UAAU,CAAA;KAClB;IACD,IAAIzS,MAAMA,GAAA;AACR,MAAA,OAAOoS,YAAY,CAAA;KACpB;IACDsE,UAAU;IACVnH,SAAS;IACTwR,uBAAuB;IACvBlI,QAAQ;IACR8E,KAAK;IACLnE,UAAU;AACV;AACA;IACA/a,UAAU,EAAGT,EAAM,IAAK0O,IAAI,CAAC/N,OAAO,CAACF,UAAU,CAACT,EAAE,CAAC;IACnDc,cAAc,EAAGd,EAAM,IAAK0O,IAAI,CAAC/N,OAAO,CAACG,cAAc,CAACd,EAAE,CAAC;IAC3DiiB,UAAU;AACVzI,IAAAA,aAAa,EAAE0I,2BAA2B;IAC1C5I,OAAO;IACPkJ,UAAU;IACV/I,aAAa;IACbqK,WAAW;AACXC,IAAAA,yBAAyB,EAAEhM,gBAAgB;AAC3CiM,IAAAA,wBAAwB,EAAEzL,eAAe;AACzC;AACA;AACAqL,IAAAA,kBAAAA;GACD,CAAA;AAED,EAAA,OAAOlN,MAAM,CAAA;AACf,CAAA;AACA;AAEA;AACA;AACA;MAEauN,sBAAsB,GAAGC,MAAM,CAAC,UAAU,EAAC;AAoBxC,SAAAC,mBAAmBA,CACjCxe,MAA6B,EAC7BgU,IAAiC,EAAA;EAEjCvW,SAAS,CACPuC,MAAM,CAACpG,MAAM,GAAG,CAAC,EACjB,kEAAkE,CACnE,CAAA;EAED,IAAIuG,QAAQ,GAAkB,EAAE,CAAA;EAChC,IAAIU,QAAQ,GAAG,CAACmT,IAAI,GAAGA,IAAI,CAACnT,QAAQ,GAAG,IAAI,KAAK,GAAG,CAAA;AACnD,EAAA,IAAIZ,kBAA8C,CAAA;AAClD,EAAA,IAAI+T,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAE/T,kBAAkB,EAAE;IAC5BA,kBAAkB,GAAG+T,IAAI,CAAC/T,kBAAkB,CAAA;AAC7C,GAAA,MAAM,IAAI+T,IAAI,YAAJA,IAAI,CAAEnF,mBAAmB,EAAE;AACpC;AACA,IAAA,IAAIA,mBAAmB,GAAGmF,IAAI,CAACnF,mBAAmB,CAAA;IAClD5O,kBAAkB,GAAIH,KAAK,KAAM;MAC/BuO,gBAAgB,EAAEQ,mBAAmB,CAAC/O,KAAK,CAAA;AAC5C,KAAA,CAAC,CAAA;AACH,GAAA,MAAM;AACLG,IAAAA,kBAAkB,GAAGmO,yBAAyB,CAAA;AAC/C,GAAA;AACD;EACA,IAAIiB,MAAM,GAAA9Q,QAAA,CAAA;AACRuJ,IAAAA,oBAAoB,EAAE,KAAK;AAC3B2W,IAAAA,mBAAmB,EAAE,KAAA;AAAK,GAAA,EACtBzK,IAAI,GAAGA,IAAI,CAAC3E,MAAM,GAAG,IAAI,CAC9B,CAAA;EAED,IAAIP,UAAU,GAAG/O,yBAAyB,CACxCC,MAAM,EACNC,kBAAkB,EAClBvG,SAAS,EACTyG,QAAQ,CACT,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACH,EAAA,eAAeue,KAAKA,CAClBnI,OAAgB,EAAAoI,MAAA,EASV;IAAA,IARN;MACEC,cAAc;MACdC,uBAAuB;AACvB5P,MAAAA,YAAAA;AAAY,KAAA,GAAA0P,MAAA,KAAA,KAAA,CAAA,GAKV,EAAE,GAAAA,MAAA,CAAA;IAEN,IAAIvhB,GAAG,GAAG,IAAIlC,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,CAAA;AAC9B,IAAA,IAAIya,MAAM,GAAGtB,OAAO,CAACsB,MAAM,CAAA;AAC3B,IAAA,IAAItd,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEO,UAAU,CAACqC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACnE,IAAIgE,OAAO,GAAGT,WAAW,CAACmO,UAAU,EAAEvU,QAAQ,EAAEsG,QAAQ,CAAC,CAAA;AAEzD;IACA,IAAI,CAACie,aAAa,CAACjH,MAAM,CAAC,IAAIA,MAAM,KAAK,MAAM,EAAE;AAC/C,MAAA,IAAI1Y,KAAK,GAAGgR,sBAAsB,CAAC,GAAG,EAAE;AAAE0H,QAAAA,MAAAA;AAAQ,OAAA,CAAC,CAAA;MACnD,IAAI;AAAEzW,QAAAA,OAAO,EAAE2d,uBAAuB;AAAEjf,QAAAA,KAAAA;AAAO,OAAA,GAC7CsQ,sBAAsB,CAACtB,UAAU,CAAC,CAAA;MACpC,OAAO;QACLjO,QAAQ;QACRtG,QAAQ;AACR6G,QAAAA,OAAO,EAAE2d,uBAAuB;QAChCpd,UAAU,EAAE,EAAE;AACd0P,QAAAA,UAAU,EAAE,IAAI;AAChBT,QAAAA,MAAM,EAAE;UACN,CAAC9Q,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;SACb;QACD6f,UAAU,EAAE7f,KAAK,CAAC8J,MAAM;QACxBgW,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;AACjBtM,QAAAA,eAAe,EAAE,IAAA;OAClB,CAAA;AACF,KAAA,MAAM,IAAI,CAACxR,OAAO,EAAE;AACnB,MAAA,IAAIjC,KAAK,GAAGgR,sBAAsB,CAAC,GAAG,EAAE;QAAE1V,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;AAAQ,OAAE,CAAC,CAAA;MACxE,IAAI;AAAE2G,QAAAA,OAAO,EAAEgV,eAAe;AAAEtW,QAAAA,KAAAA;AAAO,OAAA,GACrCsQ,sBAAsB,CAACtB,UAAU,CAAC,CAAA;MACpC,OAAO;QACLjO,QAAQ;QACRtG,QAAQ;AACR6G,QAAAA,OAAO,EAAEgV,eAAe;QACxBzU,UAAU,EAAE,EAAE;AACd0P,QAAAA,UAAU,EAAE,IAAI;AAChBT,QAAAA,MAAM,EAAE;UACN,CAAC9Q,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;SACb;QACD6f,UAAU,EAAE7f,KAAK,CAAC8J,MAAM;QACxBgW,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;AACjBtM,QAAAA,eAAe,EAAE,IAAA;OAClB,CAAA;AACF,KAAA;IAED,IAAIrP,MAAM,GAAG,MAAM4b,SAAS,CAC1B5I,OAAO,EACPhc,QAAQ,EACR6G,OAAO,EACPwd,cAAc,EACd3P,YAAY,IAAI,IAAI,EACpB4P,uBAAuB,KAAK,IAAI,EAChC,IAAI,CACL,CAAA;AACD,IAAA,IAAIO,UAAU,CAAC7b,MAAM,CAAC,EAAE;AACtB,MAAA,OAAOA,MAAM,CAAA;AACd,KAAA;AAED;AACA;AACA;AACA,IAAA,OAAAhF,QAAA,CAAA;MAAShE,QAAQ;AAAEsG,MAAAA,QAAAA;AAAQ,KAAA,EAAK0C,MAAM,CAAA,CAAA;AACxC,GAAA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACH,EAAA,eAAe8b,UAAUA,CACvB9I,OAAgB,EAAA+I,MAAA,EASV;IAAA,IARN;MACExI,OAAO;MACP8H,cAAc;AACd3P,MAAAA,YAAAA;AAAY,KAAA,GAAAqQ,MAAA,KAAA,KAAA,CAAA,GAKV,EAAE,GAAAA,MAAA,CAAA;IAEN,IAAIliB,GAAG,GAAG,IAAIlC,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,CAAA;AAC9B,IAAA,IAAIya,MAAM,GAAGtB,OAAO,CAACsB,MAAM,CAAA;AAC3B,IAAA,IAAItd,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEO,UAAU,CAACqC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACnE,IAAIgE,OAAO,GAAGT,WAAW,CAACmO,UAAU,EAAEvU,QAAQ,EAAEsG,QAAQ,CAAC,CAAA;AAEzD;AACA,IAAA,IAAI,CAACie,aAAa,CAACjH,MAAM,CAAC,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,SAAS,EAAE;MACvE,MAAM1H,sBAAsB,CAAC,GAAG,EAAE;AAAE0H,QAAAA,MAAAA;AAAM,OAAE,CAAC,CAAA;AAC9C,KAAA,MAAM,IAAI,CAACzW,OAAO,EAAE;MACnB,MAAM+O,sBAAsB,CAAC,GAAG,EAAE;QAAE1V,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;AAAU,OAAA,CAAC,CAAA;AACnE,KAAA;IAED,IAAIiH,KAAK,GAAGoV,OAAO,GACf1V,OAAO,CAACme,IAAI,CAAE9O,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAKwW,OAAO,CAAC,GAC3Cc,cAAc,CAACxW,OAAO,EAAE7G,QAAQ,CAAC,CAAA;AAErC,IAAA,IAAIuc,OAAO,IAAI,CAACpV,KAAK,EAAE;MACrB,MAAMyO,sBAAsB,CAAC,GAAG,EAAE;QAChC1V,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;AAC3Bqc,QAAAA,OAAAA;AACD,OAAA,CAAC,CAAA;AACH,KAAA,MAAM,IAAI,CAACpV,KAAK,EAAE;AACjB;MACA,MAAMyO,sBAAsB,CAAC,GAAG,EAAE;QAAE1V,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;AAAU,OAAA,CAAC,CAAA;AACnE,KAAA;IAED,IAAI8I,MAAM,GAAG,MAAM4b,SAAS,CAC1B5I,OAAO,EACPhc,QAAQ,EACR6G,OAAO,EACPwd,cAAc,EACd3P,YAAY,IAAI,IAAI,EACpB,KAAK,EACLvN,KAAK,CACN,CAAA;AAED,IAAA,IAAI0d,UAAU,CAAC7b,MAAM,CAAC,EAAE;AACtB,MAAA,OAAOA,MAAM,CAAA;AACd,KAAA;AAED,IAAA,IAAIpE,KAAK,GAAGoE,MAAM,CAACqN,MAAM,GAAGzL,MAAM,CAACqa,MAAM,CAACjc,MAAM,CAACqN,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGlX,SAAS,CAAA;IACvE,IAAIyF,KAAK,KAAKzF,SAAS,EAAE;AACvB;AACA;AACA;AACA;AACA,MAAA,MAAMyF,KAAK,CAAA;AACZ,KAAA;AAED;IACA,IAAIoE,MAAM,CAAC8N,UAAU,EAAE;MACrB,OAAOlM,MAAM,CAACqa,MAAM,CAACjc,MAAM,CAAC8N,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;AAC3C,KAAA;IAED,IAAI9N,MAAM,CAAC5B,UAAU,EAAE;AAAA,MAAA,IAAA8d,qBAAA,CAAA;AACrB,MAAA,IAAI5d,IAAI,GAAGsD,MAAM,CAACqa,MAAM,CAACjc,MAAM,CAAC5B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9C,MAAA,IAAA,CAAA8d,qBAAA,GAAIlc,MAAM,CAACqP,eAAe,KAAtB6M,IAAAA,IAAAA,qBAAA,CAAyB/d,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,EAAE;AAC5CuB,QAAAA,IAAI,CAACyc,sBAAsB,CAAC,GAAG/a,MAAM,CAACqP,eAAe,CAAClR,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;AACtE,OAAA;AACD,MAAA,OAAOuB,IAAI,CAAA;AACZ,KAAA;AAED,IAAA,OAAOnI,SAAS,CAAA;AAClB,GAAA;AAEA,EAAA,eAAeylB,SAASA,CACtB5I,OAAgB,EAChBhc,QAAkB,EAClB6G,OAAiC,EACjCwd,cAAuB,EACvB3P,YAAyC,EACzC4P,uBAAgC,EAChCa,UAAyC,EAAA;AAEzCjiB,IAAAA,SAAS,CACP8Y,OAAO,CAAC9L,MAAM,EACd,sEAAsE,CACvE,CAAA;IAED,IAAI;MACF,IAAIkK,gBAAgB,CAAC4B,OAAO,CAACsB,MAAM,CAAChR,WAAW,EAAE,CAAC,EAAE;QAClD,IAAItD,MAAM,GAAG,MAAMoc,MAAM,CACvBpJ,OAAO,EACPnV,OAAO,EACPse,UAAU,IAAI9H,cAAc,CAACxW,OAAO,EAAE7G,QAAQ,CAAC,EAC/CqkB,cAAc,EACd3P,YAAY,EACZ4P,uBAAuB,EACvBa,UAAU,IAAI,IAAI,CACnB,CAAA;AACD,QAAA,OAAOnc,MAAM,CAAA;AACd,OAAA;AAED,MAAA,IAAIA,MAAM,GAAG,MAAMqc,aAAa,CAC9BrJ,OAAO,EACPnV,OAAO,EACPwd,cAAc,EACd3P,YAAY,EACZ4P,uBAAuB,EACvBa,UAAU,CACX,CAAA;MACD,OAAON,UAAU,CAAC7b,MAAM,CAAC,GACrBA,MAAM,GAAAhF,QAAA,CAAA,EAAA,EAEDgF,MAAM,EAAA;AACT8N,QAAAA,UAAU,EAAE,IAAI;AAChB6N,QAAAA,aAAa,EAAE,EAAE;OAClB,CAAA,CAAA;KACN,CAAC,OAAOlhB,CAAC,EAAE;AACV;AACA;AACA;MACA,IAAI6hB,oBAAoB,CAAC7hB,CAAC,CAAC,IAAIohB,UAAU,CAACphB,CAAC,CAACuF,MAAM,CAAC,EAAE;AACnD,QAAA,IAAIvF,CAAC,CAACyL,IAAI,KAAK/J,UAAU,CAACP,KAAK,EAAE;UAC/B,MAAMnB,CAAC,CAACuF,MAAM,CAAA;AACf,SAAA;QACD,OAAOvF,CAAC,CAACuF,MAAM,CAAA;AAChB,OAAA;AACD;AACA;AACA,MAAA,IAAIuc,kBAAkB,CAAC9hB,CAAC,CAAC,EAAE;AACzB,QAAA,OAAOA,CAAC,CAAA;AACT,OAAA;AACD,MAAA,MAAMA,CAAC,CAAA;AACR,KAAA;AACH,GAAA;AAEA,EAAA,eAAe2hB,MAAMA,CACnBpJ,OAAgB,EAChBnV,OAAiC,EACjCuW,WAAmC,EACnCiH,cAAuB,EACvB3P,YAAyC,EACzC4P,uBAAgC,EAChCkB,cAAuB,EAAA;AAEvB,IAAA,IAAIxc,MAAkB,CAAA;AAEtB,IAAA,IAAI,CAACoU,WAAW,CAAC7X,KAAK,CAACjG,MAAM,IAAI,CAAC8d,WAAW,CAAC7X,KAAK,CAAC4Q,IAAI,EAAE;AACxD,MAAA,IAAIvR,KAAK,GAAGgR,sBAAsB,CAAC,GAAG,EAAE;QACtC0H,MAAM,EAAEtB,OAAO,CAACsB,MAAM;QACtBpd,QAAQ,EAAE,IAAIS,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,CAAC3C,QAAQ;AACvCqc,QAAAA,OAAO,EAAEa,WAAW,CAAC7X,KAAK,CAACQ,EAAAA;AAC5B,OAAA,CAAC,CAAA;AACF,MAAA,IAAIyf,cAAc,EAAE;AAClB,QAAA,MAAM5gB,KAAK,CAAA;AACZ,OAAA;AACDoE,MAAAA,MAAM,GAAG;QACPkG,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAAA;OACD,CAAA;AACF,KAAA,MAAM;MACL,IAAI2Y,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRxB,OAAO,EACP,CAACoB,WAAW,CAAC,EACbvW,OAAO,EACP2e,cAAc,EACdnB,cAAc,EACd3P,YAAY,CACb,CAAA;MACD1L,MAAM,GAAGuU,OAAO,CAACH,WAAW,CAAC7X,KAAK,CAACQ,EAAE,CAAC,CAAA;AAEtC,MAAA,IAAIiW,OAAO,CAAC9L,MAAM,CAACa,OAAO,EAAE;AAC1B0U,QAAAA,8BAA8B,CAACzJ,OAAO,EAAEwJ,cAAc,EAAE1Q,MAAM,CAAC,CAAA;AAChE,OAAA;AACF,KAAA;AAED,IAAA,IAAI2I,gBAAgB,CAACzU,MAAM,CAAC,EAAE;AAC5B;AACA;AACA;AACA;AACA,MAAA,MAAM,IAAI+F,QAAQ,CAAC,IAAI,EAAE;AACvBL,QAAAA,MAAM,EAAE1F,MAAM,CAACuJ,QAAQ,CAAC7D,MAAM;AAC9BC,QAAAA,OAAO,EAAE;UACP+W,QAAQ,EAAE1c,MAAM,CAACuJ,QAAQ,CAAC5D,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAA;AACjD,SAAA;AACF,OAAA,CAAC,CAAA;AACH,KAAA;AAED,IAAA,IAAI8M,gBAAgB,CAAC5U,MAAM,CAAC,EAAE;AAC5B,MAAA,IAAIpE,KAAK,GAAGgR,sBAAsB,CAAC,GAAG,EAAE;AAAE1G,QAAAA,IAAI,EAAE,cAAA;AAAgB,OAAA,CAAC,CAAA;AACjE,MAAA,IAAIsW,cAAc,EAAE;AAClB,QAAA,MAAM5gB,KAAK,CAAA;AACZ,OAAA;AACDoE,MAAAA,MAAM,GAAG;QACPkG,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAAA;OACD,CAAA;AACF,KAAA;AAED,IAAA,IAAI4gB,cAAc,EAAE;AAClB;AACA;AACA,MAAA,IAAIhJ,aAAa,CAACxT,MAAM,CAAC,EAAE;QACzB,MAAMA,MAAM,CAACpE,KAAK,CAAA;AACnB,OAAA;MAED,OAAO;QACLiC,OAAO,EAAE,CAACuW,WAAW,CAAC;QACtBhW,UAAU,EAAE,EAAE;AACd0P,QAAAA,UAAU,EAAE;AAAE,UAAA,CAACsG,WAAW,CAAC7X,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAAC1B,IAAAA;SAAM;AACnD+O,QAAAA,MAAM,EAAE,IAAI;AACZ;AACA;AACAoO,QAAAA,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;AACjBtM,QAAAA,eAAe,EAAE,IAAA;OAClB,CAAA;AACF,KAAA;AAED;IACA,IAAIsN,aAAa,GAAG,IAAIC,OAAO,CAAC5J,OAAO,CAACnZ,GAAG,EAAE;MAC3C8L,OAAO,EAAEqN,OAAO,CAACrN,OAAO;MACxB0D,QAAQ,EAAE2J,OAAO,CAAC3J,QAAQ;MAC1BnC,MAAM,EAAE8L,OAAO,CAAC9L,MAAAA;AACjB,KAAA,CAAC,CAAA;AAEF,IAAA,IAAIsM,aAAa,CAACxT,MAAM,CAAC,EAAE;AACzB;AACA;AACA,MAAA,IAAI6U,aAAa,GAAGyG,uBAAuB,GACvClH,WAAW,GACXjB,mBAAmB,CAACtV,OAAO,EAAEuW,WAAW,CAAC7X,KAAK,CAACQ,EAAE,CAAC,CAAA;MAEtD,IAAI8f,OAAO,GAAG,MAAMR,aAAa,CAC/BM,aAAa,EACb9e,OAAO,EACPwd,cAAc,EACd3P,YAAY,EACZ4P,uBAAuB,EACvB,IAAI,EACJ,CAACzG,aAAa,CAACtY,KAAK,CAACQ,EAAE,EAAEiD,MAAM,CAAC,CACjC,CAAA;AAED;MACA,OAAAhF,QAAA,KACK6hB,OAAO,EAAA;QACVpB,UAAU,EAAE9R,oBAAoB,CAAC3J,MAAM,CAACpE,KAAK,CAAC,GAC1CoE,MAAM,CAACpE,KAAK,CAAC8J,MAAM,GACnB1F,MAAM,CAACyb,UAAU,IAAI,IAAI,GACzBzb,MAAM,CAACyb,UAAU,GACjB,GAAG;AACP3N,QAAAA,UAAU,EAAE,IAAI;AAChB6N,QAAAA,aAAa,EAAA3gB,QAAA,CAAA,EAAA,EACPgF,MAAM,CAAC2F,OAAO,GAAG;AAAE,UAAA,CAACyO,WAAW,CAAC7X,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAAC2F,OAAAA;SAAS,GAAG,EAAE,CAAA;AACrE,OAAA,CAAA,CAAA;AAEJ,KAAA;AAED,IAAA,IAAIkX,OAAO,GAAG,MAAMR,aAAa,CAC/BM,aAAa,EACb9e,OAAO,EACPwd,cAAc,EACd3P,YAAY,EACZ4P,uBAAuB,EACvB,IAAI,CACL,CAAA;IAED,OAAAtgB,QAAA,KACK6hB,OAAO,EAAA;AACV/O,MAAAA,UAAU,EAAE;AACV,QAAA,CAACsG,WAAW,CAAC7X,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAAC1B,IAAAA;AAChC,OAAA;KAEG0B,EAAAA,MAAM,CAACyb,UAAU,GAAG;MAAEA,UAAU,EAAEzb,MAAM,CAACyb,UAAAA;KAAY,GAAG,EAAE,EAAA;AAC9DE,MAAAA,aAAa,EAAE3b,MAAM,CAAC2F,OAAO,GACzB;AAAE,QAAA,CAACyO,WAAW,CAAC7X,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAAC2F,OAAAA;AAAS,OAAA,GAC1C,EAAE;AAAA,KAAA,CAAA,CAAA;AAEV,GAAA;AAEA,EAAA,eAAe0W,aAAaA,CAC1BrJ,OAAgB,EAChBnV,OAAiC,EACjCwd,cAAuB,EACvB3P,YAAyC,EACzC4P,uBAAgC,EAChCa,UAAyC,EACzCjJ,mBAAyC,EAAA;AAQzC,IAAA,IAAIsJ,cAAc,GAAGL,UAAU,IAAI,IAAI,CAAA;AAEvC;AACA,IAAA,IACEK,cAAc,IACd,EAACL,UAAU,IAAVA,IAAAA,IAAAA,UAAU,CAAE5f,KAAK,CAAC6Q,MAAM,CACzB,IAAA,EAAC+O,UAAU,IAAVA,IAAAA,IAAAA,UAAU,CAAE5f,KAAK,CAAC4Q,IAAI,CACvB,EAAA;MACA,MAAMP,sBAAsB,CAAC,GAAG,EAAE;QAChC0H,MAAM,EAAEtB,OAAO,CAACsB,MAAM;QACtBpd,QAAQ,EAAE,IAAIS,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,CAAC3C,QAAQ;AACvCqc,QAAAA,OAAO,EAAE4I,UAAU,IAAA,IAAA,GAAA,KAAA,CAAA,GAAVA,UAAU,CAAE5f,KAAK,CAACQ,EAAAA;AAC5B,OAAA,CAAC,CAAA;AACH,KAAA;AAED,IAAA,IAAI8Z,cAAc,GAAGsF,UAAU,GAC3B,CAACA,UAAU,CAAC,GACZjJ,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAC5D4J,6BAA6B,CAACjf,OAAO,EAAEqV,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAC9DrV,OAAO,CAAA;AACX,IAAA,IAAIqX,aAAa,GAAG2B,cAAc,CAAC7V,MAAM,CACtCkM,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAAC6Q,MAAM,IAAIF,CAAC,CAAC3Q,KAAK,CAAC4Q,IAAI,CACtC,CAAA;AAED;AACA,IAAA,IAAI+H,aAAa,CAAC7e,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO;QACLwH,OAAO;AACP;AACAO,QAAAA,UAAU,EAAEP,OAAO,CAACoD,MAAM,CACxB,CAACkG,GAAG,EAAE+F,CAAC,KAAKtL,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAE;AAAE,UAAA,CAAC+F,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,GAAG,IAAA;AAAI,SAAE,CAAC,EACtD,EAAE,CACH;QACDsQ,MAAM,EACJ6F,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACxD;UACE,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,CAACtX,KAAAA;AAClD,SAAA,GACD,IAAI;AACV6f,QAAAA,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,EAAE;AACjBrM,QAAAA,eAAe,EAAE,IAAA;OAClB,CAAA;AACF,KAAA;AAED,IAAA,IAAIkF,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRxB,OAAO,EACPkC,aAAa,EACbrX,OAAO,EACP2e,cAAc,EACdnB,cAAc,EACd3P,YAAY,CACb,CAAA;AAED,IAAA,IAAIsH,OAAO,CAAC9L,MAAM,CAACa,OAAO,EAAE;AAC1B0U,MAAAA,8BAA8B,CAACzJ,OAAO,EAAEwJ,cAAc,EAAE1Q,MAAM,CAAC,CAAA;AAChE,KAAA;AAED;AACA,IAAA,IAAIuD,eAAe,GAAG,IAAIrB,GAAG,EAAwB,CAAA;AACrD,IAAA,IAAI6O,OAAO,GAAGE,sBAAsB,CAClClf,OAAO,EACP0W,OAAO,EACPrB,mBAAmB,EACnB7D,eAAe,EACfiM,uBAAuB,CACxB,CAAA;AAED;AACA,IAAA,IAAI0B,eAAe,GAAG,IAAI3gB,GAAG,CAC3B6Y,aAAa,CAACpf,GAAG,CAAEqI,KAAK,IAAKA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAC7C,CAAA;AACDc,IAAAA,OAAO,CAACsB,OAAO,CAAEhB,KAAK,IAAI;MACxB,IAAI,CAAC6e,eAAe,CAACnX,GAAG,CAAC1H,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,EAAE;QACxC8f,OAAO,CAACze,UAAU,CAACD,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,GAAG,IAAI,CAAA;AAC1C,OAAA;AACH,KAAC,CAAC,CAAA;IAEF,OAAA/B,QAAA,KACK6hB,OAAO,EAAA;MACVhf,OAAO;AACPwR,MAAAA,eAAe,EACbA,eAAe,CAAC1G,IAAI,GAAG,CAAC,GACpB/G,MAAM,CAACqb,WAAW,CAAC5N,eAAe,CAACxZ,OAAO,EAAE,CAAC,GAC7C,IAAA;AAAI,KAAA,CAAA,CAAA;AAEd,GAAA;AAEA;AACA;AACA,EAAA,eAAe2e,gBAAgBA,CAC7BtO,IAAyB,EACzB8M,OAAgB,EAChBkC,aAAuC,EACvCrX,OAAiC,EACjC2e,cAAuB,EACvBnB,cAAuB,EACvB3P,YAAyC,EAAA;IAEzC,IAAI6I,OAAO,GAAG,MAAM6D,oBAAoB,CACtC1M,YAAY,IAAIC,mBAAmB,EACnCzF,IAAI,EACJ,IAAI,EACJ8M,OAAO,EACPkC,aAAa,EACbrX,OAAO,EACP,IAAI,EACJjB,QAAQ,EACRF,kBAAkB,EAClB2e,cAAc,CACf,CAAA;IAED,IAAIlD,WAAW,GAA+B,EAAE,CAAA;IAChD,MAAMvR,OAAO,CAACgS,GAAG,CACf/a,OAAO,CAAC/H,GAAG,CAAC,MAAOqI,KAAK,IAAI;MAC1B,IAAI,EAAEA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,IAAIwX,OAAO,CAAC,EAAE;AAChC,QAAA,OAAA;AACD,OAAA;MACD,IAAIvU,MAAM,GAAGuU,OAAO,CAACpW,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;AACpC,MAAA,IAAIsb,kCAAkC,CAACrY,MAAM,CAAC,EAAE;AAC9C,QAAA,IAAIuJ,QAAQ,GAAGvJ,MAAM,CAACA,MAAkB,CAAA;AACxC;AACA,QAAA,MAAMsY,wCAAwC,CAC5C/O,QAAQ,EACRyJ,OAAO,EACP7U,KAAK,CAAC5B,KAAK,CAACQ,EAAE,EACdc,OAAO,EACPP,QAAQ,EACRwO,MAAM,CAACvH,oBAAoB,CAC5B,CAAA;AACF,OAAA;MACD,IAAIsX,UAAU,CAAC7b,MAAM,CAACA,MAAM,CAAC,IAAIwc,cAAc,EAAE;AAC/C;AACA;AACA,QAAA,MAAMxc,MAAM,CAAA;AACb,OAAA;AAEDmY,MAAAA,WAAW,CAACha,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,GACzB,MAAMwb,qCAAqC,CAACvY,MAAM,CAAC,CAAA;AACvD,KAAC,CAAC,CACH,CAAA;AACD,IAAA,OAAOmY,WAAW,CAAA;AACpB,GAAA;EAEA,OAAO;IACL5M,UAAU;IACV4P,KAAK;AACLW,IAAAA,UAAAA;GACD,CAAA;AACH,CAAA;AAEA;AAEA;AACA;AACA;AAEA;;;AAGG;SACaoB,yBAAyBA,CACvCzgB,MAAiC,EACjCogB,OAA6B,EAC7BjhB,KAAU,EAAA;AAEV,EAAA,IAAIuhB,UAAU,GAAAniB,QAAA,CAAA,EAAA,EACT6hB,OAAO,EAAA;IACVpB,UAAU,EAAE9R,oBAAoB,CAAC/N,KAAK,CAAC,GAAGA,KAAK,CAAC8J,MAAM,GAAG,GAAG;AAC5D2H,IAAAA,MAAM,EAAE;MACN,CAACwP,OAAO,CAACO,0BAA0B,IAAI3gB,MAAM,CAAC,CAAC,CAAC,CAACM,EAAE,GAAGnB,KAAAA;AACvD,KAAA;GACF,CAAA,CAAA;AACD,EAAA,OAAOuhB,UAAU,CAAA;AACnB,CAAA;AAEA,SAASV,8BAA8BA,CACrCzJ,OAAgB,EAChBwJ,cAAuB,EACvB1Q,MAAiC,EAAA;EAEjC,IAAIA,MAAM,CAACoP,mBAAmB,IAAIlI,OAAO,CAAC9L,MAAM,CAACmW,MAAM,KAAKlnB,SAAS,EAAE;AACrE,IAAA,MAAM6c,OAAO,CAAC9L,MAAM,CAACmW,MAAM,CAAA;AAC5B,GAAA;AAED,EAAA,IAAI/I,MAAM,GAAGkI,cAAc,GAAG,YAAY,GAAG,OAAO,CAAA;AACpD,EAAA,MAAM,IAAIniB,KAAK,CAAIia,MAAM,GAAoBtB,mBAAAA,GAAAA,OAAO,CAACsB,MAAM,GAAItB,GAAAA,GAAAA,OAAO,CAACnZ,GAAK,CAAC,CAAA;AAC/E,CAAA;AAEA,SAASyjB,sBAAsBA,CAC7B7M,IAAgC,EAAA;EAEhC,OACEA,IAAI,IAAI,IAAI,KACV,UAAU,IAAIA,IAAI,IAAIA,IAAI,CAACnG,QAAQ,IAAI,IAAI,IAC1C,MAAM,IAAImG,IAAI,IAAIA,IAAI,CAAC8M,IAAI,KAAKpnB,SAAU,CAAC,CAAA;AAElD,CAAA;AAEA,SAAS0b,WAAWA,CAClB7a,QAAc,EACd6G,OAAiC,EACjCP,QAAgB,EAChBkgB,eAAwB,EACxB1mB,EAAa,EACbyN,oBAA6B,EAC7BuN,WAAoB,EACpBC,QAA8B,EAAA;AAE9B,EAAA,IAAI0L,iBAA2C,CAAA;AAC/C,EAAA,IAAIC,gBAAoD,CAAA;AACxD,EAAA,IAAI5L,WAAW,EAAE;AACf;AACA;AACA2L,IAAAA,iBAAiB,GAAG,EAAE,CAAA;AACtB,IAAA,KAAK,IAAItf,KAAK,IAAIN,OAAO,EAAE;AACzB4f,MAAAA,iBAAiB,CAACxlB,IAAI,CAACkG,KAAK,CAAC,CAAA;AAC7B,MAAA,IAAIA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,KAAK+U,WAAW,EAAE;AAClC4L,QAAAA,gBAAgB,GAAGvf,KAAK,CAAA;AACxB,QAAA,MAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA,MAAM;AACLsf,IAAAA,iBAAiB,GAAG5f,OAAO,CAAA;IAC3B6f,gBAAgB,GAAG7f,OAAO,CAACA,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAAA;AAC/C,GAAA;AAED;AACA,EAAA,IAAIwB,IAAI,GAAG4M,SAAS,CAClB3N,EAAE,GAAGA,EAAE,GAAG,GAAG,EACbwN,mBAAmB,CAACmZ,iBAAiB,EAAElZ,oBAAoB,CAAC,EAC5D9G,aAAa,CAACzG,QAAQ,CAACE,QAAQ,EAAEoG,QAAQ,CAAC,IAAItG,QAAQ,CAACE,QAAQ,EAC/D6a,QAAQ,KAAK,MAAM,CACpB,CAAA;AAED;AACA;AACA;EACA,IAAIjb,EAAE,IAAI,IAAI,EAAE;AACde,IAAAA,IAAI,CAACE,MAAM,GAAGf,QAAQ,CAACe,MAAM,CAAA;AAC7BF,IAAAA,IAAI,CAACG,IAAI,GAAGhB,QAAQ,CAACgB,IAAI,CAAA;AAC1B,GAAA;AAED;AACA,EAAA,IAAI,CAAClB,EAAE,IAAI,IAAI,IAAIA,EAAE,KAAK,EAAE,IAAIA,EAAE,KAAK,GAAG,KAAK4mB,gBAAgB,EAAE;AAC/D,IAAA,IAAIC,UAAU,GAAGC,kBAAkB,CAAC/lB,IAAI,CAACE,MAAM,CAAC,CAAA;IAChD,IAAI2lB,gBAAgB,CAACnhB,KAAK,CAACvG,KAAK,IAAI,CAAC2nB,UAAU,EAAE;AAC/C;AACA9lB,MAAAA,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACE,MAAM,GACrBF,IAAI,CAACE,MAAM,CAACO,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ,CAAA;KACb,MAAM,IAAI,CAAColB,gBAAgB,CAACnhB,KAAK,CAACvG,KAAK,IAAI2nB,UAAU,EAAE;AACtD;MACA,IAAItf,MAAM,GAAG,IAAIwf,eAAe,CAAChmB,IAAI,CAACE,MAAM,CAAC,CAAA;AAC7C,MAAA,IAAI+lB,WAAW,GAAGzf,MAAM,CAAC0f,MAAM,CAAC,OAAO,CAAC,CAAA;AACxC1f,MAAAA,MAAM,CAAC2J,MAAM,CAAC,OAAO,CAAC,CAAA;MACtB8V,WAAW,CAAC9c,MAAM,CAAEoC,CAAC,IAAKA,CAAC,CAAC,CAACjE,OAAO,CAAEiE,CAAC,IAAK/E,MAAM,CAAC2f,MAAM,CAAC,OAAO,EAAE5a,CAAC,CAAC,CAAC,CAAA;AACtE,MAAA,IAAI6a,EAAE,GAAG5f,MAAM,CAACzD,QAAQ,EAAE,CAAA;AAC1B/C,MAAAA,IAAI,CAACE,MAAM,GAAGkmB,EAAE,GAAOA,GAAAA,GAAAA,EAAE,GAAK,EAAE,CAAA;AACjC,KAAA;AACF,GAAA;AAED;AACA;AACA;AACA;AACA,EAAA,IAAIT,eAAe,IAAIlgB,QAAQ,KAAK,GAAG,EAAE;IACvCzF,IAAI,CAACX,QAAQ,GACXW,IAAI,CAACX,QAAQ,KAAK,GAAG,GAAGoG,QAAQ,GAAGwB,SAAS,CAAC,CAACxB,QAAQ,EAAEzF,IAAI,CAACX,QAAQ,CAAC,CAAC,CAAA;AAC1E,GAAA;EAED,OAAOM,UAAU,CAACK,IAAI,CAAC,CAAA;AACzB,CAAA;AAEA;AACA;AACA,SAASoa,wBAAwBA,CAC/BiM,mBAA4B,EAC5BC,SAAkB,EAClBtmB,IAAY,EACZ4Y,IAAiC,EAAA;AAMjC;EACA,IAAI,CAACA,IAAI,IAAI,CAAC6M,sBAAsB,CAAC7M,IAAI,CAAC,EAAE;IAC1C,OAAO;AAAE5Y,MAAAA,IAAAA;KAAM,CAAA;AAChB,GAAA;EAED,IAAI4Y,IAAI,CAACtG,UAAU,IAAI,CAACoR,aAAa,CAAC9K,IAAI,CAACtG,UAAU,CAAC,EAAE;IACtD,OAAO;MACLtS,IAAI;AACJ+D,MAAAA,KAAK,EAAEgR,sBAAsB,CAAC,GAAG,EAAE;QAAE0H,MAAM,EAAE7D,IAAI,CAACtG,UAAAA;OAAY,CAAA;KAC/D,CAAA;AACF,GAAA;EAED,IAAIiU,mBAAmB,GAAGA,OAAO;IAC/BvmB,IAAI;AACJ+D,IAAAA,KAAK,EAAEgR,sBAAsB,CAAC,GAAG,EAAE;AAAE1G,MAAAA,IAAI,EAAE,cAAA;KAAgB,CAAA;AAC5D,GAAA,CAAC,CAAA;AAEF;AACA,EAAA,IAAImY,aAAa,GAAG5N,IAAI,CAACtG,UAAU,IAAI,KAAK,CAAA;AAC5C,EAAA,IAAIA,UAAU,GAAG+T,mBAAmB,GAC/BG,aAAa,CAACC,WAAW,EAAoB,GAC7CD,aAAa,CAAC/a,WAAW,EAAiB,CAAA;AAC/C,EAAA,IAAI8G,UAAU,GAAGmU,iBAAiB,CAAC1mB,IAAI,CAAC,CAAA;AAExC,EAAA,IAAI4Y,IAAI,CAAC8M,IAAI,KAAKpnB,SAAS,EAAE;AAC3B,IAAA,IAAIsa,IAAI,CAACpG,WAAW,KAAK,YAAY,EAAE;AACrC;AACA,MAAA,IAAI,CAAC+G,gBAAgB,CAACjH,UAAU,CAAC,EAAE;QACjC,OAAOiU,mBAAmB,EAAE,CAAA;AAC7B,OAAA;MAED,IAAI7T,IAAI,GACN,OAAOkG,IAAI,CAAC8M,IAAI,KAAK,QAAQ,GACzB9M,IAAI,CAAC8M,IAAI,GACT9M,IAAI,CAAC8M,IAAI,YAAYiB,QAAQ,IAC7B/N,IAAI,CAAC8M,IAAI,YAAYM,eAAe;AACpC;AACArX,MAAAA,KAAK,CAACzB,IAAI,CAAC0L,IAAI,CAAC8M,IAAI,CAAC1nB,OAAO,EAAE,CAAC,CAACoL,MAAM,CACpC,CAACkG,GAAG,EAAA0B,KAAA,KAAA;AAAA,QAAA,IAAE,CAAC/M,IAAI,EAAE3B,KAAK,CAAC,GAAA0O,KAAA,CAAA;AAAA,QAAA,OAAA,EAAA,GAAQ1B,GAAG,GAAGrL,IAAI,GAAA,GAAA,GAAI3B,KAAK,GAAA,IAAA,CAAA;OAAI,EAClD,EAAE,CACH,GACD2C,MAAM,CAAC2T,IAAI,CAAC8M,IAAI,CAAC,CAAA;MAEvB,OAAO;QACL1lB,IAAI;AACJma,QAAAA,UAAU,EAAE;UACV7H,UAAU;UACVC,UAAU;UACVC,WAAW,EAAEoG,IAAI,CAACpG,WAAW;AAC7BC,UAAAA,QAAQ,EAAEnU,SAAS;AACnBoP,UAAAA,IAAI,EAAEpP,SAAS;AACfoU,UAAAA,IAAAA;AACD,SAAA;OACF,CAAA;AACF,KAAA,MAAM,IAAIkG,IAAI,CAACpG,WAAW,KAAK,kBAAkB,EAAE;AAClD;AACA,MAAA,IAAI,CAAC+G,gBAAgB,CAACjH,UAAU,CAAC,EAAE;QACjC,OAAOiU,mBAAmB,EAAE,CAAA;AAC7B,OAAA;MAED,IAAI;QACF,IAAI7Y,IAAI,GACN,OAAOkL,IAAI,CAAC8M,IAAI,KAAK,QAAQ,GAAGlmB,IAAI,CAAConB,KAAK,CAAChO,IAAI,CAAC8M,IAAI,CAAC,GAAG9M,IAAI,CAAC8M,IAAI,CAAA;QAEnE,OAAO;UACL1lB,IAAI;AACJma,UAAAA,UAAU,EAAE;YACV7H,UAAU;YACVC,UAAU;YACVC,WAAW,EAAEoG,IAAI,CAACpG,WAAW;AAC7BC,YAAAA,QAAQ,EAAEnU,SAAS;YACnBoP,IAAI;AACJgF,YAAAA,IAAI,EAAEpU,SAAAA;AACP,WAAA;SACF,CAAA;OACF,CAAC,OAAOsE,CAAC,EAAE;QACV,OAAO2jB,mBAAmB,EAAE,CAAA;AAC7B,OAAA;AACF,KAAA;AACF,GAAA;AAEDlkB,EAAAA,SAAS,CACP,OAAOskB,QAAQ,KAAK,UAAU,EAC9B,+CAA+C,CAChD,CAAA;AAED,EAAA,IAAIE,YAA6B,CAAA;AACjC,EAAA,IAAIpU,QAAkB,CAAA;EAEtB,IAAImG,IAAI,CAACnG,QAAQ,EAAE;AACjBoU,IAAAA,YAAY,GAAGC,6BAA6B,CAAClO,IAAI,CAACnG,QAAQ,CAAC,CAAA;IAC3DA,QAAQ,GAAGmG,IAAI,CAACnG,QAAQ,CAAA;AACzB,GAAA,MAAM,IAAImG,IAAI,CAAC8M,IAAI,YAAYiB,QAAQ,EAAE;AACxCE,IAAAA,YAAY,GAAGC,6BAA6B,CAAClO,IAAI,CAAC8M,IAAI,CAAC,CAAA;IACvDjT,QAAQ,GAAGmG,IAAI,CAAC8M,IAAI,CAAA;AACrB,GAAA,MAAM,IAAI9M,IAAI,CAAC8M,IAAI,YAAYM,eAAe,EAAE;IAC/Ca,YAAY,GAAGjO,IAAI,CAAC8M,IAAI,CAAA;AACxBjT,IAAAA,QAAQ,GAAGsU,6BAA6B,CAACF,YAAY,CAAC,CAAA;AACvD,GAAA,MAAM,IAAIjO,IAAI,CAAC8M,IAAI,IAAI,IAAI,EAAE;AAC5BmB,IAAAA,YAAY,GAAG,IAAIb,eAAe,EAAE,CAAA;AACpCvT,IAAAA,QAAQ,GAAG,IAAIkU,QAAQ,EAAE,CAAA;AAC1B,GAAA,MAAM;IACL,IAAI;AACFE,MAAAA,YAAY,GAAG,IAAIb,eAAe,CAACpN,IAAI,CAAC8M,IAAI,CAAC,CAAA;AAC7CjT,MAAAA,QAAQ,GAAGsU,6BAA6B,CAACF,YAAY,CAAC,CAAA;KACvD,CAAC,OAAOjkB,CAAC,EAAE;MACV,OAAO2jB,mBAAmB,EAAE,CAAA;AAC7B,KAAA;AACF,GAAA;AAED,EAAA,IAAIpM,UAAU,GAAe;IAC3B7H,UAAU;IACVC,UAAU;AACVC,IAAAA,WAAW,EACRoG,IAAI,IAAIA,IAAI,CAACpG,WAAW,IAAK,mCAAmC;IACnEC,QAAQ;AACR/E,IAAAA,IAAI,EAAEpP,SAAS;AACfoU,IAAAA,IAAI,EAAEpU,SAAAA;GACP,CAAA;AAED,EAAA,IAAIib,gBAAgB,CAACY,UAAU,CAAC7H,UAAU,CAAC,EAAE;IAC3C,OAAO;MAAEtS,IAAI;AAAEma,MAAAA,UAAAA;KAAY,CAAA;AAC5B,GAAA;AAED;AACA,EAAA,IAAI9W,UAAU,GAAGpD,SAAS,CAACD,IAAI,CAAC,CAAA;AAChC;AACA;AACA;AACA,EAAA,IAAIsmB,SAAS,IAAIjjB,UAAU,CAACnD,MAAM,IAAI6lB,kBAAkB,CAAC1iB,UAAU,CAACnD,MAAM,CAAC,EAAE;AAC3E2mB,IAAAA,YAAY,CAACV,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;AACjC,GAAA;EACD9iB,UAAU,CAACnD,MAAM,GAAA,GAAA,GAAO2mB,YAAc,CAAA;EAEtC,OAAO;AAAE7mB,IAAAA,IAAI,EAAEL,UAAU,CAAC0D,UAAU,CAAC;AAAE8W,IAAAA,UAAAA;GAAY,CAAA;AACrD,CAAA;AAEA;AACA;AACA,SAAS8K,6BAA6BA,CACpCjf,OAAiC,EACjCqW,UAAkB,EAClB2K,eAAe,EAAQ;AAAA,EAAA,IAAvBA,eAAe,KAAA,KAAA,CAAA,EAAA;AAAfA,IAAAA,eAAe,GAAG,KAAK,CAAA;AAAA,GAAA;AAEvB,EAAA,IAAI7oB,KAAK,GAAG6H,OAAO,CAACyP,SAAS,CAAEJ,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAKmX,UAAU,CAAC,CAAA;EAC/D,IAAIle,KAAK,IAAI,CAAC,EAAE;AACd,IAAA,OAAO6H,OAAO,CAAC7D,KAAK,CAAC,CAAC,EAAE6kB,eAAe,GAAG7oB,KAAK,GAAG,CAAC,GAAGA,KAAK,CAAC,CAAA;AAC7D,GAAA;AACD,EAAA,OAAO6H,OAAO,CAAA;AAChB,CAAA;AAEA,SAASuX,gBAAgBA,CACvB3d,OAAgB,EAChBvB,KAAkB,EAClB2H,OAAiC,EACjCmU,UAAkC,EAClChb,QAAkB,EAClBmZ,gBAAyB,EACzB2O,2BAAoC,EACpCpQ,sBAA+B,EAC/BC,uBAAiC,EACjCC,qBAAkC,EAClCQ,eAA4B,EAC5BF,gBAA6C,EAC7CD,gBAA6B,EAC7B0D,WAAsC,EACtCrV,QAA4B,EAC5B4V,mBAAyC,EAAA;EAEzC,IAAIE,YAAY,GAAGF,mBAAmB,GAClCM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACnCA,mBAAmB,CAAC,CAAC,CAAC,CAACtX,KAAK,GAC5BsX,mBAAmB,CAAC,CAAC,CAAC,CAAC5U,IAAI,GAC7BnI,SAAS,CAAA;EACb,IAAI4oB,UAAU,GAAGtnB,OAAO,CAACC,SAAS,CAACxB,KAAK,CAACc,QAAQ,CAAC,CAAA;AAClD,EAAA,IAAIgoB,OAAO,GAAGvnB,OAAO,CAACC,SAAS,CAACV,QAAQ,CAAC,CAAA;AAEzC;EACA,IAAIioB,eAAe,GAAGphB,OAAO,CAAA;AAC7B,EAAA,IAAIsS,gBAAgB,IAAIja,KAAK,CAACmX,MAAM,EAAE;AACpC;AACA;AACA;AACA;AACA;AACA4R,IAAAA,eAAe,GAAGnC,6BAA6B,CAC7Cjf,OAAO,EACP+D,MAAM,CAAC0P,IAAI,CAACpb,KAAK,CAACmX,MAAM,CAAC,CAAC,CAAC,CAAC,EAC5B,IAAI,CACL,CAAA;GACF,MAAM,IAAI6F,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE;AACvE;AACA;IACA+L,eAAe,GAAGnC,6BAA6B,CAC7Cjf,OAAO,EACPqV,mBAAmB,CAAC,CAAC,CAAC,CACvB,CAAA;AACF,GAAA;AAED;AACA;AACA;EACA,IAAIgM,YAAY,GAAGhM,mBAAmB,GAClCA,mBAAmB,CAAC,CAAC,CAAC,CAACuI,UAAU,GACjCtlB,SAAS,CAAA;EACb,IAAIgpB,sBAAsB,GACxBL,2BAA2B,IAAII,YAAY,IAAIA,YAAY,IAAI,GAAG,CAAA;EAEpE,IAAIE,iBAAiB,GAAGH,eAAe,CAACje,MAAM,CAAC,CAAC7C,KAAK,EAAEnI,KAAK,KAAI;IAC9D,IAAI;AAAEuG,MAAAA,KAAAA;AAAO,KAAA,GAAG4B,KAAK,CAAA;IACrB,IAAI5B,KAAK,CAAC4Q,IAAI,EAAE;AACd;AACA,MAAA,OAAO,IAAI,CAAA;AACZ,KAAA;AAED,IAAA,IAAI5Q,KAAK,CAAC6Q,MAAM,IAAI,IAAI,EAAE;AACxB,MAAA,OAAO,KAAK,CAAA;AACb,KAAA;AAED,IAAA,IAAI+C,gBAAgB,EAAE;MACpB,OAAO5C,0BAA0B,CAAChR,KAAK,EAAErG,KAAK,CAACkI,UAAU,EAAElI,KAAK,CAACmX,MAAM,CAAC,CAAA;AACzE,KAAA;AAED;AACA,IAAA,IACEgS,WAAW,CAACnpB,KAAK,CAACkI,UAAU,EAAElI,KAAK,CAAC2H,OAAO,CAAC7H,KAAK,CAAC,EAAEmI,KAAK,CAAC,IAC1DwQ,uBAAuB,CAAC5N,IAAI,CAAEhE,EAAE,IAAKA,EAAE,KAAKoB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,EAC3D;AACA,MAAA,OAAO,IAAI,CAAA;AACZ,KAAA;AAED;AACA;AACA;AACA;AACA,IAAA,IAAIuiB,iBAAiB,GAAGppB,KAAK,CAAC2H,OAAO,CAAC7H,KAAK,CAAC,CAAA;IAC5C,IAAIupB,cAAc,GAAGphB,KAAK,CAAA;AAE1B,IAAA,OAAOqhB,sBAAsB,CAACrhB,KAAK,EAAAnD,QAAA,CAAA;MACjC+jB,UAAU;MACVU,aAAa,EAAEH,iBAAiB,CAACjhB,MAAM;MACvC2gB,OAAO;MACPU,UAAU,EAAEH,cAAc,CAAClhB,MAAAA;AAAM,KAAA,EAC9B2T,UAAU,EAAA;MACboB,YAAY;MACZ8L,YAAY;MACZS,uBAAuB,EAAER,sBAAsB,GAC3C,KAAK;AACL;AACAzQ,MAAAA,sBAAsB,IACtBqQ,UAAU,CAAC7nB,QAAQ,GAAG6nB,UAAU,CAAChnB,MAAM,KACrCinB,OAAO,CAAC9nB,QAAQ,GAAG8nB,OAAO,CAACjnB,MAAM;AACnC;MACAgnB,UAAU,CAAChnB,MAAM,KAAKinB,OAAO,CAACjnB,MAAM,IACpC6nB,kBAAkB,CAACN,iBAAiB,EAAEC,cAAc,CAAA;AAAC,KAAA,CAC1D,CAAC,CAAA;AACJ,GAAC,CAAC,CAAA;AAEF;EACA,IAAIpK,oBAAoB,GAA0B,EAAE,CAAA;AACpDjG,EAAAA,gBAAgB,CAAC/P,OAAO,CAAC,CAAC0W,CAAC,EAAE9e,GAAG,KAAI;AAClC;AACA;AACA;AACA;AACA;IACA,IACEoZ,gBAAgB,IAChB,CAACtS,OAAO,CAACkD,IAAI,CAAEmM,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAK8Y,CAAC,CAACtC,OAAO,CAAC,IAC9CnE,eAAe,CAACvJ,GAAG,CAAC9O,GAAG,CAAC,EACxB;AACA,MAAA,OAAA;AACD,KAAA;IAED,IAAI8oB,cAAc,GAAGziB,WAAW,CAACuV,WAAW,EAAEkD,CAAC,CAAChe,IAAI,EAAEyF,QAAQ,CAAC,CAAA;AAE/D;AACA;AACA;AACA;IACA,IAAI,CAACuiB,cAAc,EAAE;MACnB1K,oBAAoB,CAACld,IAAI,CAAC;QACxBlB,GAAG;QACHwc,OAAO,EAAEsC,CAAC,CAACtC,OAAO;QAClB1b,IAAI,EAAEge,CAAC,CAAChe,IAAI;AACZgG,QAAAA,OAAO,EAAE,IAAI;AACbM,QAAAA,KAAK,EAAE,IAAI;AACX2I,QAAAA,UAAU,EAAE,IAAA;AACb,OAAA,CAAC,CAAA;AACF,MAAA,OAAA;AACD,KAAA;AAED;AACA;AACA;IACA,IAAI8J,OAAO,GAAG1a,KAAK,CAAC6X,QAAQ,CAACjG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;IACrC,IAAI+oB,YAAY,GAAGzL,cAAc,CAACwL,cAAc,EAAEhK,CAAC,CAAChe,IAAI,CAAC,CAAA;IAEzD,IAAIkoB,gBAAgB,GAAG,KAAK,CAAA;AAC5B,IAAA,IAAI9Q,gBAAgB,CAACpJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;AAC7B;AACAgpB,MAAAA,gBAAgB,GAAG,KAAK,CAAA;KACzB,MAAM,IAAInR,qBAAqB,CAAC/I,GAAG,CAAC9O,GAAG,CAAC,EAAE;AACzC;AACA6X,MAAAA,qBAAqB,CAAC5G,MAAM,CAACjR,GAAG,CAAC,CAAA;AACjCgpB,MAAAA,gBAAgB,GAAG,IAAI,CAAA;AACxB,KAAA,MAAM,IACLnP,OAAO,IACPA,OAAO,CAAC1a,KAAK,KAAK,MAAM,IACxB0a,OAAO,CAACtS,IAAI,KAAKnI,SAAS,EAC1B;AACA;AACA;AACA;AACA4pB,MAAAA,gBAAgB,GAAGrR,sBAAsB,CAAA;AAC1C,KAAA,MAAM;AACL;AACA;AACAqR,MAAAA,gBAAgB,GAAGP,sBAAsB,CAACM,YAAY,EAAA9kB,QAAA,CAAA;QACpD+jB,UAAU;AACVU,QAAAA,aAAa,EAAEvpB,KAAK,CAAC2H,OAAO,CAAC3H,KAAK,CAAC2H,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAACgI,MAAM;QAC7D2gB,OAAO;QACPU,UAAU,EAAE7hB,OAAO,CAACA,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAACgI,MAAAA;AAAM,OAAA,EAC3C2T,UAAU,EAAA;QACboB,YAAY;QACZ8L,YAAY;AACZS,QAAAA,uBAAuB,EAAER,sBAAsB,GAC3C,KAAK,GACLzQ,sBAAAA;AAAsB,OAAA,CAC3B,CAAC,CAAA;AACH,KAAA;AAED,IAAA,IAAIqR,gBAAgB,EAAE;MACpB5K,oBAAoB,CAACld,IAAI,CAAC;QACxBlB,GAAG;QACHwc,OAAO,EAAEsC,CAAC,CAACtC,OAAO;QAClB1b,IAAI,EAAEge,CAAC,CAAChe,IAAI;AACZgG,QAAAA,OAAO,EAAEgiB,cAAc;AACvB1hB,QAAAA,KAAK,EAAE2hB,YAAY;QACnBhZ,UAAU,EAAE,IAAIC,eAAe,EAAE;AAClC,OAAA,CAAC,CAAA;AACH,KAAA;AACH,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO,CAACqY,iBAAiB,EAAEjK,oBAAoB,CAAC,CAAA;AAClD,CAAA;AAEA,SAAS5H,0BAA0BA,CACjChR,KAA8B,EAC9B6B,UAAwC,EACxCiP,MAAoC,EAAA;AAEpC;EACA,IAAI9Q,KAAK,CAAC4Q,IAAI,EAAE;AACd,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED;AACA,EAAA,IAAI,CAAC5Q,KAAK,CAAC6Q,MAAM,EAAE;AACjB,IAAA,OAAO,KAAK,CAAA;AACb,GAAA;AAED,EAAA,IAAI4S,OAAO,GAAG5hB,UAAU,IAAI,IAAI,IAAIA,UAAU,CAAC7B,KAAK,CAACQ,EAAE,CAAC,KAAK5G,SAAS,CAAA;AACtE,EAAA,IAAI8pB,QAAQ,GAAG5S,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC9Q,KAAK,CAACQ,EAAE,CAAC,KAAK5G,SAAS,CAAA;AAE/D;AACA,EAAA,IAAI,CAAC6pB,OAAO,IAAIC,QAAQ,EAAE;AACxB,IAAA,OAAO,KAAK,CAAA;AACb,GAAA;AAED;AACA,EAAA,IAAI,OAAO1jB,KAAK,CAAC6Q,MAAM,KAAK,UAAU,IAAI7Q,KAAK,CAAC6Q,MAAM,CAAC8S,OAAO,KAAK,IAAI,EAAE;AACvE,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED;AACA,EAAA,OAAO,CAACF,OAAO,IAAI,CAACC,QAAQ,CAAA;AAC9B,CAAA;AAEA,SAASZ,WAAWA,CAClBc,iBAA4B,EAC5BC,YAAoC,EACpCjiB,KAA6B,EAAA;AAE7B,EAAA,IAAIkiB,KAAK;AACP;AACA,EAAA,CAACD,YAAY;AACb;EACAjiB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,KAAKqjB,YAAY,CAAC7jB,KAAK,CAACQ,EAAE,CAAA;AAE1C;AACA;EACA,IAAIujB,aAAa,GAAGH,iBAAiB,CAAChiB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,KAAK5G,SAAS,CAAA;AAEnE;EACA,OAAOkqB,KAAK,IAAIC,aAAa,CAAA;AAC/B,CAAA;AAEA,SAASV,kBAAkBA,CACzBQ,YAAoC,EACpCjiB,KAA6B,EAAA;AAE7B,EAAA,IAAIoiB,WAAW,GAAGH,YAAY,CAAC7jB,KAAK,CAAC1E,IAAI,CAAA;AACzC,EAAA;AACE;AACAuoB,IAAAA,YAAY,CAAClpB,QAAQ,KAAKiH,KAAK,CAACjH,QAAQ;AACxC;AACA;IACCqpB,WAAW,IAAI,IAAI,IAClBA,WAAW,CAAC1gB,QAAQ,CAAC,GAAG,CAAC,IACzBugB,YAAY,CAAC/hB,MAAM,CAAC,GAAG,CAAC,KAAKF,KAAK,CAACE,MAAM,CAAC,GAAG,CAAA;AAAE,IAAA;AAErD,CAAA;AAEA,SAASmhB,sBAAsBA,CAC7BgB,WAAmC,EACnCC,GAAiC,EAAA;AAEjC,EAAA,IAAID,WAAW,CAACjkB,KAAK,CAACwjB,gBAAgB,EAAE;IACtC,IAAIW,WAAW,GAAGF,WAAW,CAACjkB,KAAK,CAACwjB,gBAAgB,CAACU,GAAG,CAAC,CAAA;AACzD,IAAA,IAAI,OAAOC,WAAW,KAAK,SAAS,EAAE;AACpC,MAAA,OAAOA,WAAW,CAAA;AACnB,KAAA;AACF,GAAA;EAED,OAAOD,GAAG,CAACd,uBAAuB,CAAA;AACpC,CAAA;AAEA,SAASpF,eAAeA,CACtBhH,OAAsB,EACtBtW,QAA+B,EAC/B0V,WAAsC,EACtC/V,QAAuB,EACvBF,kBAA8C,EAAA;AAAA,EAAA,IAAAikB,gBAAA,CAAA;AAE9C,EAAA,IAAIC,eAA0C,CAAA;AAC9C,EAAA,IAAIrN,OAAO,EAAE;AACX,IAAA,IAAIhX,KAAK,GAAGK,QAAQ,CAAC2W,OAAO,CAAC,CAAA;AAC7BrZ,IAAAA,SAAS,CACPqC,KAAK,EAC+CgX,mDAAAA,GAAAA,OAAS,CAC9D,CAAA;AACD,IAAA,IAAI,CAAChX,KAAK,CAACU,QAAQ,EAAE;MACnBV,KAAK,CAACU,QAAQ,GAAG,EAAE,CAAA;AACpB,KAAA;IACD2jB,eAAe,GAAGrkB,KAAK,CAACU,QAAQ,CAAA;AACjC,GAAA,MAAM;AACL2jB,IAAAA,eAAe,GAAGjO,WAAW,CAAA;AAC9B,GAAA;AAED;AACA;AACA;EACA,IAAIkO,cAAc,GAAG5jB,QAAQ,CAAC+D,MAAM,CACjC8f,QAAQ,IACP,CAACF,eAAe,CAAC7f,IAAI,CAAEggB,aAAa,IAClCC,WAAW,CAACF,QAAQ,EAAEC,aAAa,CAAC,CACrC,CACJ,CAAA;AAED,EAAA,IAAIpG,SAAS,GAAGne,yBAAyB,CACvCqkB,cAAc,EACdnkB,kBAAkB,EAClB,CAAC6W,OAAO,IAAI,GAAG,EAAE,OAAO,EAAEzW,MAAM,CAAC,CAAA6jB,CAAAA,gBAAA,GAAAC,eAAe,qBAAfD,gBAAA,CAAiBtqB,MAAM,KAAI,GAAG,CAAC,CAAC,EACjEuG,QAAQ,CACT,CAAA;AAEDgkB,EAAAA,eAAe,CAAC3oB,IAAI,CAAC,GAAG0iB,SAAS,CAAC,CAAA;AACpC,CAAA;AAEA,SAASqG,WAAWA,CAClBF,QAA6B,EAC7BC,aAAkC,EAAA;AAElC;AACA,EAAA,IACE,IAAI,IAAID,QAAQ,IAChB,IAAI,IAAIC,aAAa,IACrBD,QAAQ,CAAC/jB,EAAE,KAAKgkB,aAAa,CAAChkB,EAAE,EAChC;AACA,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED;EACA,IACE,EACE+jB,QAAQ,CAAC9qB,KAAK,KAAK+qB,aAAa,CAAC/qB,KAAK,IACtC8qB,QAAQ,CAACjpB,IAAI,KAAKkpB,aAAa,CAAClpB,IAAI,IACpCipB,QAAQ,CAACliB,aAAa,KAAKmiB,aAAa,CAACniB,aAAa,CACvD,EACD;AACA,IAAA,OAAO,KAAK,CAAA;AACb,GAAA;AAED;AACA;EACA,IACE,CAAC,CAACkiB,QAAQ,CAAC7jB,QAAQ,IAAI6jB,QAAQ,CAAC7jB,QAAQ,CAAC5G,MAAM,KAAK,CAAC,MACpD,CAAC0qB,aAAa,CAAC9jB,QAAQ,IAAI8jB,aAAa,CAAC9jB,QAAQ,CAAC5G,MAAM,KAAK,CAAC,CAAC,EAChE;AACA,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED;AACA;EACA,OAAOyqB,QAAQ,CAAC7jB,QAAS,CAACoE,KAAK,CAAC,CAAC4f,MAAM,EAAEnjB,CAAC,KAAA;AAAA,IAAA,IAAAojB,qBAAA,CAAA;AAAA,IAAA,OAAA,CAAAA,qBAAA,GACxCH,aAAa,CAAC9jB,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAtBikB,qBAAA,CAAwBngB,IAAI,CAAEogB,MAAM,IAAKH,WAAW,CAACC,MAAM,EAAEE,MAAM,CAAC,CAAC,CAAA;GACtE,CAAA,CAAA;AACH,CAAA;AAEA;;;;AAIG;AACH,eAAeC,mBAAmBA,CAChC7kB,KAA8B,EAC9BG,kBAA8C,EAC9CE,QAAuB,EAAA;AAEvB,EAAA,IAAI,CAACL,KAAK,CAAC4Q,IAAI,EAAE;AACf,IAAA,OAAA;AACD,GAAA;AAED,EAAA,IAAIkU,SAAS,GAAG,MAAM9kB,KAAK,CAAC4Q,IAAI,EAAE,CAAA;AAElC;AACA;AACA;AACA,EAAA,IAAI,CAAC5Q,KAAK,CAAC4Q,IAAI,EAAE;AACf,IAAA,OAAA;AACD,GAAA;AAED,EAAA,IAAImU,aAAa,GAAG1kB,QAAQ,CAACL,KAAK,CAACQ,EAAE,CAAC,CAAA;AACtC7C,EAAAA,SAAS,CAAConB,aAAa,EAAE,4BAA4B,CAAC,CAAA;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIC,YAAY,GAAwB,EAAE,CAAA;AAC1C,EAAA,KAAK,IAAIC,iBAAiB,IAAIH,SAAS,EAAE;AACvC,IAAA,IAAII,gBAAgB,GAClBH,aAAa,CAACE,iBAA+C,CAAC,CAAA;AAEhE,IAAA,IAAIE,2BAA2B,GAC7BD,gBAAgB,KAAKtrB,SAAS;AAC9B;AACA;AACAqrB,IAAAA,iBAAiB,KAAK,kBAAkB,CAAA;AAE1CrqB,IAAAA,OAAO,CACL,CAACuqB,2BAA2B,EAC5B,aAAUJ,aAAa,CAACvkB,EAAE,GAAA,6BAAA,GAA4BykB,iBAAiB,GAAA,KAAA,GAAA,6EACQ,IACjDA,4BAAAA,GAAAA,iBAAiB,yBAAoB,CACpE,CAAA;IAED,IACE,CAACE,2BAA2B,IAC5B,CAACtlB,kBAAkB,CAACyJ,GAAG,CAAC2b,iBAAsC,CAAC,EAC/D;AACAD,MAAAA,YAAY,CAACC,iBAAiB,CAAC,GAC7BH,SAAS,CAACG,iBAA2C,CAAC,CAAA;AACzD,KAAA;AACF,GAAA;AAED;AACA;AACA5f,EAAAA,MAAM,CAAC7F,MAAM,CAACulB,aAAa,EAAEC,YAAY,CAAC,CAAA;AAE1C;AACA;AACA;EACA3f,MAAM,CAAC7F,MAAM,CAACulB,aAAa,EAAAtmB,QAAA,CAKtB0B,EAAAA,EAAAA,kBAAkB,CAAC4kB,aAAa,CAAC,EAAA;AACpCnU,IAAAA,IAAI,EAAEhX,SAAAA;AAAS,GAAA,CAChB,CAAC,CAAA;AACJ,CAAA;AAEA;AACA,eAAewV,mBAAmBA,CAAAgW,KAAA,EAEP;EAAA,IAFQ;AACjC9jB,IAAAA,OAAAA;AACyB,GAAA,GAAA8jB,KAAA,CAAA;EACzB,IAAIzM,aAAa,GAAGrX,OAAO,CAACmD,MAAM,CAAEkM,CAAC,IAAKA,CAAC,CAAC0U,UAAU,CAAC,CAAA;AACvD,EAAA,IAAIrN,OAAO,GAAG,MAAM3N,OAAO,CAACgS,GAAG,CAAC1D,aAAa,CAACpf,GAAG,CAAEoX,CAAC,IAAKA,CAAC,CAACxE,OAAO,EAAE,CAAC,CAAC,CAAA;AACtE,EAAA,OAAO6L,OAAO,CAACtT,MAAM,CACnB,CAACkG,GAAG,EAAEnH,MAAM,EAAElC,CAAC,KACb8D,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAE;IAAE,CAAC+N,aAAa,CAACpX,CAAC,CAAC,CAACvB,KAAK,CAACQ,EAAE,GAAGiD,MAAAA;AAAM,GAAE,CAAC,EAC7D,EAAE,CACH,CAAA;AACH,CAAA;AAEA,eAAeoY,oBAAoBA,CACjC3M,gBAAsC,EACtCvF,IAAyB,EACzBhQ,KAAyB,EACzB8c,OAAgB,EAChBkC,aAAuC,EACvCrX,OAAiC,EACjCqa,UAAyB,EACzBtb,QAAuB,EACvBF,kBAA8C,EAC9C2e,cAAwB,EAAA;EAExB,IAAIwG,4BAA4B,GAAGhkB,OAAO,CAAC/H,GAAG,CAAEoX,CAAC,IAC/CA,CAAC,CAAC3Q,KAAK,CAAC4Q,IAAI,GACRiU,mBAAmB,CAAClU,CAAC,CAAC3Q,KAAK,EAAEG,kBAAkB,EAAEE,QAAQ,CAAC,GAC1DzG,SAAS,CACd,CAAA;EAED,IAAI2rB,SAAS,GAAGjkB,OAAO,CAAC/H,GAAG,CAAC,CAACqI,KAAK,EAAEL,CAAC,KAAI;AACvC,IAAA,IAAIikB,gBAAgB,GAAGF,4BAA4B,CAAC/jB,CAAC,CAAC,CAAA;AACtD,IAAA,IAAI8jB,UAAU,GAAG1M,aAAa,CAACnU,IAAI,CAAEmM,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAKoB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;AACzE;AACA;AACA;AACA;AACA,IAAA,IAAI2L,OAAO,GAAiC,MAAOsZ,eAAe,IAAI;MACpE,IACEA,eAAe,IACfhP,OAAO,CAACsB,MAAM,KAAK,KAAK,KACvBnW,KAAK,CAAC5B,KAAK,CAAC4Q,IAAI,IAAIhP,KAAK,CAAC5B,KAAK,CAAC6Q,MAAM,CAAC,EACxC;AACAwU,QAAAA,UAAU,GAAG,IAAI,CAAA;AAClB,OAAA;MACD,OAAOA,UAAU,GACbK,kBAAkB,CAChB/b,IAAI,EACJ8M,OAAO,EACP7U,KAAK,EACL4jB,gBAAgB,EAChBC,eAAe,EACf3G,cAAc,CACf,GACDzU,OAAO,CAAC8B,OAAO,CAAC;QAAExC,IAAI,EAAE/J,UAAU,CAACmC,IAAI;AAAE0B,QAAAA,MAAM,EAAE7J,SAAAA;AAAS,OAAE,CAAC,CAAA;KAClE,CAAA;IAED,OAAA6E,QAAA,KACKmD,KAAK,EAAA;MACRyjB,UAAU;AACVlZ,MAAAA,OAAAA;AAAO,KAAA,CAAA,CAAA;AAEX,GAAC,CAAC,CAAA;AAEF;AACA;AACA;AACA,EAAA,IAAI6L,OAAO,GAAG,MAAM9I,gBAAgB,CAAC;AACnC5N,IAAAA,OAAO,EAAEikB,SAAS;IAClB9O,OAAO;AACP3U,IAAAA,MAAM,EAAER,OAAO,CAAC,CAAC,CAAC,CAACQ,MAAM;IACzB6Z,UAAU;AACV2E,IAAAA,OAAO,EAAExB,cAAAA;AACV,GAAA,CAAC,CAAA;AAEF;AACA;AACA;EACA,IAAI;AACF,IAAA,MAAMzU,OAAO,CAACgS,GAAG,CAACiJ,4BAA4B,CAAC,CAAA;GAChD,CAAC,OAAOpnB,CAAC,EAAE;AACV;AAAA,GAAA;AAGF,EAAA,OAAO8Z,OAAO,CAAA;AAChB,CAAA;AAEA;AACA,eAAe0N,kBAAkBA,CAC/B/b,IAAyB,EACzB8M,OAAgB,EAChB7U,KAA6B,EAC7B4jB,gBAA2C,EAC3CC,eAA4D,EAC5DE,aAAuB,EAAA;AAEvB,EAAA,IAAIliB,MAA0B,CAAA;AAC9B,EAAA,IAAImiB,QAAkC,CAAA;EAEtC,IAAIC,UAAU,GACZC,OAAsE,IACvC;AAC/B;AACA,IAAA,IAAI3b,MAAkB,CAAA;AACtB;AACA;AACA,IAAA,IAAIC,YAAY,GAAG,IAAIC,OAAO,CAAqB,CAAC1D,CAAC,EAAE2D,CAAC,KAAMH,MAAM,GAAGG,CAAE,CAAC,CAAA;AAC1Esb,IAAAA,QAAQ,GAAGA,MAAMzb,MAAM,EAAE,CAAA;IACzBsM,OAAO,CAAC9L,MAAM,CAACjL,gBAAgB,CAAC,OAAO,EAAEkmB,QAAQ,CAAC,CAAA;IAElD,IAAIG,aAAa,GAAIC,GAAa,IAAI;AACpC,MAAA,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE;AACjC,QAAA,OAAOzb,OAAO,CAACF,MAAM,CACnB,IAAIrM,KAAK,CACP,kEAAA,IAAA,IAAA,GACM6L,IAAI,GAAA,eAAA,GAAe/H,KAAK,CAAC5B,KAAK,CAACQ,EAAE,GAAA,GAAA,CAAG,CAC3C,CACF,CAAA;AACF,OAAA;AACD,MAAA,OAAOslB,OAAO,CACZ;QACErP,OAAO;QACP3U,MAAM,EAAEF,KAAK,CAACE,MAAM;AACpBwe,QAAAA,OAAO,EAAEqF,aAAAA;AACV,OAAA,EACD,IAAIK,GAAG,KAAKpsB,SAAS,GAAG,CAACosB,GAAG,CAAC,GAAG,EAAE,CAAC,CACpC,CAAA;KACF,CAAA;IAED,IAAIC,cAAc,GAAgC,CAAC,YAAW;MAC5D,IAAI;AACF,QAAA,IAAIC,GAAG,GAAG,OAAOT,eAAe,GAC5BA,eAAe,CAAEO,GAAY,IAAKD,aAAa,CAACC,GAAG,CAAC,CAAC,GACrDD,aAAa,EAAE,CAAC,CAAA;QACpB,OAAO;AAAEpc,UAAAA,IAAI,EAAE,MAAM;AAAElG,UAAAA,MAAM,EAAEyiB,GAAAA;SAAK,CAAA;OACrC,CAAC,OAAOhoB,CAAC,EAAE;QACV,OAAO;AAAEyL,UAAAA,IAAI,EAAE,OAAO;AAAElG,UAAAA,MAAM,EAAEvF,CAAAA;SAAG,CAAA;AACpC,OAAA;AACH,KAAC,GAAG,CAAA;IAEJ,OAAOmM,OAAO,CAACa,IAAI,CAAC,CAAC+a,cAAc,EAAE7b,YAAY,CAAC,CAAC,CAAA;GACpD,CAAA;EAED,IAAI;AACF,IAAA,IAAI0b,OAAO,GAAGlkB,KAAK,CAAC5B,KAAK,CAAC2J,IAAI,CAAC,CAAA;AAE/B;AACA,IAAA,IAAI6b,gBAAgB,EAAE;AACpB,MAAA,IAAIM,OAAO,EAAE;AACX;AACA,QAAA,IAAIK,YAAY,CAAA;QAChB,IAAI,CAACvoB,KAAK,CAAC,GAAG,MAAMyM,OAAO,CAACgS,GAAG,CAAC;AAC9B;AACA;AACA;AACAwJ,QAAAA,UAAU,CAACC,OAAO,CAAC,CAACza,KAAK,CAAEnN,CAAC,IAAI;AAC9BioB,UAAAA,YAAY,GAAGjoB,CAAC,CAAA;AAClB,SAAC,CAAC,EACFsnB,gBAAgB,CACjB,CAAC,CAAA;QACF,IAAIW,YAAY,KAAKvsB,SAAS,EAAE;AAC9B,UAAA,MAAMusB,YAAY,CAAA;AACnB,SAAA;AACD1iB,QAAAA,MAAM,GAAG7F,KAAM,CAAA;AAChB,OAAA,MAAM;AACL;AACA,QAAA,MAAM4nB,gBAAgB,CAAA;AAEtBM,QAAAA,OAAO,GAAGlkB,KAAK,CAAC5B,KAAK,CAAC2J,IAAI,CAAC,CAAA;AAC3B,QAAA,IAAImc,OAAO,EAAE;AACX;AACA;AACA;AACAriB,UAAAA,MAAM,GAAG,MAAMoiB,UAAU,CAACC,OAAO,CAAC,CAAA;AACnC,SAAA,MAAM,IAAInc,IAAI,KAAK,QAAQ,EAAE;UAC5B,IAAIrM,GAAG,GAAG,IAAIlC,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,CAAA;UAC9B,IAAI3C,QAAQ,GAAG2C,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,CAAA;UACxC,MAAM6U,sBAAsB,CAAC,GAAG,EAAE;YAChC0H,MAAM,EAAEtB,OAAO,CAACsB,MAAM;YACtBpd,QAAQ;AACRqc,YAAAA,OAAO,EAAEpV,KAAK,CAAC5B,KAAK,CAACQ,EAAAA;AACtB,WAAA,CAAC,CAAA;AACH,SAAA,MAAM;AACL;AACA;UACA,OAAO;YAAEmJ,IAAI,EAAE/J,UAAU,CAACmC,IAAI;AAAE0B,YAAAA,MAAM,EAAE7J,SAAAA;WAAW,CAAA;AACpD,SAAA;AACF,OAAA;AACF,KAAA,MAAM,IAAI,CAACksB,OAAO,EAAE;MACnB,IAAIxoB,GAAG,GAAG,IAAIlC,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,CAAA;MAC9B,IAAI3C,QAAQ,GAAG2C,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,CAAA;MACxC,MAAM6U,sBAAsB,CAAC,GAAG,EAAE;AAChC1V,QAAAA,QAAAA;AACD,OAAA,CAAC,CAAA;AACH,KAAA,MAAM;AACL8I,MAAAA,MAAM,GAAG,MAAMoiB,UAAU,CAACC,OAAO,CAAC,CAAA;AACnC,KAAA;IAEDnoB,SAAS,CACP8F,MAAM,CAACA,MAAM,KAAK7J,SAAS,EAC3B,cAAA,IAAe+P,IAAI,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAU,CACrD/H,GAAAA,aAAAA,IAAAA,IAAAA,GAAAA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,GAA4CmJ,2CAAAA,GAAAA,IAAI,GAAK,IAAA,CAAA,GAAA,4CACzB,CACjD,CAAA;GACF,CAAC,OAAOzL,CAAC,EAAE;AACV;AACA;AACA;IACA,OAAO;MAAEyL,IAAI,EAAE/J,UAAU,CAACP,KAAK;AAAEoE,MAAAA,MAAM,EAAEvF,CAAAA;KAAG,CAAA;AAC7C,GAAA,SAAS;AACR,IAAA,IAAI0nB,QAAQ,EAAE;MACZnP,OAAO,CAAC9L,MAAM,CAAChL,mBAAmB,CAAC,OAAO,EAAEimB,QAAQ,CAAC,CAAA;AACtD,KAAA;AACF,GAAA;AAED,EAAA,OAAOniB,MAAM,CAAA;AACf,CAAA;AAEA,eAAeuY,qCAAqCA,CAClDoK,kBAAsC,EAAA;EAEtC,IAAI;IAAE3iB,MAAM;AAAEkG,IAAAA,IAAAA;AAAM,GAAA,GAAGyc,kBAAkB,CAAA;AAEzC,EAAA,IAAI9G,UAAU,CAAC7b,MAAM,CAAC,EAAE;AACtB,IAAA,IAAI1B,IAAS,CAAA;IAEb,IAAI;MACF,IAAIskB,WAAW,GAAG5iB,MAAM,CAAC2F,OAAO,CAACmC,GAAG,CAAC,cAAc,CAAC,CAAA;AACpD;AACA;MACA,IAAI8a,WAAW,IAAI,uBAAuB,CAACzhB,IAAI,CAACyhB,WAAW,CAAC,EAAE;AAC5D,QAAA,IAAI5iB,MAAM,CAACud,IAAI,IAAI,IAAI,EAAE;AACvBjf,UAAAA,IAAI,GAAG,IAAI,CAAA;AACZ,SAAA,MAAM;AACLA,UAAAA,IAAI,GAAG,MAAM0B,MAAM,CAACuF,IAAI,EAAE,CAAA;AAC3B,SAAA;AACF,OAAA,MAAM;AACLjH,QAAAA,IAAI,GAAG,MAAM0B,MAAM,CAACuK,IAAI,EAAE,CAAA;AAC3B,OAAA;KACF,CAAC,OAAO9P,CAAC,EAAE;MACV,OAAO;QAAEyL,IAAI,EAAE/J,UAAU,CAACP,KAAK;AAAEA,QAAAA,KAAK,EAAEnB,CAAAA;OAAG,CAAA;AAC5C,KAAA;AAED,IAAA,IAAIyL,IAAI,KAAK/J,UAAU,CAACP,KAAK,EAAE;MAC7B,OAAO;QACLsK,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAK,EAAE,IAAI4N,iBAAiB,CAACxJ,MAAM,CAAC0F,MAAM,EAAE1F,MAAM,CAACyJ,UAAU,EAAEnL,IAAI,CAAC;QACpEmd,UAAU,EAAEzb,MAAM,CAAC0F,MAAM;QACzBC,OAAO,EAAE3F,MAAM,CAAC2F,OAAAA;OACjB,CAAA;AACF,KAAA;IAED,OAAO;MACLO,IAAI,EAAE/J,UAAU,CAACmC,IAAI;MACrBA,IAAI;MACJmd,UAAU,EAAEzb,MAAM,CAAC0F,MAAM;MACzBC,OAAO,EAAE3F,MAAM,CAAC2F,OAAAA;KACjB,CAAA;AACF,GAAA;AAED,EAAA,IAAIO,IAAI,KAAK/J,UAAU,CAACP,KAAK,EAAE;AAC7B,IAAA,IAAIinB,sBAAsB,CAAC7iB,MAAM,CAAC,EAAE;AAAA,MAAA,IAAA8iB,aAAA,CAAA;AAClC,MAAA,IAAI9iB,MAAM,CAAC1B,IAAI,YAAYjE,KAAK,EAAE;AAAA,QAAA,IAAA0oB,YAAA,CAAA;QAChC,OAAO;UACL7c,IAAI,EAAE/J,UAAU,CAACP,KAAK;UACtBA,KAAK,EAAEoE,MAAM,CAAC1B,IAAI;UAClBmd,UAAU,EAAA,CAAAsH,YAAA,GAAE/iB,MAAM,CAACwF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAXud,YAAA,CAAard,MAAAA;SAC1B,CAAA;AACF,OAAA;AAED;MACA1F,MAAM,GAAG,IAAIwJ,iBAAiB,CAC5B,EAAAsZ,aAAA,GAAA9iB,MAAM,CAACwF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAXsd,aAAA,CAAapd,MAAM,KAAI,GAAG,EAC1BvP,SAAS,EACT6J,MAAM,CAAC1B,IAAI,CACZ,CAAA;AACF,KAAA;IACD,OAAO;MACL4H,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,MAAAA,KAAK,EAAEoE,MAAM;MACbyb,UAAU,EAAE9R,oBAAoB,CAAC3J,MAAM,CAAC,GAAGA,MAAM,CAAC0F,MAAM,GAAGvP,SAAAA;KAC5D,CAAA;AACF,GAAA;AAED,EAAA,IAAI6sB,cAAc,CAAChjB,MAAM,CAAC,EAAE;IAAA,IAAAijB,aAAA,EAAAC,aAAA,CAAA;IAC1B,OAAO;MACLhd,IAAI,EAAE/J,UAAU,CAACgnB,QAAQ;AACzBhN,MAAAA,YAAY,EAAEnW,MAAM;MACpByb,UAAU,EAAA,CAAAwH,aAAA,GAAEjjB,MAAM,CAACwF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAXyd,aAAA,CAAavd,MAAM;AAC/BC,MAAAA,OAAO,EAAE,CAAAud,CAAAA,aAAA,GAAAljB,MAAM,CAACwF,IAAI,KAAX0d,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAA,CAAavd,OAAO,KAAI,IAAIC,OAAO,CAAC5F,MAAM,CAACwF,IAAI,CAACG,OAAO,CAAA;KACjE,CAAA;AACF,GAAA;AAED,EAAA,IAAIkd,sBAAsB,CAAC7iB,MAAM,CAAC,EAAE;IAAA,IAAAojB,aAAA,EAAAC,aAAA,CAAA;IAClC,OAAO;MACLnd,IAAI,EAAE/J,UAAU,CAACmC,IAAI;MACrBA,IAAI,EAAE0B,MAAM,CAAC1B,IAAI;MACjBmd,UAAU,EAAA,CAAA2H,aAAA,GAAEpjB,MAAM,CAACwF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAX4d,aAAA,CAAa1d,MAAM;MAC/BC,OAAO,EAAE,CAAA0d,aAAA,GAAArjB,MAAM,CAACwF,IAAI,aAAX6d,aAAA,CAAa1d,OAAO,GACzB,IAAIC,OAAO,CAAC5F,MAAM,CAACwF,IAAI,CAACG,OAAO,CAAC,GAChCxP,SAAAA;KACL,CAAA;AACF,GAAA;EAED,OAAO;IAAE+P,IAAI,EAAE/J,UAAU,CAACmC,IAAI;AAAEA,IAAAA,IAAI,EAAE0B,MAAAA;GAAQ,CAAA;AAChD,CAAA;AAEA;AACA,SAASsY,wCAAwCA,CAC/C/O,QAAkB,EAClByJ,OAAgB,EAChBO,OAAe,EACf1V,OAAiC,EACjCP,QAAgB,EAChBiH,oBAA6B,EAAA;EAE7B,IAAIvN,QAAQ,GAAGuS,QAAQ,CAAC5D,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAC,CAAA;AAC/C5N,EAAAA,SAAS,CACPlD,QAAQ,EACR,4EAA4E,CAC7E,CAAA;AAED,EAAA,IAAI,CAAC4T,kBAAkB,CAACzJ,IAAI,CAACnK,QAAQ,CAAC,EAAE;IACtC,IAAIssB,cAAc,GAAGzlB,OAAO,CAAC7D,KAAK,CAChC,CAAC,EACD6D,OAAO,CAACyP,SAAS,CAAEJ,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAKwW,OAAO,CAAC,GAAG,CAAC,CACrD,CAAA;IACDvc,QAAQ,GAAG6a,WAAW,CACpB,IAAIla,GAAG,CAACqb,OAAO,CAACnZ,GAAG,CAAC,EACpBypB,cAAc,EACdhmB,QAAQ,EACR,IAAI,EACJtG,QAAQ,EACRuN,oBAAoB,CACrB,CAAA;IACDgF,QAAQ,CAAC5D,OAAO,CAACG,GAAG,CAAC,UAAU,EAAE9O,QAAQ,CAAC,CAAA;AAC3C,GAAA;AAED,EAAA,OAAOuS,QAAQ,CAAA;AACjB,CAAA;AAEA,SAASmL,yBAAyBA,CAChC1d,QAAgB,EAChB+nB,UAAe,EACfzhB,QAAgB,EAAA;AAEhB,EAAA,IAAIsN,kBAAkB,CAACzJ,IAAI,CAACnK,QAAQ,CAAC,EAAE;AACrC;IACA,IAAIusB,kBAAkB,GAAGvsB,QAAQ,CAAA;IACjC,IAAI6C,GAAG,GAAG0pB,kBAAkB,CAACjqB,UAAU,CAAC,IAAI,CAAC,GACzC,IAAI3B,GAAG,CAAConB,UAAU,CAACyE,QAAQ,GAAGD,kBAAkB,CAAC,GACjD,IAAI5rB,GAAG,CAAC4rB,kBAAkB,CAAC,CAAA;IAC/B,IAAIE,cAAc,GAAGhmB,aAAa,CAAC5D,GAAG,CAAC3C,QAAQ,EAAEoG,QAAQ,CAAC,IAAI,IAAI,CAAA;IAClE,IAAIzD,GAAG,CAACmC,MAAM,KAAK+iB,UAAU,CAAC/iB,MAAM,IAAIynB,cAAc,EAAE;MACtD,OAAO5pB,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,GAAG8B,GAAG,CAAC7B,IAAI,CAAA;AAC5C,KAAA;AACF,GAAA;AACD,EAAA,OAAOhB,QAAQ,CAAA;AACjB,CAAA;AAEA;AACA;AACA;AACA,SAASic,uBAAuBA,CAC9Bxb,OAAgB,EAChBT,QAA2B,EAC3BkQ,MAAmB,EACnB8K,UAAuB,EAAA;AAEvB,EAAA,IAAInY,GAAG,GAAGpC,OAAO,CAACC,SAAS,CAAC6mB,iBAAiB,CAACvnB,QAAQ,CAAC,CAAC,CAAC4D,QAAQ,EAAE,CAAA;AACnE,EAAA,IAAI4K,IAAI,GAAgB;AAAE0B,IAAAA,MAAAA;GAAQ,CAAA;EAElC,IAAI8K,UAAU,IAAIZ,gBAAgB,CAACY,UAAU,CAAC7H,UAAU,CAAC,EAAE;IACzD,IAAI;MAAEA,UAAU;AAAEE,MAAAA,WAAAA;AAAa,KAAA,GAAG2H,UAAU,CAAA;AAC5C;AACA;AACA;AACAxM,IAAAA,IAAI,CAAC8O,MAAM,GAAGnK,UAAU,CAACmU,WAAW,EAAE,CAAA;IAEtC,IAAIjU,WAAW,KAAK,kBAAkB,EAAE;AACtC7E,MAAAA,IAAI,CAACG,OAAO,GAAG,IAAIC,OAAO,CAAC;AAAE,QAAA,cAAc,EAAEyE,WAAAA;AAAa,OAAA,CAAC,CAAA;MAC3D7E,IAAI,CAAC+X,IAAI,GAAGlmB,IAAI,CAACC,SAAS,CAAC0a,UAAU,CAACzM,IAAI,CAAC,CAAA;AAC5C,KAAA,MAAM,IAAI8E,WAAW,KAAK,YAAY,EAAE;AACvC;AACA7E,MAAAA,IAAI,CAAC+X,IAAI,GAAGvL,UAAU,CAACzH,IAAI,CAAA;KAC5B,MAAM,IACLF,WAAW,KAAK,mCAAmC,IACnD2H,UAAU,CAAC1H,QAAQ,EACnB;AACA;MACA9E,IAAI,CAAC+X,IAAI,GAAGoB,6BAA6B,CAAC3M,UAAU,CAAC1H,QAAQ,CAAC,CAAA;AAC/D,KAAA,MAAM;AACL;AACA9E,MAAAA,IAAI,CAAC+X,IAAI,GAAGvL,UAAU,CAAC1H,QAAQ,CAAA;AAChC,KAAA;AACF,GAAA;AAED,EAAA,OAAO,IAAIsS,OAAO,CAAC/iB,GAAG,EAAE2L,IAAI,CAAC,CAAA;AAC/B,CAAA;AAEA,SAASmZ,6BAA6BA,CAACrU,QAAkB,EAAA;AACvD,EAAA,IAAIoU,YAAY,GAAG,IAAIb,eAAe,EAAE,CAAA;AAExC,EAAA,KAAK,IAAI,CAAC9mB,GAAG,EAAEoD,KAAK,CAAC,IAAImQ,QAAQ,CAACzU,OAAO,EAAE,EAAE;AAC3C;AACA6oB,IAAAA,YAAY,CAACV,MAAM,CAACjnB,GAAG,EAAE,OAAOoD,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC2B,IAAI,CAAC,CAAA;AACzE,GAAA;AAED,EAAA,OAAO4iB,YAAY,CAAA;AACrB,CAAA;AAEA,SAASE,6BAA6BA,CACpCF,YAA6B,EAAA;AAE7B,EAAA,IAAIpU,QAAQ,GAAG,IAAIkU,QAAQ,EAAE,CAAA;AAC7B,EAAA,KAAK,IAAI,CAACznB,GAAG,EAAEoD,KAAK,CAAC,IAAIukB,YAAY,CAAC7oB,OAAO,EAAE,EAAE;AAC/CyU,IAAAA,QAAQ,CAAC0T,MAAM,CAACjnB,GAAG,EAAEoD,KAAK,CAAC,CAAA;AAC5B,GAAA;AACD,EAAA,OAAOmQ,QAAQ,CAAA;AACjB,CAAA;AAEA,SAASyS,sBAAsBA,CAC7Blf,OAAiC,EACjC0W,OAAmC,EACnCrB,mBAAoD,EACpD7D,eAA0C,EAC1CiM,uBAAgC,EAAA;AAOhC;EACA,IAAIld,UAAU,GAA8B,EAAE,CAAA;EAC9C,IAAIiP,MAAM,GAAiC,IAAI,CAAA;AAC/C,EAAA,IAAIoO,UAA8B,CAAA;EAClC,IAAIiI,UAAU,GAAG,KAAK,CAAA;EACtB,IAAIhI,aAAa,GAA4B,EAAE,CAAA;AAC/C,EAAA,IAAIvJ,YAAY,GACde,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACxDA,mBAAmB,CAAC,CAAC,CAAC,CAACtX,KAAK,GAC5BzF,SAAS,CAAA;AAEf;AACA0H,EAAAA,OAAO,CAACsB,OAAO,CAAEhB,KAAK,IAAI;IACxB,IAAI,EAAEA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,IAAIwX,OAAO,CAAC,EAAE;AAChC,MAAA,OAAA;AACD,KAAA;AACD,IAAA,IAAIxX,EAAE,GAAGoB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAA;AACvB,IAAA,IAAIiD,MAAM,GAAGuU,OAAO,CAACxX,EAAE,CAAC,CAAA;IACxB7C,SAAS,CACP,CAACua,gBAAgB,CAACzU,MAAM,CAAC,EACzB,qDAAqD,CACtD,CAAA;AACD,IAAA,IAAIwT,aAAa,CAACxT,MAAM,CAAC,EAAE;AACzB,MAAA,IAAIpE,KAAK,GAAGoE,MAAM,CAACpE,KAAK,CAAA;AACxB;AACA;AACA;MACA,IAAIuW,YAAY,KAAKhc,SAAS,EAAE;AAC9ByF,QAAAA,KAAK,GAAGuW,YAAY,CAAA;AACpBA,QAAAA,YAAY,GAAGhc,SAAS,CAAA;AACzB,OAAA;AAEDkX,MAAAA,MAAM,GAAGA,MAAM,IAAI,EAAE,CAAA;AAErB,MAAA,IAAIiO,uBAAuB,EAAE;AAC3BjO,QAAAA,MAAM,CAACtQ,EAAE,CAAC,GAAGnB,KAAK,CAAA;AACnB,OAAA,MAAM;AACL;AACA;AACA;AACA,QAAA,IAAIiZ,aAAa,GAAG1B,mBAAmB,CAACtV,OAAO,EAAEd,EAAE,CAAC,CAAA;QACpD,IAAIsQ,MAAM,CAACwH,aAAa,CAACtY,KAAK,CAACQ,EAAE,CAAC,IAAI,IAAI,EAAE;UAC1CsQ,MAAM,CAACwH,aAAa,CAACtY,KAAK,CAACQ,EAAE,CAAC,GAAGnB,KAAK,CAAA;AACvC,SAAA;AACF,OAAA;AAED;AACAwC,MAAAA,UAAU,CAACrB,EAAE,CAAC,GAAG5G,SAAS,CAAA;AAE1B;AACA;MACA,IAAI,CAACutB,UAAU,EAAE;AACfA,QAAAA,UAAU,GAAG,IAAI,CAAA;AACjBjI,QAAAA,UAAU,GAAG9R,oBAAoB,CAAC3J,MAAM,CAACpE,KAAK,CAAC,GAC3CoE,MAAM,CAACpE,KAAK,CAAC8J,MAAM,GACnB,GAAG,CAAA;AACR,OAAA;MACD,IAAI1F,MAAM,CAAC2F,OAAO,EAAE;AAClB+V,QAAAA,aAAa,CAAC3e,EAAE,CAAC,GAAGiD,MAAM,CAAC2F,OAAO,CAAA;AACnC,OAAA;AACF,KAAA,MAAM;AACL,MAAA,IAAIiP,gBAAgB,CAAC5U,MAAM,CAAC,EAAE;QAC5BqP,eAAe,CAACvJ,GAAG,CAAC/I,EAAE,EAAEiD,MAAM,CAACmW,YAAY,CAAC,CAAA;QAC5C/X,UAAU,CAACrB,EAAE,CAAC,GAAGiD,MAAM,CAACmW,YAAY,CAAC7X,IAAI,CAAA;AACzC;AACA;AACA,QAAA,IACE0B,MAAM,CAACyb,UAAU,IAAI,IAAI,IACzBzb,MAAM,CAACyb,UAAU,KAAK,GAAG,IACzB,CAACiI,UAAU,EACX;UACAjI,UAAU,GAAGzb,MAAM,CAACyb,UAAU,CAAA;AAC/B,SAAA;QACD,IAAIzb,MAAM,CAAC2F,OAAO,EAAE;AAClB+V,UAAAA,aAAa,CAAC3e,EAAE,CAAC,GAAGiD,MAAM,CAAC2F,OAAO,CAAA;AACnC,SAAA;AACF,OAAA,MAAM;AACLvH,QAAAA,UAAU,CAACrB,EAAE,CAAC,GAAGiD,MAAM,CAAC1B,IAAI,CAAA;AAC5B;AACA;AACA,QAAA,IAAI0B,MAAM,CAACyb,UAAU,IAAIzb,MAAM,CAACyb,UAAU,KAAK,GAAG,IAAI,CAACiI,UAAU,EAAE;UACjEjI,UAAU,GAAGzb,MAAM,CAACyb,UAAU,CAAA;AAC/B,SAAA;QACD,IAAIzb,MAAM,CAAC2F,OAAO,EAAE;AAClB+V,UAAAA,aAAa,CAAC3e,EAAE,CAAC,GAAGiD,MAAM,CAAC2F,OAAO,CAAA;AACnC,SAAA;AACF,OAAA;AACF,KAAA;AACH,GAAC,CAAC,CAAA;AAEF;AACA;AACA;AACA,EAAA,IAAIwM,YAAY,KAAKhc,SAAS,IAAI+c,mBAAmB,EAAE;AACrD7F,IAAAA,MAAM,GAAG;AAAE,MAAA,CAAC6F,mBAAmB,CAAC,CAAC,CAAC,GAAGf,YAAAA;KAAc,CAAA;AACnD/T,IAAAA,UAAU,CAAC8U,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAAG/c,SAAS,CAAA;AAC/C,GAAA;EAED,OAAO;IACLiI,UAAU;IACViP,MAAM;IACNoO,UAAU,EAAEA,UAAU,IAAI,GAAG;AAC7BC,IAAAA,aAAAA;GACD,CAAA;AACH,CAAA;AAEA,SAASxF,iBAAiBA,CACxBhgB,KAAkB,EAClB2H,OAAiC,EACjC0W,OAAmC,EACnCrB,mBAAoD,EACpDiC,oBAA2C,EAC3CY,cAA0C,EAC1C1G,eAA0C,EAAA;EAK1C,IAAI;IAAEjR,UAAU;AAAEiP,IAAAA,MAAAA;AAAQ,GAAA,GAAG0P,sBAAsB,CACjDlf,OAAO,EACP0W,OAAO,EACPrB,mBAAmB,EACnB7D,eAAe,EACf,KAAK;GACN,CAAA;AAED;AACA8F,EAAAA,oBAAoB,CAAChW,OAAO,CAAEuW,EAAE,IAAI;IAClC,IAAI;MAAE3e,GAAG;MAAEoH,KAAK;AAAE2I,MAAAA,UAAAA;AAAU,KAAE,GAAG4O,EAAE,CAAA;AACnC,IAAA,IAAI1V,MAAM,GAAG+V,cAAc,CAAChf,GAAG,CAAC,CAAA;AAChCmD,IAAAA,SAAS,CAAC8F,MAAM,EAAE,2CAA2C,CAAC,CAAA;AAE9D;AACA,IAAA,IAAI8G,UAAU,IAAIA,UAAU,CAACI,MAAM,CAACa,OAAO,EAAE;AAC3C;AACA,MAAA,OAAA;AACD,KAAA,MAAM,IAAIyL,aAAa,CAACxT,MAAM,CAAC,EAAE;AAChC,MAAA,IAAI6U,aAAa,GAAG1B,mBAAmB,CAACjd,KAAK,CAAC2H,OAAO,EAAEM,KAAK,oBAALA,KAAK,CAAE5B,KAAK,CAACQ,EAAE,CAAC,CAAA;AACvE,MAAA,IAAI,EAAEsQ,MAAM,IAAIA,MAAM,CAACwH,aAAa,CAACtY,KAAK,CAACQ,EAAE,CAAC,CAAC,EAAE;QAC/CsQ,MAAM,GAAArS,QAAA,CAAA,EAAA,EACDqS,MAAM,EAAA;AACT,UAAA,CAACwH,aAAa,CAACtY,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAACpE,KAAAA;SAClC,CAAA,CAAA;AACF,OAAA;AACD1F,MAAAA,KAAK,CAAC6X,QAAQ,CAAC/F,MAAM,CAACjR,GAAG,CAAC,CAAA;AAC3B,KAAA,MAAM,IAAI0d,gBAAgB,CAACzU,MAAM,CAAC,EAAE;AACnC;AACA;AACA9F,MAAAA,SAAS,CAAC,KAAK,EAAE,yCAAyC,CAAC,CAAA;AAC5D,KAAA,MAAM,IAAI0a,gBAAgB,CAAC5U,MAAM,CAAC,EAAE;AACnC;AACA;AACA9F,MAAAA,SAAS,CAAC,KAAK,EAAE,iCAAiC,CAAC,CAAA;AACpD,KAAA,MAAM;AACL,MAAA,IAAIyd,WAAW,GAAGL,cAAc,CAACtX,MAAM,CAAC1B,IAAI,CAAC,CAAA;MAC7CpI,KAAK,CAAC6X,QAAQ,CAACjI,GAAG,CAAC/O,GAAG,EAAE4gB,WAAW,CAAC,CAAA;AACrC,KAAA;AACH,GAAC,CAAC,CAAA;EAEF,OAAO;IAAEvZ,UAAU;AAAEiP,IAAAA,MAAAA;GAAQ,CAAA;AAC/B,CAAA;AAEA,SAASkE,eAAeA,CACtBnT,UAAqB,EACrBulB,aAAwB,EACxB9lB,OAAiC,EACjCwP,MAAoC,EAAA;AAEpC,EAAA,IAAIuW,gBAAgB,GAAA5oB,QAAA,CAAA,EAAA,EAAQ2oB,aAAa,CAAE,CAAA;AAC3C,EAAA,KAAK,IAAIxlB,KAAK,IAAIN,OAAO,EAAE;AACzB,IAAA,IAAId,EAAE,GAAGoB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAA;AACvB,IAAA,IAAI4mB,aAAa,CAACE,cAAc,CAAC9mB,EAAE,CAAC,EAAE;AACpC,MAAA,IAAI4mB,aAAa,CAAC5mB,EAAE,CAAC,KAAK5G,SAAS,EAAE;AACnCytB,QAAAA,gBAAgB,CAAC7mB,EAAE,CAAC,GAAG4mB,aAAa,CAAC5mB,EAAE,CAAC,CAAA;AACzC,OAGC;AAEH,KAAA,MAAM,IAAIqB,UAAU,CAACrB,EAAE,CAAC,KAAK5G,SAAS,IAAIgI,KAAK,CAAC5B,KAAK,CAAC6Q,MAAM,EAAE;AAC7D;AACA;AACAwW,MAAAA,gBAAgB,CAAC7mB,EAAE,CAAC,GAAGqB,UAAU,CAACrB,EAAE,CAAC,CAAA;AACtC,KAAA;IAED,IAAIsQ,MAAM,IAAIA,MAAM,CAACwW,cAAc,CAAC9mB,EAAE,CAAC,EAAE;AACvC;AACA,MAAA,MAAA;AACD,KAAA;AACF,GAAA;AACD,EAAA,OAAO6mB,gBAAgB,CAAA;AACzB,CAAA;AAEA,SAAS/P,sBAAsBA,CAC7BX,mBAAoD,EAAA;EAEpD,IAAI,CAACA,mBAAmB,EAAE;AACxB,IAAA,OAAO,EAAE,CAAA;AACV,GAAA;AACD,EAAA,OAAOM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACxC;AACE;AACApF,IAAAA,UAAU,EAAE,EAAE;AACf,GAAA,GACD;AACEA,IAAAA,UAAU,EAAE;MACV,CAACoF,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,CAAC5U,IAAAA;AAClD,KAAA;GACF,CAAA;AACP,CAAA;AAEA;AACA;AACA;AACA,SAAS6U,mBAAmBA,CAC1BtV,OAAiC,EACjC0V,OAAgB,EAAA;AAEhB,EAAA,IAAIuQ,eAAe,GAAGvQ,OAAO,GACzB1V,OAAO,CAAC7D,KAAK,CAAC,CAAC,EAAE6D,OAAO,CAACyP,SAAS,CAAEJ,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAKwW,OAAO,CAAC,GAAG,CAAC,CAAC,GACtE,CAAC,GAAG1V,OAAO,CAAC,CAAA;EAChB,OACEimB,eAAe,CAACC,OAAO,EAAE,CAAC/H,IAAI,CAAE9O,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACuO,gBAAgB,KAAK,IAAI,CAAC,IACxEjN,OAAO,CAAC,CAAC,CAAC,CAAA;AAEd,CAAA;AAEA,SAASgP,sBAAsBA,CAACpQ,MAAiC,EAAA;AAI/D;AACA,EAAA,IAAIF,KAAK,GACPE,MAAM,CAACpG,MAAM,KAAK,CAAC,GACfoG,MAAM,CAAC,CAAC,CAAC,GACTA,MAAM,CAACuf,IAAI,CAAEnV,CAAC,IAAKA,CAAC,CAAC7Q,KAAK,IAAI,CAAC6Q,CAAC,CAAChP,IAAI,IAAIgP,CAAC,CAAChP,IAAI,KAAK,GAAG,CAAC,IAAI;IAC1DkF,EAAE,EAAA,sBAAA;GACH,CAAA;EAEP,OAAO;AACLc,IAAAA,OAAO,EAAE,CACP;MACEQ,MAAM,EAAE,EAAE;AACVnH,MAAAA,QAAQ,EAAE,EAAE;AACZ2K,MAAAA,YAAY,EAAE,EAAE;AAChBtF,MAAAA,KAAAA;AACD,KAAA,CACF;AACDA,IAAAA,KAAAA;GACD,CAAA;AACH,CAAA;AAEA,SAASqQ,sBAAsBA,CAC7BlH,MAAc,EAAAse,MAAA,EAaR;EAAA,IAZN;IACE9sB,QAAQ;IACRqc,OAAO;IACPe,MAAM;IACNpO,IAAI;AACJ9L,IAAAA,OAAAA;0BAOE,EAAE,GAAA4pB,MAAA,CAAA;EAEN,IAAIva,UAAU,GAAG,sBAAsB,CAAA;EACvC,IAAIwa,YAAY,GAAG,iCAAiC,CAAA;EAEpD,IAAIve,MAAM,KAAK,GAAG,EAAE;AAClB+D,IAAAA,UAAU,GAAG,aAAa,CAAA;AAC1B,IAAA,IAAI6K,MAAM,IAAIpd,QAAQ,IAAIqc,OAAO,EAAE;MACjC0Q,YAAY,GACV,gBAAc3P,MAAM,GAAA,gBAAA,GAAgBpd,QAAQ,GACDqc,SAAAA,IAAAA,yCAAAA,GAAAA,OAAO,UAAK,GACZ,2CAAA,CAAA;AAC9C,KAAA,MAAM,IAAIrN,IAAI,KAAK,cAAc,EAAE;AAClC+d,MAAAA,YAAY,GAAG,qCAAqC,CAAA;AACrD,KAAA,MAAM,IAAI/d,IAAI,KAAK,cAAc,EAAE;AAClC+d,MAAAA,YAAY,GAAG,kCAAkC,CAAA;AAClD,KAAA;AACF,GAAA,MAAM,IAAIve,MAAM,KAAK,GAAG,EAAE;AACzB+D,IAAAA,UAAU,GAAG,WAAW,CAAA;AACxBwa,IAAAA,YAAY,GAAa1Q,UAAAA,GAAAA,OAAO,GAAyBrc,0BAAAA,GAAAA,QAAQ,GAAG,IAAA,CAAA;AACrE,GAAA,MAAM,IAAIwO,MAAM,KAAK,GAAG,EAAE;AACzB+D,IAAAA,UAAU,GAAG,WAAW,CAAA;IACxBwa,YAAY,GAAA,yBAAA,GAA4B/sB,QAAQ,GAAG,IAAA,CAAA;AACpD,GAAA,MAAM,IAAIwO,MAAM,KAAK,GAAG,EAAE;AACzB+D,IAAAA,UAAU,GAAG,oBAAoB,CAAA;AACjC,IAAA,IAAI6K,MAAM,IAAIpd,QAAQ,IAAIqc,OAAO,EAAE;AACjC0Q,MAAAA,YAAY,GACV,aAAA,GAAc3P,MAAM,CAACgK,WAAW,EAAE,GAAA,gBAAA,GAAgBpnB,QAAQ,GAAA,SAAA,IAAA,0CAAA,GACdqc,OAAO,GAAA,MAAA,CAAK,GACb,2CAAA,CAAA;KAC9C,MAAM,IAAIe,MAAM,EAAE;AACjB2P,MAAAA,YAAY,iCAA8B3P,MAAM,CAACgK,WAAW,EAAE,GAAG,IAAA,CAAA;AAClE,KAAA;AACF,GAAA;AAED,EAAA,OAAO,IAAI9U,iBAAiB,CAC1B9D,MAAM,IAAI,GAAG,EACb+D,UAAU,EACV,IAAIpP,KAAK,CAAC4pB,YAAY,CAAC,EACvB,IAAI,CACL,CAAA;AACH,CAAA;AAEA;AACA,SAAShO,YAAYA,CACnB1B,OAAmC,EAAA;AAEnC,EAAA,IAAI1e,OAAO,GAAG+L,MAAM,CAAC/L,OAAO,CAAC0e,OAAO,CAAC,CAAA;AACrC,EAAA,KAAK,IAAIzW,CAAC,GAAGjI,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAEyH,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC5C,IAAI,CAAC/G,GAAG,EAAEiJ,MAAM,CAAC,GAAGnK,OAAO,CAACiI,CAAC,CAAC,CAAA;AAC9B,IAAA,IAAI2W,gBAAgB,CAACzU,MAAM,CAAC,EAAE;MAC5B,OAAO;QAAEjJ,GAAG;AAAEiJ,QAAAA,MAAAA;OAAQ,CAAA;AACvB,KAAA;AACF,GAAA;AACH,CAAA;AAEA,SAASue,iBAAiBA,CAAC1mB,IAAQ,EAAA;AACjC,EAAA,IAAIqD,UAAU,GAAG,OAAOrD,IAAI,KAAK,QAAQ,GAAGC,SAAS,CAACD,IAAI,CAAC,GAAGA,IAAI,CAAA;AAClE,EAAA,OAAOL,UAAU,CAAAwD,QAAA,CAAA,EAAA,EAAME,UAAU,EAAA;AAAElD,IAAAA,IAAI,EAAE,EAAA;AAAE,GAAA,CAAE,CAAC,CAAA;AAChD,CAAA;AAEA,SAAS+a,gBAAgBA,CAAC5S,CAAW,EAAEC,CAAW,EAAA;AAChD,EAAA,IAAID,CAAC,CAACjJ,QAAQ,KAAKkJ,CAAC,CAAClJ,QAAQ,IAAIiJ,CAAC,CAACpI,MAAM,KAAKqI,CAAC,CAACrI,MAAM,EAAE;AACtD,IAAA,OAAO,KAAK,CAAA;AACb,GAAA;AAED,EAAA,IAAIoI,CAAC,CAACnI,IAAI,KAAK,EAAE,EAAE;AACjB;AACA,IAAA,OAAOoI,CAAC,CAACpI,IAAI,KAAK,EAAE,CAAA;GACrB,MAAM,IAAImI,CAAC,CAACnI,IAAI,KAAKoI,CAAC,CAACpI,IAAI,EAAE;AAC5B;AACA,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA,MAAM,IAAIoI,CAAC,CAACpI,IAAI,KAAK,EAAE,EAAE;AACxB;AACA,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED;AACA;AACA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAMA,SAASskB,oBAAoBA,CAACtc,MAAe,EAAA;AAC3C,EAAA,OACEA,MAAM,IAAI,IAAI,IACd,OAAOA,MAAM,KAAK,QAAQ,IAC1B,MAAM,IAAIA,MAAM,IAChB,QAAQ,IAAIA,MAAM,KACjBA,MAAM,CAACkG,IAAI,KAAK/J,UAAU,CAACmC,IAAI,IAAI0B,MAAM,CAACkG,IAAI,KAAK/J,UAAU,CAACP,KAAK,CAAC,CAAA;AAEzE,CAAA;AAEA,SAASyc,kCAAkCA,CAACrY,MAA0B,EAAA;AACpE,EAAA,OACE6b,UAAU,CAAC7b,MAAM,CAACA,MAAM,CAAC,IAAIgK,mBAAmB,CAACnE,GAAG,CAAC7F,MAAM,CAACA,MAAM,CAAC0F,MAAM,CAAC,CAAA;AAE9E,CAAA;AAEA,SAASkP,gBAAgBA,CAAC5U,MAAkB,EAAA;AAC1C,EAAA,OAAOA,MAAM,CAACkG,IAAI,KAAK/J,UAAU,CAACgnB,QAAQ,CAAA;AAC5C,CAAA;AAEA,SAAS3P,aAAaA,CAACxT,MAAkB,EAAA;AACvC,EAAA,OAAOA,MAAM,CAACkG,IAAI,KAAK/J,UAAU,CAACP,KAAK,CAAA;AACzC,CAAA;AAEA,SAAS6Y,gBAAgBA,CAACzU,MAAmB,EAAA;EAC3C,OAAO,CAACA,MAAM,IAAIA,MAAM,CAACkG,IAAI,MAAM/J,UAAU,CAACkN,QAAQ,CAAA;AACxD,CAAA;AAEM,SAAUwZ,sBAAsBA,CACpC1oB,KAAU,EAAA;EAEV,OACE,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,IAAI,IAAI,IACb,MAAM,IAAIA,KAAK,IACf,MAAM,IAAIA,KAAK,IACf,MAAM,IAAIA,KAAK,IACfA,KAAK,CAAC+L,IAAI,KAAK,sBAAsB,CAAA;AAEzC,CAAA;AAEM,SAAU8c,cAAcA,CAAC7oB,KAAU,EAAA;EACvC,IAAIgpB,QAAQ,GAAiBhpB,KAAK,CAAA;AAClC,EAAA,OACEgpB,QAAQ,IACR,OAAOA,QAAQ,KAAK,QAAQ,IAC5B,OAAOA,QAAQ,CAAC7kB,IAAI,KAAK,QAAQ,IACjC,OAAO6kB,QAAQ,CAAC9a,SAAS,KAAK,UAAU,IACxC,OAAO8a,QAAQ,CAAC7a,MAAM,KAAK,UAAU,IACrC,OAAO6a,QAAQ,CAAC1a,WAAW,KAAK,UAAU,CAAA;AAE9C,CAAA;AAEA,SAASoT,UAAUA,CAAC1hB,KAAU,EAAA;AAC5B,EAAA,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAACuL,MAAM,KAAK,QAAQ,IAChC,OAAOvL,KAAK,CAACsP,UAAU,KAAK,QAAQ,IACpC,OAAOtP,KAAK,CAACwL,OAAO,KAAK,QAAQ,IACjC,OAAOxL,KAAK,CAACojB,IAAI,KAAK,WAAW,CAAA;AAErC,CAAA;AAEA,SAAShB,kBAAkBA,CAACvc,MAAW,EAAA;AACrC,EAAA,IAAI,CAAC6b,UAAU,CAAC7b,MAAM,CAAC,EAAE;AACvB,IAAA,OAAO,KAAK,CAAA;AACb,GAAA;AAED,EAAA,IAAI0F,MAAM,GAAG1F,MAAM,CAAC0F,MAAM,CAAA;EAC1B,IAAI1O,QAAQ,GAAGgJ,MAAM,CAAC2F,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAC,CAAA;EAC7C,OAAOpC,MAAM,IAAI,GAAG,IAAIA,MAAM,IAAI,GAAG,IAAI1O,QAAQ,IAAI,IAAI,CAAA;AAC3D,CAAA;AAEA,SAASukB,aAAaA,CAACjH,MAAc,EAAA;EACnC,OAAOvK,mBAAmB,CAAClE,GAAG,CAACyO,MAAM,CAAChR,WAAW,EAAgB,CAAC,CAAA;AACpE,CAAA;AAEA,SAAS8N,gBAAgBA,CACvBkD,MAAc,EAAA;EAEd,OAAOzK,oBAAoB,CAAChE,GAAG,CAACyO,MAAM,CAAChR,WAAW,EAAwB,CAAC,CAAA;AAC7E,CAAA;AAEA,eAAeuV,gCAAgCA,CAC7Chb,OAA0C,EAC1C0W,OAAmC,EACnCrN,MAAmB,EACnBuR,cAAwC,EACxC0H,iBAA4B,EAAA;AAE5B,EAAA,IAAItqB,OAAO,GAAG+L,MAAM,CAAC/L,OAAO,CAAC0e,OAAO,CAAC,CAAA;AACrC,EAAA,KAAK,IAAIve,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,OAAO,CAACQ,MAAM,EAAEL,KAAK,EAAE,EAAE;IACnD,IAAI,CAACud,OAAO,EAAEvT,MAAM,CAAC,GAAGnK,OAAO,CAACG,KAAK,CAAC,CAAA;AACtC,IAAA,IAAImI,KAAK,GAAGN,OAAO,CAACme,IAAI,CAAE9O,CAAC,IAAK,CAAAA,CAAC,IAAA,IAAA,GAAA,KAAA,CAAA,GAADA,CAAC,CAAE3Q,KAAK,CAACQ,EAAE,MAAKwW,OAAO,CAAC,CAAA;AACxD;AACA;AACA;IACA,IAAI,CAACpV,KAAK,EAAE;AACV,MAAA,SAAA;AACD,KAAA;AAED,IAAA,IAAIiiB,YAAY,GAAG3H,cAAc,CAACuD,IAAI,CACnC9O,CAAC,IAAKA,CAAC,CAAC3Q,KAAK,CAACQ,EAAE,KAAKoB,KAAM,CAAC5B,KAAK,CAACQ,EAAE,CACtC,CAAA;IACD,IAAImnB,oBAAoB,GACtB9D,YAAY,IAAI,IAAI,IACpB,CAACR,kBAAkB,CAACQ,YAAY,EAAEjiB,KAAK,CAAC,IACxC,CAACgiB,iBAAiB,IAAIA,iBAAiB,CAAChiB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,MAAM5G,SAAS,CAAA;AAExE,IAAA,IAAIye,gBAAgB,CAAC5U,MAAM,CAAC,IAAIkkB,oBAAoB,EAAE;AACpD;AACA;AACA;AACA,MAAA,MAAMtM,mBAAmB,CAAC5X,MAAM,EAAEkH,MAAM,EAAE,KAAK,CAAC,CAACQ,IAAI,CAAE1H,MAAM,IAAI;AAC/D,QAAA,IAAIA,MAAM,EAAE;AACVuU,UAAAA,OAAO,CAAChB,OAAO,CAAC,GAAGvT,MAAM,CAAA;AAC1B,SAAA;AACH,OAAC,CAAC,CAAA;AACH,KAAA;AACF,GAAA;AACH,CAAA;AAEA,eAAe8Y,6BAA6BA,CAC1Cjb,OAA0C,EAC1C0W,OAAmC,EACnCY,oBAA2C,EAAA;AAE3C,EAAA,KAAK,IAAInf,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGmf,oBAAoB,CAAC9e,MAAM,EAAEL,KAAK,EAAE,EAAE;IAChE,IAAI;MAAEe,GAAG;MAAEwc,OAAO;AAAEzM,MAAAA,UAAAA;AAAY,KAAA,GAAGqO,oBAAoB,CAACnf,KAAK,CAAC,CAAA;AAC9D,IAAA,IAAIgK,MAAM,GAAGuU,OAAO,CAACxd,GAAG,CAAC,CAAA;AACzB,IAAA,IAAIoH,KAAK,GAAGN,OAAO,CAACme,IAAI,CAAE9O,CAAC,IAAK,CAAAA,CAAC,IAAA,IAAA,GAAA,KAAA,CAAA,GAADA,CAAC,CAAE3Q,KAAK,CAACQ,EAAE,MAAKwW,OAAO,CAAC,CAAA;AACxD;AACA;AACA;IACA,IAAI,CAACpV,KAAK,EAAE;AACV,MAAA,SAAA;AACD,KAAA;AAED,IAAA,IAAIyW,gBAAgB,CAAC5U,MAAM,CAAC,EAAE;AAC5B;AACA;AACA;AACA9F,MAAAA,SAAS,CACP4M,UAAU,EACV,sEAAsE,CACvE,CAAA;AACD,MAAA,MAAM8Q,mBAAmB,CAAC5X,MAAM,EAAE8G,UAAU,CAACI,MAAM,EAAE,IAAI,CAAC,CAACQ,IAAI,CAC5D1H,MAAM,IAAI;AACT,QAAA,IAAIA,MAAM,EAAE;AACVuU,UAAAA,OAAO,CAACxd,GAAG,CAAC,GAAGiJ,MAAM,CAAA;AACtB,SAAA;AACH,OAAC,CACF,CAAA;AACF,KAAA;AACF,GAAA;AACH,CAAA;AAEA,eAAe4X,mBAAmBA,CAChC5X,MAAsB,EACtBkH,MAAmB,EACnBid,MAAM,EAAQ;AAAA,EAAA,IAAdA,MAAM,KAAA,KAAA,CAAA,EAAA;AAANA,IAAAA,MAAM,GAAG,KAAK,CAAA;AAAA,GAAA;EAEd,IAAIpc,OAAO,GAAG,MAAM/H,MAAM,CAACmW,YAAY,CAAC1N,WAAW,CAACvB,MAAM,CAAC,CAAA;AAC3D,EAAA,IAAIa,OAAO,EAAE;AACX,IAAA,OAAA;AACD,GAAA;AAED,EAAA,IAAIoc,MAAM,EAAE;IACV,IAAI;MACF,OAAO;QACLje,IAAI,EAAE/J,UAAU,CAACmC,IAAI;AACrBA,QAAAA,IAAI,EAAE0B,MAAM,CAACmW,YAAY,CAACvN,aAAAA;OAC3B,CAAA;KACF,CAAC,OAAOnO,CAAC,EAAE;AACV;MACA,OAAO;QACLyL,IAAI,EAAE/J,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAK,EAAEnB,CAAAA;OACR,CAAA;AACF,KAAA;AACF,GAAA;EAED,OAAO;IACLyL,IAAI,EAAE/J,UAAU,CAACmC,IAAI;AACrBA,IAAAA,IAAI,EAAE0B,MAAM,CAACmW,YAAY,CAAC7X,IAAAA;GAC3B,CAAA;AACH,CAAA;AAEA,SAASsf,kBAAkBA,CAAC7lB,MAAc,EAAA;AACxC,EAAA,OAAO,IAAI8lB,eAAe,CAAC9lB,MAAM,CAAC,CAACgmB,MAAM,CAAC,OAAO,CAAC,CAAChd,IAAI,CAAEqC,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC,CAAA;AAC1E,CAAA;AAEA,SAASiR,cAAcA,CACrBxW,OAAiC,EACjC7G,QAA2B,EAAA;AAE3B,EAAA,IAAIe,MAAM,GACR,OAAOf,QAAQ,KAAK,QAAQ,GAAGc,SAAS,CAACd,QAAQ,CAAC,CAACe,MAAM,GAAGf,QAAQ,CAACe,MAAM,CAAA;AAC7E,EAAA,IACE8F,OAAO,CAACA,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAACkG,KAAK,CAACvG,KAAK,IACvC4nB,kBAAkB,CAAC7lB,MAAM,IAAI,EAAE,CAAC,EAChC;AACA;AACA,IAAA,OAAO8F,OAAO,CAACA,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAAA;AACnC,GAAA;AACD;AACA;AACA,EAAA,IAAImO,WAAW,GAAGH,0BAA0B,CAACxG,OAAO,CAAC,CAAA;AACrD,EAAA,OAAO2G,WAAW,CAACA,WAAW,CAACnO,MAAM,GAAG,CAAC,CAAC,CAAA;AAC5C,CAAA;AAEA,SAAS0e,2BAA2BA,CAClCrH,UAAsB,EAAA;EAEtB,IAAI;IAAEvD,UAAU;IAAEC,UAAU;IAAEC,WAAW;IAAEE,IAAI;IAAED,QAAQ;AAAE/E,IAAAA,IAAAA;AAAM,GAAA,GAC/DmI,UAAU,CAAA;EACZ,IAAI,CAACvD,UAAU,IAAI,CAACC,UAAU,IAAI,CAACC,WAAW,EAAE;AAC9C,IAAA,OAAA;AACD,GAAA;EAED,IAAIE,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO;MACLJ,UAAU;MACVC,UAAU;MACVC,WAAW;AACXC,MAAAA,QAAQ,EAAEnU,SAAS;AACnBoP,MAAAA,IAAI,EAAEpP,SAAS;AACfoU,MAAAA,IAAAA;KACD,CAAA;AACF,GAAA,MAAM,IAAID,QAAQ,IAAI,IAAI,EAAE;IAC3B,OAAO;MACLH,UAAU;MACVC,UAAU;MACVC,WAAW;MACXC,QAAQ;AACR/E,MAAAA,IAAI,EAAEpP,SAAS;AACfoU,MAAAA,IAAI,EAAEpU,SAAAA;KACP,CAAA;AACF,GAAA,MAAM,IAAIoP,IAAI,KAAKpP,SAAS,EAAE;IAC7B,OAAO;MACLgU,UAAU;MACVC,UAAU;MACVC,WAAW;AACXC,MAAAA,QAAQ,EAAEnU,SAAS;MACnBoP,IAAI;AACJgF,MAAAA,IAAI,EAAEpU,SAAAA;KACP,CAAA;AACF,GAAA;AACH,CAAA;AAEA,SAASsd,oBAAoBA,CAC3Bzc,QAAkB,EAClBgb,UAAuB,EAAA;AAEvB,EAAA,IAAIA,UAAU,EAAE;AACd,IAAA,IAAItE,UAAU,GAAgC;AAC5CxX,MAAAA,KAAK,EAAE,SAAS;MAChBc,QAAQ;MACRmT,UAAU,EAAE6H,UAAU,CAAC7H,UAAU;MACjCC,UAAU,EAAE4H,UAAU,CAAC5H,UAAU;MACjCC,WAAW,EAAE2H,UAAU,CAAC3H,WAAW;MACnCC,QAAQ,EAAE0H,UAAU,CAAC1H,QAAQ;MAC7B/E,IAAI,EAAEyM,UAAU,CAACzM,IAAI;MACrBgF,IAAI,EAAEyH,UAAU,CAACzH,IAAAA;KAClB,CAAA;AACD,IAAA,OAAOmD,UAAU,CAAA;AAClB,GAAA,MAAM;AACL,IAAA,IAAIA,UAAU,GAAgC;AAC5CxX,MAAAA,KAAK,EAAE,SAAS;MAChBc,QAAQ;AACRmT,MAAAA,UAAU,EAAEhU,SAAS;AACrBiU,MAAAA,UAAU,EAAEjU,SAAS;AACrBkU,MAAAA,WAAW,EAAElU,SAAS;AACtBmU,MAAAA,QAAQ,EAAEnU,SAAS;AACnBoP,MAAAA,IAAI,EAAEpP,SAAS;AACfoU,MAAAA,IAAI,EAAEpU,SAAAA;KACP,CAAA;AACD,IAAA,OAAOuX,UAAU,CAAA;AAClB,GAAA;AACH,CAAA;AAEA,SAASqG,uBAAuBA,CAC9B/c,QAAkB,EAClBgb,UAAsB,EAAA;AAEtB,EAAA,IAAItE,UAAU,GAAmC;AAC/CxX,IAAAA,KAAK,EAAE,YAAY;IACnBc,QAAQ;IACRmT,UAAU,EAAE6H,UAAU,CAAC7H,UAAU;IACjCC,UAAU,EAAE4H,UAAU,CAAC5H,UAAU;IACjCC,WAAW,EAAE2H,UAAU,CAAC3H,WAAW;IACnCC,QAAQ,EAAE0H,UAAU,CAAC1H,QAAQ;IAC7B/E,IAAI,EAAEyM,UAAU,CAACzM,IAAI;IACrBgF,IAAI,EAAEyH,UAAU,CAACzH,IAAAA;GAClB,CAAA;AACD,EAAA,OAAOmD,UAAU,CAAA;AACnB,CAAA;AAEA,SAAS8I,iBAAiBA,CACxBxE,UAAuB,EACvB1T,IAAsB,EAAA;AAEtB,EAAA,IAAI0T,UAAU,EAAE;AACd,IAAA,IAAIpB,OAAO,GAA6B;AACtC1a,MAAAA,KAAK,EAAE,SAAS;MAChBiU,UAAU,EAAE6H,UAAU,CAAC7H,UAAU;MACjCC,UAAU,EAAE4H,UAAU,CAAC5H,UAAU;MACjCC,WAAW,EAAE2H,UAAU,CAAC3H,WAAW;MACnCC,QAAQ,EAAE0H,UAAU,CAAC1H,QAAQ;MAC7B/E,IAAI,EAAEyM,UAAU,CAACzM,IAAI;MACrBgF,IAAI,EAAEyH,UAAU,CAACzH,IAAI;AACrBjM,MAAAA,IAAAA;KACD,CAAA;AACD,IAAA,OAAOsS,OAAO,CAAA;AACf,GAAA,MAAM;AACL,IAAA,IAAIA,OAAO,GAA6B;AACtC1a,MAAAA,KAAK,EAAE,SAAS;AAChBiU,MAAAA,UAAU,EAAEhU,SAAS;AACrBiU,MAAAA,UAAU,EAAEjU,SAAS;AACrBkU,MAAAA,WAAW,EAAElU,SAAS;AACtBmU,MAAAA,QAAQ,EAAEnU,SAAS;AACnBoP,MAAAA,IAAI,EAAEpP,SAAS;AACfoU,MAAAA,IAAI,EAAEpU,SAAS;AACfmI,MAAAA,IAAAA;KACD,CAAA;AACD,IAAA,OAAOsS,OAAO,CAAA;AACf,GAAA;AACH,CAAA;AAEA,SAASqG,oBAAoBA,CAC3BjF,UAAsB,EACtB+E,eAAyB,EAAA;AAEzB,EAAA,IAAInG,OAAO,GAAgC;AACzC1a,IAAAA,KAAK,EAAE,YAAY;IACnBiU,UAAU,EAAE6H,UAAU,CAAC7H,UAAU;IACjCC,UAAU,EAAE4H,UAAU,CAAC5H,UAAU;IACjCC,WAAW,EAAE2H,UAAU,CAAC3H,WAAW;IACnCC,QAAQ,EAAE0H,UAAU,CAAC1H,QAAQ;IAC7B/E,IAAI,EAAEyM,UAAU,CAACzM,IAAI;IACrBgF,IAAI,EAAEyH,UAAU,CAACzH,IAAI;AACrBjM,IAAAA,IAAI,EAAEyY,eAAe,GAAGA,eAAe,CAACzY,IAAI,GAAGnI,SAAAA;GAChD,CAAA;AACD,EAAA,OAAOya,OAAO,CAAA;AAChB,CAAA;AAEA,SAAS0G,cAAcA,CAAChZ,IAAqB,EAAA;AAC3C,EAAA,IAAIsS,OAAO,GAA0B;AACnC1a,IAAAA,KAAK,EAAE,MAAM;AACbiU,IAAAA,UAAU,EAAEhU,SAAS;AACrBiU,IAAAA,UAAU,EAAEjU,SAAS;AACrBkU,IAAAA,WAAW,EAAElU,SAAS;AACtBmU,IAAAA,QAAQ,EAAEnU,SAAS;AACnBoP,IAAAA,IAAI,EAAEpP,SAAS;AACfoU,IAAAA,IAAI,EAAEpU,SAAS;AACfmI,IAAAA,IAAAA;GACD,CAAA;AACD,EAAA,OAAOsS,OAAO,CAAA;AAChB,CAAA;AAEA,SAASZ,yBAAyBA,CAChCoU,OAAe,EACfC,WAAqC,EAAA;EAErC,IAAI;IACF,IAAIC,gBAAgB,GAAGF,OAAO,CAACG,cAAc,CAACC,OAAO,CACnDxZ,uBAAuB,CACxB,CAAA;AACD,IAAA,IAAIsZ,gBAAgB,EAAE;AACpB,MAAA,IAAI/e,IAAI,GAAGlO,IAAI,CAAConB,KAAK,CAAC6F,gBAAgB,CAAC,CAAA;AACvC,MAAA,KAAK,IAAI,CAAC9b,CAAC,EAAEpF,CAAC,CAAC,IAAIxB,MAAM,CAAC/L,OAAO,CAAC0P,IAAI,IAAI,EAAE,CAAC,EAAE;QAC7C,IAAInC,CAAC,IAAIoD,KAAK,CAACC,OAAO,CAACrD,CAAC,CAAC,EAAE;AACzBihB,UAAAA,WAAW,CAACve,GAAG,CAAC0C,CAAC,EAAE,IAAInM,GAAG,CAAC+G,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;AACrC,SAAA;AACF,OAAA;AACF,KAAA;GACF,CAAC,OAAO3I,CAAC,EAAE;AACV;AAAA,GAAA;AAEJ,CAAA;AAEA,SAASyV,yBAAyBA,CAChCkU,OAAe,EACfC,WAAqC,EAAA;AAErC,EAAA,IAAIA,WAAW,CAAC1b,IAAI,GAAG,CAAC,EAAE;IACxB,IAAIpD,IAAI,GAA6B,EAAE,CAAA;IACvC,KAAK,IAAI,CAACiD,CAAC,EAAEpF,CAAC,CAAC,IAAIihB,WAAW,EAAE;AAC9B9e,MAAAA,IAAI,CAACiD,CAAC,CAAC,GAAG,CAAC,GAAGpF,CAAC,CAAC,CAAA;AACjB,KAAA;IACD,IAAI;AACFghB,MAAAA,OAAO,CAACG,cAAc,CAACE,OAAO,CAC5BzZ,uBAAuB,EACvB3T,IAAI,CAACC,SAAS,CAACiO,IAAI,CAAC,CACrB,CAAA;KACF,CAAC,OAAO3J,KAAK,EAAE;AACdzE,MAAAA,OAAO,CACL,KAAK,EACyDyE,6DAAAA,GAAAA,KAAK,OAAI,CACxE,CAAA;AACF,KAAA;AACF,GAAA;AACH,CAAA;AACA;;;;"}