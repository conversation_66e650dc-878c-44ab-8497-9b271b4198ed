{"version": 3, "file": "effect-ts.modern.mjs", "sources": ["../src/effect-ts.ts"], "sourcesContent": ["import { formatIssue } from '@effect/schema/ArrayFormatter';\nimport { decodeUnknown } from '@effect/schema/ParseResult';\nimport { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport * as Effect from 'effect/Effect';\nimport type { FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nexport const effectTsResolver: Resolver =\n  (schema, config = { errors: 'all', onExcessProperty: 'ignore' }) =>\n  (values, _, options) => {\n    return decodeUnknown(\n      schema,\n      config,\n    )(values).pipe(\n      Effect.catchAll((parseIssue) => Effect.flip(formatIssue(parseIssue))),\n      Effect.mapError((issues) => {\n        const errors = issues.reduce((acc, current) => {\n          const key = current.path.join('.');\n          acc[key] = { message: current.message, type: current._tag };\n          return acc;\n        }, {} as FieldErrors);\n\n        return toNestErrors(errors, options);\n      }),\n      Effect.tap(() =>\n        Effect.sync(\n          () =>\n            options.shouldUseNativeValidation &&\n            validateFieldsNatively({}, options),\n        ),\n      ),\n      Effect.match({\n        onFailure: (errors) => ({ errors, values: {} }),\n        onSuccess: (result) => ({ errors: {}, values: result }),\n      }),\n      Effect.runPromise,\n    );\n  };\n"], "names": ["effectTsResolver", "schema", "config", "errors", "onExcessProperty", "values", "_", "options", "decodeUnknown", "pipe", "Effect", "catchAll", "parseIssue", "flip", "formatIssue", "mapError", "issues", "reduce", "acc", "current", "path", "join", "message", "type", "_tag", "toNestErrors", "tap", "sync", "shouldUseNativeValidation", "validateFieldsNatively", "match", "onFailure", "onSuccess", "result", "runPromise"], "mappings": "sOAOa,MAAAA,EACXA,CAACC,EAAQC,EAAS,CAAEC,OAAQ,MAAOC,iBAAkB,YACrD,CAACC,EAAQC,EAAGC,IACHC,EACLP,EACAC,EAFKM,CAGLH,GAAQI,KACRC,EAAOC,SAAUC,GAAeF,EAAOG,KAAKC,EAAYF,KACxDF,EAAOK,SAAUC,IACf,MAAMb,EAASa,EAAOC,OAAO,CAACC,EAAKC,KAEjCD,EADYC,EAAQC,KAAKC,KAAK,MACnB,CAAEC,QAASH,EAAQG,QAASC,KAAMJ,EAAQK,MAC9CN,GACN,CAAiB,GAEpB,OAAOO,EAAatB,EAAQI,EAAO,GAErCG,EAAOgB,IAAI,IACThB,EAAOiB,KACL,IACEpB,EAAQqB,2BACRC,EAAuB,CAAE,EAAEtB,KAGjCG,EAAOoB,MAAM,CACXC,UAAY5B,IAAY,CAAEA,SAAQE,OAAQ,KAC1C2B,UAAYC,IAAM,CAAQ9B,OAAQ,GAAIE,OAAQ4B,MAEhDvB,EAAOwB"}