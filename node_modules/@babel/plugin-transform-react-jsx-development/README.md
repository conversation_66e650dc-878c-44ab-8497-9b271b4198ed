# @babel/plugin-transform-react-jsx-development

> Turn JSX into React function calls in development

See our website [@babel/plugin-transform-react-jsx-development](https://babeljs.io/docs/babel-plugin-transform-react-jsx-development) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-react-jsx-development
```

or using yarn:

```sh
yarn add @babel/plugin-transform-react-jsx-development --dev
```
