var q=function(J){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},q(J)},W=function(J,H){var T=Object.keys(J);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(J);H&&(Y=Y.filter(function(x){return Object.getOwnPropertyDescriptor(J,x).enumerable})),T.push.apply(T,Y)}return T},K=function(J){for(var H=1;H<arguments.length;H++){var T=arguments[H]!=null?arguments[H]:{};H%2?W(Object(T),!0).forEach(function(Y){C0(J,Y,T[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(T)):W(Object(T)).forEach(function(Y){Object.defineProperty(J,Y,Object.getOwnPropertyDescriptor(T,Y))})}return J},C0=function(J,H,T){if(H=E0(H),H in J)Object.defineProperty(J,H,{value:T,enumerable:!0,configurable:!0,writable:!0});else J[H]=T;return J},E0=function(J){var H=B0(J,"string");return q(H)=="symbol"?H:String(H)},B0=function(J,H){if(q(J)!="object"||!J)return J;var T=J[Symbol.toPrimitive];if(T!==void 0){var Y=T.call(J,H||"default");if(q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,T=function C(B,G){for(var E in G)H(B,E,{get:G[E],enumerable:!0,configurable:!0,set:function U(X){return G[E]=function(){return X}}})},Y={lessThanXSeconds:{standalone:{one:"\u0C38\u0C46\u0C15\u0C28\u0C41 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35",other:"{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35"},withPreposition:{one:"\u0C38\u0C46\u0C15\u0C28\u0C41",other:"{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"}},xSeconds:{standalone:{one:"\u0C12\u0C15 \u0C38\u0C46\u0C15\u0C28\u0C41",other:"{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"},withPreposition:{one:"\u0C12\u0C15 \u0C38\u0C46\u0C15\u0C28\u0C41",other:"{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"}},halfAMinute:{standalone:"\u0C05\u0C30 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",withPreposition:"\u0C05\u0C30 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02"},lessThanXMinutes:{standalone:{one:"\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35",other:"{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35"},withPreposition:{one:"\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",other:"{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32"}},xMinutes:{standalone:{one:"\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",other:"{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",other:"{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32"}},aboutXHours:{standalone:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C17\u0C02\u0C1F",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C17\u0C02\u0C1F\u0C32\u0C41"},withPreposition:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C17\u0C02\u0C1F",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C17\u0C02\u0C1F\u0C32"}},xHours:{standalone:{one:"\u0C12\u0C15 \u0C17\u0C02\u0C1F",other:"{{count}} \u0C17\u0C02\u0C1F\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C17\u0C02\u0C1F",other:"{{count}} \u0C17\u0C02\u0C1F\u0C32"}},xDays:{standalone:{one:"\u0C12\u0C15 \u0C30\u0C4B\u0C1C\u0C41",other:"{{count}} \u0C30\u0C4B\u0C1C\u0C41\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C30\u0C4B\u0C1C\u0C41",other:"{{count}} \u0C30\u0C4B\u0C1C\u0C41\u0C32"}},aboutXWeeks:{standalone:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C32"}},xWeeks:{standalone:{one:"\u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",other:"{{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",other:"{{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C32"}},aboutXMonths:{standalone:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C28\u0C46\u0C32",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C28\u0C46\u0C32\u0C32\u0C41"},withPreposition:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C28\u0C46\u0C32",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C28\u0C46\u0C32\u0C32"}},xMonths:{standalone:{one:"\u0C12\u0C15 \u0C28\u0C46\u0C32",other:"{{count}} \u0C28\u0C46\u0C32\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C28\u0C46\u0C32",other:"{{count}} \u0C28\u0C46\u0C32\u0C32"}},aboutXYears:{standalone:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"}},xYears:{standalone:{one:"\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"}},overXYears:{standalone:{one:"\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02 \u0C2A\u0C48\u0C17\u0C3E",other:"{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C15\u0C41 \u0C2A\u0C48\u0C17\u0C3E"},withPreposition:{one:"\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"}},almostXYears:{standalone:{one:"\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"},withPreposition:{one:"\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",other:"\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"}}},x=function C(B,G,E){var U,X=E!==null&&E!==void 0&&E.addSuffix?Y[B].withPreposition:Y[B].standalone;if(typeof X==="string")U=X;else if(G===1)U=X.one;else U=X.other.replace("{{count}}",String(G));if(E!==null&&E!==void 0&&E.addSuffix)if(E.comparison&&E.comparison>0)return U+"\u0C32\u0C4B";else return U+" \u0C15\u0C4D\u0C30\u0C3F\u0C24\u0C02";return U};function z(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=B.width?String(B.width):C.defaultWidth,E=C.formats[G]||C.formats[C.defaultWidth];return E}}var $={full:"d, MMMM y, EEEE",long:"d MMMM, y",medium:"d MMM, y",short:"dd-MM-yy"},M={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},S={full:"{{date}} {{time}}'\u0C15\u0C3F'",long:"{{date}} {{time}}'\u0C15\u0C3F'",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:z({formats:$,defaultWidth:"full"}),time:z({formats:M,defaultWidth:"full"}),dateTime:z({formats:S,defaultWidth:"full"})},L={lastWeek:"'\u0C17\u0C24' eeee p",yesterday:"'\u0C28\u0C3F\u0C28\u0C4D\u0C28' p",today:"'\u0C08 \u0C30\u0C4B\u0C1C\u0C41' p",tomorrow:"'\u0C30\u0C47\u0C2A\u0C41' p",nextWeek:"'\u0C24\u0C26\u0C41\u0C2A\u0C30\u0C3F' eeee p",other:"P"},V=function C(B,G,E,U){return L[B]};function O(C){return function(B,G){var E=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",U;if(E==="formatting"&&C.formattingValues){var X=C.defaultFormattingWidth||C.defaultWidth,Z=G!==null&&G!==void 0&&G.width?String(G.width):X;U=C.formattingValues[Z]||C.formattingValues[X]}else{var A=C.defaultWidth,D=G!==null&&G!==void 0&&G.width?String(G.width):C.defaultWidth;U=C.values[D]||C.values[A]}var I=C.argumentCallback?C.argumentCallback(B):B;return U[I]}}var f={narrow:["\u0C15\u0C4D\u0C30\u0C40.\u0C2A\u0C42.","\u0C15\u0C4D\u0C30\u0C40.\u0C36."],abbreviated:["\u0C15\u0C4D\u0C30\u0C40.\u0C2A\u0C42.","\u0C15\u0C4D\u0C30\u0C40.\u0C36."],wide:["\u0C15\u0C4D\u0C30\u0C40\u0C38\u0C4D\u0C24\u0C41 \u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C02","\u0C15\u0C4D\u0C30\u0C40\u0C38\u0C4D\u0C24\u0C41\u0C36\u0C15\u0C02"]},j={narrow:["1","2","3","4"],abbreviated:["\u0C24\u0C4D\u0C30\u0C481","\u0C24\u0C4D\u0C30\u0C482","\u0C24\u0C4D\u0C30\u0C483","\u0C24\u0C4D\u0C30\u0C484"],wide:["1\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02","2\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02","3\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02","4\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02"]},w={narrow:["\u0C1C","\u0C2B\u0C3F","\u0C2E\u0C3E","\u0C0F","\u0C2E\u0C47","\u0C1C\u0C42","\u0C1C\u0C41","\u0C06","\u0C38\u0C46","\u0C05","\u0C28","\u0C21\u0C3F"],abbreviated:["\u0C1C\u0C28","\u0C2B\u0C3F\u0C2C\u0C4D\u0C30","\u0C2E\u0C3E\u0C30\u0C4D\u0C1A\u0C3F","\u0C0F\u0C2A\u0C4D\u0C30\u0C3F","\u0C2E\u0C47","\u0C1C\u0C42\u0C28\u0C4D","\u0C1C\u0C41\u0C32\u0C48","\u0C06\u0C17","\u0C38\u0C46\u0C2A\u0C4D\u0C1F\u0C46\u0C02","\u0C05\u0C15\u0C4D\u0C1F\u0C4B","\u0C28\u0C35\u0C02","\u0C21\u0C3F\u0C38\u0C46\u0C02"],wide:["\u0C1C\u0C28\u0C35\u0C30\u0C3F","\u0C2B\u0C3F\u0C2C\u0C4D\u0C30\u0C35\u0C30\u0C3F","\u0C2E\u0C3E\u0C30\u0C4D\u0C1A\u0C3F","\u0C0F\u0C2A\u0C4D\u0C30\u0C3F\u0C32\u0C4D","\u0C2E\u0C47","\u0C1C\u0C42\u0C28\u0C4D","\u0C1C\u0C41\u0C32\u0C48","\u0C06\u0C17\u0C38\u0C4D\u0C1F\u0C41","\u0C38\u0C46\u0C2A\u0C4D\u0C1F\u0C46\u0C02\u0C2C\u0C30\u0C4D","\u0C05\u0C15\u0C4D\u0C1F\u0C4B\u0C2C\u0C30\u0C4D","\u0C28\u0C35\u0C02\u0C2C\u0C30\u0C4D","\u0C21\u0C3F\u0C38\u0C46\u0C02\u0C2C\u0C30\u0C4D"]},v={narrow:["\u0C06","\u0C38\u0C4B","\u0C2E","\u0C2C\u0C41","\u0C17\u0C41","\u0C36\u0C41","\u0C36"],short:["\u0C06\u0C26\u0C3F","\u0C38\u0C4B\u0C2E","\u0C2E\u0C02\u0C17\u0C33","\u0C2C\u0C41\u0C27","\u0C17\u0C41\u0C30\u0C41","\u0C36\u0C41\u0C15\u0C4D\u0C30","\u0C36\u0C28\u0C3F"],abbreviated:["\u0C06\u0C26\u0C3F","\u0C38\u0C4B\u0C2E","\u0C2E\u0C02\u0C17\u0C33","\u0C2C\u0C41\u0C27","\u0C17\u0C41\u0C30\u0C41","\u0C36\u0C41\u0C15\u0C4D\u0C30","\u0C36\u0C28\u0C3F"],wide:["\u0C06\u0C26\u0C3F\u0C35\u0C3E\u0C30\u0C02","\u0C38\u0C4B\u0C2E\u0C35\u0C3E\u0C30\u0C02","\u0C2E\u0C02\u0C17\u0C33\u0C35\u0C3E\u0C30\u0C02","\u0C2C\u0C41\u0C27\u0C35\u0C3E\u0C30\u0C02","\u0C17\u0C41\u0C30\u0C41\u0C35\u0C3E\u0C30\u0C02","\u0C36\u0C41\u0C15\u0C4D\u0C30\u0C35\u0C3E\u0C30\u0C02","\u0C36\u0C28\u0C3F\u0C35\u0C3E\u0C30\u0C02"]},P={narrow:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"},abbreviated:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"},wide:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"}},_={narrow:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"},abbreviated:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"},wide:{am:"\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",pm:"\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",midnight:"\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",noon:"\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",morning:"\u0C09\u0C26\u0C2F\u0C02",afternoon:"\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",evening:"\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",night:"\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"}},F=function C(B,G){var E=Number(B);return E+"\u0C35"},u={ordinalNumber:F,era:O({values:f,defaultWidth:"wide"}),quarter:O({values:j,defaultWidth:"wide",argumentCallback:function C(B){return B-1}}),month:O({values:w,defaultWidth:"wide"}),day:O({values:v,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function Q(C){return function(B){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=G.width,U=E&&C.matchPatterns[E]||C.matchPatterns[C.defaultMatchWidth],X=B.match(U);if(!X)return null;var Z=X[0],A=E&&C.parsePatterns[E]||C.parsePatterns[C.defaultParseWidth],D=Array.isArray(A)?k(A,function(N){return N.test(Z)}):b(A,function(N){return N.test(Z)}),I;I=C.valueCallback?C.valueCallback(D):D,I=G.valueCallback?G.valueCallback(I):I;var t=B.slice(Z.length);return{value:I,rest:t}}}var b=function C(B,G){for(var E in B)if(Object.prototype.hasOwnProperty.call(B,E)&&G(B[E]))return E;return},k=function C(B,G){for(var E=0;E<B.length;E++)if(G(B[E]))return E;return};function h(C){return function(B){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=B.match(C.matchPattern);if(!E)return null;var U=E[0],X=B.match(C.parsePattern);if(!X)return null;var Z=C.valueCallback?C.valueCallback(X[0]):X[0];Z=G.valueCallback?G.valueCallback(Z):Z;var A=B.slice(U.length);return{value:Z,rest:A}}}var m=/^(\d+)(వ)?/i,c=/\d+/i,y={narrow:/^(క్రీ\.పూ\.|క్రీ\.శ\.)/i,abbreviated:/^(క్రీ\.?\s?పూ\.?|ప్ర\.?\s?శ\.?\s?పూ\.?|క్రీ\.?\s?శ\.?|సా\.?\s?శ\.?)/i,wide:/^(క్రీస్తు పూర్వం|ప్రస్తుత శకానికి పూర్వం|క్రీస్తు శకం|ప్రస్తుత శకం)/i},p={any:[/^(పూ|శ)/i,/^సా/i]},d={narrow:/^[1234]/i,abbreviated:/^త్రై[1234]/i,wide:/^[1234](వ)? త్రైమాసికం/i},g={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^(జూ|జు|జ|ఫి|మా|ఏ|మే|ఆ|సె|అ|న|డి)/i,abbreviated:/^(జన|ఫిబ్ర|మార్చి|ఏప్రి|మే|జూన్|జులై|ఆగ|సెప్|అక్టో|నవ|డిసె)/i,wide:/^(జనవరి|ఫిబ్రవరి|మార్చి|ఏప్రిల్|మే|జూన్|జులై|ఆగస్టు|సెప్టెంబర్|అక్టోబర్|నవంబర్|డిసెంబర్)/i},i={narrow:[/^జ/i,/^ఫి/i,/^మా/i,/^ఏ/i,/^మే/i,/^జూ/i,/^జు/i,/^ఆ/i,/^సె/i,/^అ/i,/^న/i,/^డి/i],any:[/^జన/i,/^ఫి/i,/^మా/i,/^ఏ/i,/^మే/i,/^జూన్/i,/^జులై/i,/^ఆగ/i,/^సె/i,/^అ/i,/^న/i,/^డి/i]},n={narrow:/^(ఆ|సో|మ|బు|గు|శు|శ)/i,short:/^(ఆది|సోమ|మం|బుధ|గురు|శుక్ర|శని)/i,abbreviated:/^(ఆది|సోమ|మం|బుధ|గురు|శుక్ర|శని)/i,wide:/^(ఆదివారం|సోమవారం|మంగళవారం|బుధవారం|గురువారం|శుక్రవారం|శనివారం)/i},s={narrow:[/^ఆ/i,/^సో/i,/^మ/i,/^బు/i,/^గు/i,/^శు/i,/^శ/i],any:[/^ఆది/i,/^సోమ/i,/^మం/i,/^బుధ/i,/^గురు/i,/^శుక్ర/i,/^శని/i]},o={narrow:/^(పూర్వాహ్నం|అపరాహ్నం|అర్ధరాత్రి|మిట్టమధ్యాహ్నం|ఉదయం|మధ్యాహ్నం|సాయంత్రం|రాత్రి)/i,any:/^(పూర్వాహ్నం|అపరాహ్నం|అర్ధరాత్రి|మిట్టమధ్యాహ్నం|ఉదయం|మధ్యాహ్నం|సాయంత్రం|రాత్రి)/i},r={any:{am:/^పూర్వాహ్నం/i,pm:/^అపరాహ్నం/i,midnight:/^అర్ధ/i,noon:/^మిట్ట/i,morning:/ఉదయం/i,afternoon:/మధ్యాహ్నం/i,evening:/సాయంత్రం/i,night:/రాత్రి/i}},e={ordinalNumber:h({matchPattern:m,parsePattern:c,valueCallback:function C(B){return parseInt(B,10)}}),era:Q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:Q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"te",formatDistance:x,formatLong:R,formatRelative:V,localize:u,match:e,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{te:a})})})();

//# debugId=AB37317B42078DAE64756e2164756e21
