var q=function(I){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},q(I)},z=function(I,H){var Y=Object.keys(I);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(I);H&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(I,x).enumerable})),Y.push.apply(Y,Z)}return Y},W=function(I){for(var H=1;H<arguments.length;H++){var Y=arguments[H]!=null?arguments[H]:{};H%2?z(Object(Y),!0).forEach(function(Z){U1(I,Z,Y[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(I,Object.getOwnPropertyDescriptors(Y)):z(Object(Y)).forEach(function(Z){Object.defineProperty(I,Z,Object.getOwnPropertyDescriptor(Y,Z))})}return I},U1=function(I,H,Y){if(H=B1(H),H in I)Object.defineProperty(I,H,{value:Y,enumerable:!0,configurable:!0,writable:!0});else I[H]=Y;return I},B1=function(I){var H=G1(I,"string");return q(H)=="symbol"?H:String(H)},G1=function(I,H){if(q(I)!="object"||!I)return I;var Y=I[Symbol.toPrimitive];if(Y!==void 0){var Z=Y.call(I,H||"default");if(q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(I)};(function(I){var H=Object.defineProperty,Y=function C(G,U){for(var B in U)H(G,B,{get:U[B],enumerable:!0,configurable:!0,set:function X(J){return U[B]=function(){return J}}})},Z={lessThanXSeconds:{one:{standalone:"manje od 1 sekunde",withPrepositionAgo:"manje od 1 sekunde",withPrepositionIn:"manje od 1 sekundu"},dual:"manje od {{count}} sekunde",other:"manje od {{count}} sekundi"},xSeconds:{one:{standalone:"1 sekunda",withPrepositionAgo:"1 sekunde",withPrepositionIn:"1 sekundu"},dual:"{{count}} sekunde",other:"{{count}} sekundi"},halfAMinute:"pola minute",lessThanXMinutes:{one:{standalone:"manje od 1 minute",withPrepositionAgo:"manje od 1 minute",withPrepositionIn:"manje od 1 minutu"},dual:"manje od {{count}} minute",other:"manje od {{count}} minuta"},xMinutes:{one:{standalone:"1 minuta",withPrepositionAgo:"1 minute",withPrepositionIn:"1 minutu"},dual:"{{count}} minute",other:"{{count}} minuta"},aboutXHours:{one:{standalone:"oko 1 sat",withPrepositionAgo:"oko 1 sat",withPrepositionIn:"oko 1 sat"},dual:"oko {{count}} sata",other:"oko {{count}} sati"},xHours:{one:{standalone:"1 sat",withPrepositionAgo:"1 sat",withPrepositionIn:"1 sat"},dual:"{{count}} sata",other:"{{count}} sati"},xDays:{one:{standalone:"1 dan",withPrepositionAgo:"1 dan",withPrepositionIn:"1 dan"},dual:"{{count}} dana",other:"{{count}} dana"},aboutXWeeks:{one:{standalone:"oko 1 nedelju",withPrepositionAgo:"oko 1 nedelju",withPrepositionIn:"oko 1 nedelju"},dual:"oko {{count}} nedelje",other:"oko {{count}} nedelje"},xWeeks:{one:{standalone:"1 nedelju",withPrepositionAgo:"1 nedelju",withPrepositionIn:"1 nedelju"},dual:"{{count}} nedelje",other:"{{count}} nedelje"},aboutXMonths:{one:{standalone:"oko 1 mesec",withPrepositionAgo:"oko 1 mesec",withPrepositionIn:"oko 1 mesec"},dual:"oko {{count}} meseca",other:"oko {{count}} meseci"},xMonths:{one:{standalone:"1 mesec",withPrepositionAgo:"1 mesec",withPrepositionIn:"1 mesec"},dual:"{{count}} meseca",other:"{{count}} meseci"},aboutXYears:{one:{standalone:"oko 1 godinu",withPrepositionAgo:"oko 1 godinu",withPrepositionIn:"oko 1 godinu"},dual:"oko {{count}} godine",other:"oko {{count}} godina"},xYears:{one:{standalone:"1 godina",withPrepositionAgo:"1 godine",withPrepositionIn:"1 godinu"},dual:"{{count}} godine",other:"{{count}} godina"},overXYears:{one:{standalone:"preko 1 godinu",withPrepositionAgo:"preko 1 godinu",withPrepositionIn:"preko 1 godinu"},dual:"preko {{count}} godine",other:"preko {{count}} godina"},almostXYears:{one:{standalone:"gotovo 1 godinu",withPrepositionAgo:"gotovo 1 godinu",withPrepositionIn:"gotovo 1 godinu"},dual:"gotovo {{count}} godine",other:"gotovo {{count}} godina"}},x=function C(G,U,B){var X,J=Z[G];if(typeof J==="string")X=J;else if(U===1)if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)X=J.one.withPrepositionIn;else X=J.one.withPrepositionAgo;else X=J.one.standalone;else if(U%10>1&&U%10<5&&String(U).substr(-2,1)!=="1")X=J.dual.replace("{{count}}",String(U));else X=J.other.replace("{{count}}",String(U));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"za "+X;else return"pre "+X;return X};function M(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=G.width?String(G.width):C.defaultWidth,B=C.formats[U]||C.formats[C.defaultWidth];return B}}var D={full:"EEEE, d. MMMM yyyy.",long:"d. MMMM yyyy.",medium:"d. MMM yy.",short:"dd. MM. yy."},S={full:"HH:mm:ss (zzzz)",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},$={full:"{{date}} 'u' {{time}}",long:"{{date}} 'u' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:M({formats:D,defaultWidth:"full"}),time:M({formats:S,defaultWidth:"full"}),dateTime:M({formats:$,defaultWidth:"full"})},L={lastWeek:function C(G){switch(G.getDay()){case 0:return"'pro\u0161le nedelje u' p";case 3:return"'pro\u0161le srede u' p";case 6:return"'pro\u0161le subote u' p";default:return"'pro\u0161li' EEEE 'u' p"}},yesterday:"'ju\u010De u' p",today:"'danas u' p",tomorrow:"'sutra u' p",nextWeek:function C(G){switch(G.getDay()){case 0:return"'slede\u0107e nedelje u' p";case 3:return"'slede\u0107u sredu u' p";case 6:return"'slede\u0107u subotu u' p";default:return"'slede\u0107i' EEEE 'u' p"}},other:"P"},j=function C(G,U,B,X){var J=L[G];if(typeof J==="function")return J(U);return J};function O(C){return function(G,U){var B=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",X;if(B==="formatting"&&C.formattingValues){var J=C.defaultFormattingWidth||C.defaultWidth,A=U!==null&&U!==void 0&&U.width?String(U.width):J;X=C.formattingValues[A]||C.formattingValues[J]}else{var E=C.defaultWidth,K=U!==null&&U!==void 0&&U.width?String(U.width):C.defaultWidth;X=C.values[K]||C.values[E]}var T=C.argumentCallback?C.argumentCallback(G):G;return X[T]}}var V={narrow:["pr.n.e.","AD"],abbreviated:["pr. Hr.","po. Hr."],wide:["Pre Hrista","Posle Hrista"]},v={narrow:["1.","2.","3.","4."],abbreviated:["1. kv.","2. kv.","3. kv.","4. kv."],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},f={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["jan","feb","mar","apr","maj","jun","jul","avg","sep","okt","nov","dec"],wide:["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"]},w={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["jan","feb","mar","apr","maj","jun","jul","avg","sep","okt","nov","dec"],wide:["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"]},P={narrow:["N","P","U","S","\u010C","P","S"],short:["ned","pon","uto","sre","\u010Det","pet","sub"],abbreviated:["ned","pon","uto","sre","\u010Det","pet","sub"],wide:["nedelja","ponedeljak","utorak","sreda","\u010Detvrtak","petak","subota"]},_={narrow:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"popodne",evening:"uve\u010De",night:"no\u0107u"},abbreviated:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"popodne",evening:"uve\u010De",night:"no\u0107u"},wide:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"posle podne",evening:"uve\u010De",night:"no\u0107u"}},F={narrow:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"popodne",evening:"uve\u010De",night:"no\u0107u"},abbreviated:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"popodne",evening:"uve\u010De",night:"no\u0107u"},wide:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"posle podne",evening:"uve\u010De",night:"no\u0107u"}},b=function C(G,U){var B=Number(G);return B+"."},h={ordinalNumber:b,era:O({values:V,defaultWidth:"wide"}),quarter:O({values:v,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:O({values:f,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"}),day:O({values:P,defaultWidth:"wide"}),dayPeriod:O({values:F,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function Q(C){return function(G){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=U.width,X=B&&C.matchPatterns[B]||C.matchPatterns[C.defaultMatchWidth],J=G.match(X);if(!J)return null;var A=J[0],E=B&&C.parsePatterns[B]||C.parsePatterns[C.defaultParseWidth],K=Array.isArray(E)?m(E,function(N){return N.test(A)}):k(E,function(N){return N.test(A)}),T;T=C.valueCallback?C.valueCallback(K):K,T=U.valueCallback?U.valueCallback(T):T;var C1=G.slice(A.length);return{value:T,rest:C1}}}var k=function C(G,U){for(var B in G)if(Object.prototype.hasOwnProperty.call(G,B)&&U(G[B]))return B;return},m=function C(G,U){for(var B=0;B<G.length;B++)if(U(G[B]))return B;return};function y(C){return function(G){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=G.match(C.matchPattern);if(!B)return null;var X=B[0],J=G.match(C.parsePattern);if(!J)return null;var A=C.valueCallback?C.valueCallback(J[0]):J[0];A=U.valueCallback?U.valueCallback(A):A;var E=G.slice(X.length);return{value:A,rest:E}}}var c=/^(\d+)\./i,g=/\d+/i,p={narrow:/^(pr\.n\.e\.|AD)/i,abbreviated:/^(pr\.\s?Hr\.|po\.\s?Hr\.)/i,wide:/^(Pre Hrista|pre nove ere|Posle Hrista|nova era)/i},d={any:[/^pr/i,/^(po|nova)/i]},u={narrow:/^[1234]/i,abbreviated:/^[1234]\.\s?kv\.?/i,wide:/^[1234]\. kvartal/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^(10|11|12|[123456789])\./i,abbreviated:/^(jan|feb|mar|apr|maj|jun|jul|avg|sep|okt|nov|dec)/i,wide:/^((januar|januara)|(februar|februara)|(mart|marta)|(april|aprila)|(maj|maja)|(jun|juna)|(jul|jula)|(avgust|avgusta)|(septembar|septembra)|(oktobar|oktobra)|(novembar|novembra)|(decembar|decembra))/i},n={narrow:[/^1/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^7/i,/^8/i,/^9/i,/^10/i,/^11/i,/^12/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^avg/i,/^s/i,/^o/i,/^n/i,/^d/i]},s={narrow:/^[npusčc]/i,short:/^(ned|pon|uto|sre|(čet|cet)|pet|sub)/i,abbreviated:/^(ned|pon|uto|sre|(čet|cet)|pet|sub)/i,wide:/^(nedelja|ponedeljak|utorak|sreda|(četvrtak|cetvrtak)|petak|subota)/i},o={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},r={any:/^(am|pm|ponoc|ponoć|(po)?podne|uvece|uveče|noću|posle podne|ujutru)/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^pono/i,noon:/^pod/i,morning:/jutro/i,afternoon:/(posle\s|po)+podne/i,evening:/(uvece|uveče)/i,night:/(nocu|noću)/i}},a={ordinalNumber:y({matchPattern:c,parsePattern:g,valueCallback:function C(G){return parseInt(G,10)}}),era:Q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:Q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},t={code:"sr-Latn",formatDistance:x,formatLong:R,formatRelative:j,localize:h,match:a,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=W(W({},window.dateFns),{},{locale:W(W({},(I=window.dateFns)===null||I===void 0?void 0:I.locale),{},{srLatn:t})})})();

//# debugId=B1173E39CEB5E17264756e2164756e21
