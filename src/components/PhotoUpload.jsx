import React, { useCallback, useState } from 'react';
import { Upload, Image, X, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

const PhotoUpload = ({ onClose, onUpload }) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const { toast } = useToast();

  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFile = (file) => {
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target?.result);
      };
      reader.readAsDataURL(file);
    } else {
      toast({
        title: "Invalid file type",
        description: "Please select an image file.",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleEnhance = () => {
    if (selectedFile) {
      onUpload(selectedFile);
      toast({
        title: "Enhancement started!",
        description: "Your photo is being enhanced with AI...",
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-white/95 backdrop-blur-lg border-0 shadow-2xl animate-scale-in">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">AI Photo Enhancement</h2>
              <p className="text-gray-600">Upload any photo to make it 20x clearer</p>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Upload Area */}
          {!preview ? (
            <div
              className={`relative border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
                dragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                type="file"
                accept="image/*"
                onChange={handleInputChange}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center mb-4 animate-float">
                  <Upload className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Drop your photo here or click to browse
                </h3>
                <p className="text-gray-500 mb-4">
                  Supports JPG, PNG, WEBP up to 50MB
                </p>
                <Button className="bg-ai-gradient hover:scale-105 transition-transform duration-200">
                  <Image className="w-4 h-4 mr-2" />
                  Choose Photo
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {/* Original Preview */}
                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900">Original</h3>
                  <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100">
                    <img
                      src={preview}
                      alt="Original"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Enhanced Preview Placeholder */}
                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    <Sparkles className="w-4 h-4 mr-2 text-yellow-500" />
                    AI Enhanced (Preview)
                  </h3>
                  <div className="relative aspect-square rounded-lg overflow-hidden bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-dashed border-blue-300 flex items-center justify-center">
                    <div className="text-center">
                      <Sparkles className="w-8 h-8 text-blue-500 mx-auto mb-2 animate-pulse-slow" />
                      <p className="text-sm text-gray-600">Click enhance to see results</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <Button
                  onClick={handleEnhance}
                  className="flex-1 bg-enhance-gradient hover:scale-105 transition-transform duration-200 text-white"
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Enhance with AI (20x Clarity)
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedFile(null);
                    setPreview(null);
                  }}
                >
                  Choose Different Photo
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default PhotoUpload;
