import React from 'react';
import { Image, Clock, Sparkles, TrendingUp } from 'lucide-react';
import { Card } from '@/components/ui/card';

const StatsDashboard = ({ totalPhotos, enhancedPhotos, processingPhotos }) => {
  const stats = [
    {
      label: 'Total Photos',
      value: totalPhotos,
      icon: Image,
      gradient: 'from-blue-500 to-purple-500',
      bgGradient: 'from-blue-50 to-purple-50'
    },
    {
      label: 'Enhanced',
      value: enhancedPhotos,
      icon: Sparkles,
      gradient: 'from-yellow-500 to-orange-500',
      bgGradient: 'from-yellow-50 to-orange-50'
    },
    {
      label: 'Processing',
      value: processingPhotos,
      icon: Clock,
      gradient: 'from-orange-500 to-red-500',
      bgGradient: 'from-orange-50 to-red-50'
    },
    {
      label: 'Quality Boost',
      value: '20x',
      icon: TrendingUp,
      gradient: 'from-green-500 to-emerald-500',
      bgGradient: 'from-green-50 to-emerald-50'
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => {
        const IconComponent = stat.icon;
        return (
          <Card key={index} className="p-6 bg-white hover:shadow-lg transition-shadow duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
              <div className={`w-12 h-12 rounded-full bg-gradient-to-br ${stat.bgGradient} flex items-center justify-center`}>
                <IconComponent className={`w-6 h-6 bg-gradient-to-br ${stat.gradient} bg-clip-text text-transparent`} />
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default StatsDashboard;
