import React, { useState } from 'react';
import { Sparkles, Download, Share2, Eye } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const PhotoGallery = ({ photos, searchQuery }) => {
  const [selectedPhoto, setSelectedPhoto] = useState(null);

  // Filter photos based on search query
  const filteredPhotos = photos.filter(photo =>
    photo.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    photo.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const PhotoCard = ({ photo }) => (
    <Card className="group relative overflow-hidden bg-white hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
      <div className="aspect-square relative overflow-hidden">
        <img
          src={photo.enhanced || photo.original}
          alt={photo.title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
        
        {/* Status Overlay */}
        {photo.status === 'enhancing' && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="text-center text-white">
              <Sparkles className="w-8 h-8 mx-auto mb-2 animate-pulse-slow" />
              <p className="text-sm">Enhancing...</p>
            </div>
          </div>
        )}

        {/* Action Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="secondary"
              className="bg-white/90 hover:bg-white"
              onClick={() => setSelectedPhoto(photo)}
            >
              <Eye className="w-4 h-4" />
            </Button>
            {photo.status === 'enhanced' && (
              <>
                <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                  <Download className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                  <Share2 className="w-4 h-4" />
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Enhancement Badge */}
        {photo.status === 'enhanced' && (
          <Badge className="absolute top-2 right-2 bg-enhance-gradient text-white border-0">
            <Sparkles className="w-3 h-3 mr-1" />
            Enhanced
          </Badge>
        )}
      </div>

      {/* Photo Info */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-1">{photo.title}</h3>
        <p className="text-sm text-gray-500 mb-2">{photo.date}</p>
        <div className="flex flex-wrap gap-1">
          {photo.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {photo.tags.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{photo.tags.length - 3}
            </Badge>
          )}
        </div>
      </div>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {searchQuery ? `Search Results for "${searchQuery}"` : 'Your Photos'}
          </h2>
          <p className="text-gray-600">
            {filteredPhotos.length} {filteredPhotos.length === 1 ? 'photo' : 'photos'}
            {searchQuery && ` found`}
          </p>
        </div>
      </div>

      {/* Photo Grid */}
      {filteredPhotos.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {filteredPhotos.map((photo) => (
            <PhotoCard key={photo.id} photo={photo} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {searchQuery ? 'No photos found' : 'No photos yet'}
          </h3>
          <p className="text-gray-500">
            {searchQuery
              ? 'Try adjusting your search terms'
              : 'Upload your first photo to get started with AI enhancement'}
          </p>
        </div>
      )}

      {/* Photo Modal */}
      {selectedPhoto && (
        <div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedPhoto(null)}
        >
          <div className="max-w-4xl max-h-[90vh] bg-white rounded-lg overflow-hidden" onClick={(e) => e.stopPropagation()}>
            <img
              src={selectedPhoto.enhanced || selectedPhoto.original}
              alt={selectedPhoto.title}
              className="w-full h-auto max-h-[70vh] object-contain"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">{selectedPhoto.title}</h3>
              <p className="text-gray-600 mb-4">{selectedPhoto.date}</p>
              <div className="flex space-x-3">
                <Button className="bg-enhance-gradient">
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
                <Button variant="outline">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PhotoGallery;
