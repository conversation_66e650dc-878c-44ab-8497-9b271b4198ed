import * as React from "react"

import { cn } from "@/lib/utils"

const Table = React.forwardRef
>(({ className, ...props }}
      {...props}
    />
  </div>
))
Table.displayName = "Table"

const TableHeader = React.forwardRef
>(({ className, ...props }} {...props} />
))
TableHeader.displayName = "TableHeader"

const TableBody = React.forwardRef
>(({ className, ...props }}
    {...props}
  />
))
TableBody.displayName = "TableBody"

const TableFooter = React.forwardRef
>(({ className, ...props }}
    {...props}
  />
))
TableFooter.displayName = "TableFooter"

const TableRow = React.forwardRef
>(({ className, ...props }}
    {...props}
  />
))
TableRow.displayName = "TableRow"

const TableHead = React.forwardRef
>(({ className, ...props }]:pr-0",
      className
    )}
    {...props}
  />
))
TableHead.displayName = "TableHead"

const TableCell = React.forwardRef
>(({ className, ...props }]:pr-0", className)}
    {...props}
  />
))
TableCell.displayName = "TableCell"

const TableCaption = React.forwardRef
>(({ className, ...props }}
    {...props}
  />
))
TableCaption.displayName = "TableCaption"

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
