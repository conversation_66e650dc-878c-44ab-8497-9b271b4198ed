import React, { useState } from 'react';
import { Sparkles, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Layout from '@/components/Layout';
import PhotoUpload from '@/components/PhotoUpload';
import PhotoGallery from '@/components/PhotoGallery';
import StatsDashboard from '@/components/StatsDashboard';
import { useToast } from '@/hooks/use-toast';

// Sample data - in a real app this would come from your backend/Supabase
const samplePhotos = [
  {
    id: '1',
    original: 'https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=400',
    enhanced: 'https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=800',
    title: 'Portrait Photography',
    date: '2 hours ago',
    status: 'enhanced',
    tags: ['portrait', 'person', 'professional']
  },
  {
    id: '2',
    original: 'https://images.unsplash.com/photo-1500673922987-e212871fec22?w=400',
    enhanced: 'https://images.unsplash.com/photo-1500673922987-e212871fec22?w=800',
    title: 'Nature Landscape',
    date: '1 day ago',
    status: 'enhanced',
    tags: ['nature', 'landscape', 'water', 'trees']
  },
  {
    id: '3',
    original: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400',
    title: 'Workspace Setup',
    date: '2 days ago',
    status: 'enhancing',
    tags: ['workspace', 'technology', 'computer']
  },
  {
    id: '4',
    original: 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=400',
    enhanced: 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=800',
    title: 'Cat Portrait',
    date: '3 days ago',
    status: 'enhanced',
    tags: ['cat', 'pet', 'animal', 'cute']
  },
  {
    id: '5',
    original: 'https://images.unsplash.com/photo-1518495973542-4542c06a5843?w=400',
    title: 'Sunlight Through Trees',
    date: '1 week ago',
    status: 'original',
    tags: ['sunlight', 'forest', 'nature', 'golden hour']
  },
  {
    id: '6',
    original: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400',
    enhanced: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800',
    title: 'Mountain Vista',
    date: '1 week ago',
    status: 'enhanced',
    tags: ['mountain', 'landscape', 'sunrise', 'scenic']
  }
];

const Index = () => {
  const [showUpload, setShowUpload] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [photos, setPhotos] = useState(samplePhotos);
  const { toast } = useToast();

  const handleUpload = (file) => {
    // Simulate upload and processing
    const newPhoto = {
      id: Date.now().toString(),
      original: URL.createObjectURL(file),
      title: file.name.replace(/\.[^/.]+$/, ""),
      date: 'Just now',
      status: 'enhancing',
      tags: ['uploaded', 'processing']
    };

    setPhotos(prev => [newPhoto, ...prev]);
    setShowUpload(false);

    // Simulate AI processing
    setTimeout(() => {
      setPhotos(prev => prev.map(photo =>
        photo.id === newPhoto.id
          ? { ...photo, status: 'enhanced', enhanced: photo.original, tags: ['enhanced', 'ai-processed'] }
          : photo
      ));
      toast({
        title: "Enhancement Complete!",
        description: "Your photo has been enhanced with 20x clarity.",
      });
    }, 3000);
  };

  const totalPhotos = photos.length;
  const enhancedPhotos = photos.filter(p => p.status === 'enhanced').length;
  const processingPhotos = photos.filter(p => p.status === 'enhancing').length;

  return (
    <Layout
      onUploadClick={() => setShowUpload(true)}
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
    >
      <div className="space-y-8">
        {/* Welcome Section */}
        {photos.length === 0 && !searchQuery && (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-ai-gradient rounded-full flex items-center justify-center mx-auto mb-6 animate-float">
              <Sparkles className="w-12 h-12 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Welcome to BlurBuster AI
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Transform any blurry photo into crystal-clear perfection with our advanced AI enhancement technology.
              Get 20x clarity improvement in seconds.
            </p>
            <Button
              onClick={() => setShowUpload(true)}
              className="bg-enhance-gradient hover:scale-105 transition-transform duration-200 text-white px-8 py-3 text-lg"
            >
              <Upload className="w-5 h-5 mr-2" />
              Upload Your First Photo
            </Button>
          </div>
        )}

        {/* Stats Dashboard */}
        {photos.length > 0 && (
          <StatsDashboard
            totalPhotos={totalPhotos}
            enhancedPhotos={enhancedPhotos}
            processingPhotos={processingPhotos}
          />
        )}

        {/* Photo Gallery */}
        <PhotoGallery photos={photos} searchQuery={searchQuery} />

        {/* Upload Modal */}
        {showUpload && (
          <PhotoUpload
            onClose={() => setShowUpload(false)}
            onUpload={handleUpload}
          />
        )}
      </div>
    </Layout>
  );
};

export default Index;
